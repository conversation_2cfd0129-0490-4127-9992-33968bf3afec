import json
import time
import threading
from datetime import datetime
from collections import deque

import keyboard
import pymem
from cs2_common_lib import (
    CS2ProcessManager, CS2ConfigManager, CS2GameConnector
)
from offset_manager import get_base_offset


class RealtimeRecorder:
    def __init__(self):
        # 配置参数
        self.config = self.load_config()
        # 使用偏移量管理器，不需要手动加载偏移量
        
        # 检测阈值 - 优化后的参数
        self.position_threshold = 0.3  # 降低位置变化阈值，提高灵敏度
        self.angle_threshold = 0.2     # 进一步降低视角变化阈值，提高转向检测敏感度
        self.stable_duration = 0.3     # 缩短稳定状态持续时间
        self.noise_filter = 0.05       # 更严格的噪声过滤
        
        # 录制状态
        self.is_recording = False
        self.is_moving = False
        self.is_turning = False

        # 输入监听状态
        self.input_active = False
        self.last_input_time = 0
        self.input_timeout = 2.0  # 输入停止后2秒停止录制
        self.monitored_keys = ['w', 'a', 's', 'd']
        self.key_states = {key: False for key in self.monitored_keys}

        # 调试模式开关
        self.debug_mode = False  # 关闭调试模式，减少输出

        # 区间容差设置
        self.angle_tolerance = 3.0  # 视角区间容差（±度）
        self.position_tolerance = 5.0  # 位置区间容差（±单位）
        
        # 数据存储
        self.position_history = deque(maxlen=50)   # 减少历史记录长度，提高性能
        self.angle_history = deque(maxlen=50)      
        self.recorded_actions = []                 
        
        # 当前状态
        self.last_stable_time = time.time()
        self.current_action = None
        self.last_movement_action = None
        self.last_turning_action = None
        
        # 游戏连接
        self.pm = None
        self.client_dll = None
        self.player_ptr = None
        self.process_handle = None

    @staticmethod
    def load_config():
        """加载配置文件（使用公共配置管理器）"""
        return CS2ConfigManager.load_config()



    @staticmethod
    def get_cs_windows():
        """获取CS2游戏窗口（使用公共方法库）"""
        return CS2ProcessManager.get_cs_windows()

    def connect_to_game(self):
        """连接到CS2游戏进程（使用公共游戏连接器）"""
        try:
            print("[调试-连接] 开始连接CS2游戏...")

            # 使用公共游戏连接器的窗口连接方法
            game_connector = CS2GameConnector()
            if not game_connector.connect_via_windows():
                print("[错误-连接] 连接失败")
                return False

            # 设置兼容性属性
            self.pm = game_connector.pm
            self.client_dll = game_connector.client_dll
            self.process_handle = game_connector.process_handle

            print(f"[调试-连接] client.dll基址: 0x{self.client_dll:X}")
            print(f"[调试-连接] 进程句柄: {self.process_handle}")
            print("成功连接到CS2进程")
            return True

        except Exception as general_error:
            print(f"[错误-连接] 连接游戏失败: {general_error}")
            print(f"[错误-连接] 错误类型: {type(general_error).__name__}")
            return False

    def setup_input_listeners(self):
        """设置输入监听器"""
        # 设置键盘监听
        for key in self.monitored_keys:
            keyboard.on_press_key(key, self.on_key_press)
            keyboard.on_release_key(key, self.on_key_release)

        print(f"[输入监听] 已设置键盘监听: {', '.join([key.upper() for key in self.monitored_keys])}")

    def on_key_press(self, event):
        """键盘按下事件"""
        key = event.name.lower()
        if key in self.monitored_keys:
            if not self.key_states[key]:  # 避免重复触发
                self.key_states[key] = True
                self.input_active = True
                self.last_input_time = time.time()
                print(f"[输入检测] 按键按下: {key.upper()}")

    def on_key_release(self, event):
        """键盘释放事件"""
        key = event.name.lower()
        if key in self.monitored_keys:
            self.key_states[key] = False
            # 检查是否还有其他键被按下
            if not any(self.key_states.values()):
                print(f"[输入检测] 所有移动键已释放")

    def check_mouse_movement(self):
        """检测鼠标移动（通过视角变化）"""
        if len(self.angle_history) >= 2:
            prev_angle = self.angle_history[-2]['fov_x']
            curr_angle = self.angle_history[-1]['fov_x']

            angle_diff = abs(curr_angle - prev_angle)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff

            # 如果视角变化超过阈值，认为是鼠标移动
            if angle_diff > 0.02:  # 很小的阈值来检测鼠标移动
                self.input_active = True
                self.last_input_time = time.time()
                return True
        return False

    def is_input_active(self):
        """检查当前是否有输入活动"""
        current_time = time.time()

        # 检查键盘状态
        keyboard_active = any(self.key_states.values())

        # 检查输入超时
        time_since_input = current_time - self.last_input_time
        input_timeout = time_since_input < self.input_timeout

        # 如果有键盘输入或在超时时间内，认为输入活跃
        active = keyboard_active or input_timeout

        if not active and self.input_active:
            print(f"[输入检测] 输入活动结束 (超时: {time_since_input:.1f}s)")
            self.input_active = False

            # 强制结束当前动作
            if self.current_action:
                data = self.get_player_data()
                if data:
                    if self.current_action['type'] == 'turning' and self.is_turning:
                        print("[强制结束] 转向动作")
                        self.is_turning = False
                        self.end_turning_action(data)
                    elif self.current_action['type'] == 'movement' and self.is_moving:
                        print("[强制结束] 移动动作")
                        self.is_moving = False
                        self.end_movement_action(data)

        return active

    def get_player_data(self):
        """获取玩家当前位置和视角数据"""
        try:
            # 获取玩家指针（使用偏移量管理器）
            dw_local_player_pawn = get_base_offset("dwLocalPlayerPawn")
            self.player_ptr = self.pm.read_longlong(self.client_dll + dw_local_player_pawn)

            if not self.player_ptr:
                if self.debug_mode:
                    print(f"[调试] 玩家指针为空: {self.player_ptr}")
                return None

            # 读取位置坐标
            x_offset = int(self.config["x_offset"], 16)
            y_offset = int(self.config["y_offset"], 16)
            z_offset = int(self.config["z_offset"], 16)

            x = self.pm.read_float(self.player_ptr + x_offset)
            y = self.pm.read_float(self.player_ptr + y_offset)
            z = self.pm.read_float(self.player_ptr + z_offset)

            # 读取视角（使用偏移量管理器）
            client_dll_offset_x = get_base_offset("dwViewAngles") + 0x4
            view_angle_address = self.client_dll + client_dll_offset_x

            fov_x = pymem.memory.read_float(self.process_handle, view_angle_address)
            
            # 验证数据合理性
            if abs(x) > 10000 or abs(y) > 10000 or abs(z) > 10000:
                print(f"[警告] 坐标数据异常: ({x}, {y}, {z})")
            
            if abs(fov_x) > 360:
                print(f"[警告] 视角数据异常: {fov_x}°")
            
            return {
                'position': {'x': x, 'y': y, 'z': z},
                'angle': {'fov_x': fov_x},
                'timestamp': time.time()
            }
            
        except Exception as data_error:
            print(f"[错误] 读取玩家数据失败: {data_error}")
            print(f"[错误] 详细信息 - 玩家指针: {getattr(self, 'player_ptr', 'None')}")
            print(f"[错误] 详细信息 - 进程句柄: {getattr(self, 'process_handle', 'None')}")
            return None

    def start_recording(self):
        """开始实时录制"""
        if not self.connect_to_game():
            print("无法连接到游戏，录制失败")
            return

        # 设置输入监听
        self.setup_input_listeners()

        self.is_recording = True
        print("开始智能录制...")
        print("按 'q' 键停止录制")
        print("系统将在检测到WASD键盘输入或鼠标移动时开始录制")
        print("支持的按键: W(前进) A(左移) S(后退) D(右移)")
        if self.debug_mode:
            print("调试模式: 已启用")
        print("-" * 50)

        # 启动录制线程
        recording_thread = threading.Thread(target=self.recording_loop)
        recording_thread.daemon = True
        recording_thread.start()

        # 等待用户停止录制
        keyboard.wait('q')
        self.stop_recording()

    def stop_recording(self):
        """停止录制"""
        self.is_recording = False
        print("\n录制已停止")
        
        # 如果有未完成的动作，强制结束
        if self.current_action:
            current_time = time.time()
            if self.current_action['type'] == 'movement':
                # 获取当前位置作为结束位置
                data = self.get_player_data()
                if data:
                    self.current_action['end_position'] = data['position'].copy()
                    self.current_action['end_time'] = current_time
                    self.generate_movement_range(self.current_action)
                    self.recorded_actions.append(self.current_action)
                    print(f"强制结束移动动作: {data['position']}")
            elif self.current_action['type'] == 'turning':
                # 获取当前视角作为结束视角
                data = self.get_player_data()
                if data:
                    self.current_action['end_angle'] = data['angle']['fov_x']
                    self.current_action['end_time'] = current_time
                    self.generate_angle_range(self.current_action)
                    self.recorded_actions.append(self.current_action)
                    print(f"强制结束转向动作: {data['angle']['fov_x']}")
        
        self.save_recorded_data()
        self.generate_code()

    def recording_loop(self):
        """智能录制主循环"""
        print("[录制循环] 等待输入活动...")
        last_status_update = 0

        while self.is_recording:
            try:
                # 获取当前玩家数据
                data = self.get_player_data()
                if data:
                    # 检测鼠标移动
                    self.check_mouse_movement()

                    # 只有在输入活跃时才处理数据
                    if self.is_input_active():
                        self.process_data(data)
                    else:
                        # 输入不活跃时，只更新历史记录用于检测
                        self.position_history.append(data['position'])
                        self.angle_history.append(data['angle'])

                        # 每秒更新一次等待状态显示，避免频繁输出
                        current_time = time.time()
                        if current_time - last_status_update > 1.0:
                            if len(self.recorded_actions) == 0:
                                print(f"\r等待输入... (按WASD键或移动鼠标开始录制)", end="", flush=True)
                            else:
                                print(f"\r等待输入... (已录制: {len(self.recorded_actions)}个动作)", end="", flush=True)
                            last_status_update = current_time

                time.sleep(0.02)  # 50Hz采样频率，降低CPU使用率

            except Exception as loop_error:
                print(f"\n录制循环错误: {loop_error}")
                break

    def process_data(self, data):
        """处理实时数据（仅在输入活跃时调用）"""
        # 添加到历史记录
        self.position_history.append(data['position'])
        self.angle_history.append(data['angle'])

        # 检测状态变化
        self.detect_movement_change(data)
        self.detect_angle_change(data)

        # 更新控制台显示
        self.update_display(data)

    def detect_movement_change(self, data):
        """检测移动状态变化"""
        if len(self.position_history) < 2:
            return
            
        # 计算位置变化
        prev_pos = self.position_history[-2]
        curr_pos = data['position']
        
        distance = ((curr_pos['x'] - prev_pos['x'])**2 + 
                   (curr_pos['y'] - prev_pos['y'])**2)**0.5
        
        current_time = time.time()

        # 检测移动开始
        if not self.is_moving and distance > self.position_threshold:
            self.is_moving = True
            self.last_stable_time = current_time
            self.start_movement_action(data)

        # 检测移动结束
        elif self.is_moving and distance < self.noise_filter:
            stable_time = current_time - self.last_stable_time
            if stable_time > self.stable_duration:
                self.is_moving = False
                self.end_movement_action(data)
        else:
            self.last_stable_time = current_time

    def detect_angle_change(self, data):
        """检测视角变化"""
        if len(self.angle_history) < 2:
            return
            
        # 计算视角变化
        prev_angle = self.angle_history[-2]['fov_x']
        curr_angle = data['angle']['fov_x']
        
        angle_diff = abs(curr_angle - prev_angle)
        # 处理角度跨越问题
        if angle_diff > 180:
            angle_diff = 360 - angle_diff
            
        current_time = time.time()



        # 检测转向开始：任何视角变化都开始转向
        if not self.is_turning and angle_diff > 0.05:  # 很小的阈值，检测任何鼠标移动
            self.is_turning = True
            self.last_stable_time = current_time
            self.start_turning_action(data)

        # 检测转向结束：视角停止变化
        elif self.is_turning:
            if angle_diff < 0.02:  # 视角基本没有变化
                stable_time = current_time - self.last_stable_time
                if stable_time > 0.2:  # 0.2秒没有变化就认为停止
                    self.is_turning = False
                    self.end_turning_action(data)
            else:
                # 还在变化，重置稳定时间
                self.last_stable_time = current_time

    def start_movement_action(self, data):
        """开始移动动作"""
        # 如果有未完成的动作，先结束它
        if self.current_action:
            if self.current_action['type'] == 'movement':
                self.end_movement_action(data)
            elif self.current_action['type'] == 'turning':
                print("[动作切换] 强制结束转向动作，开始移动")
                self.is_turning = False
                self.end_turning_action(data)
            
        self.current_action = {
            'type': 'movement',
            'start_position': data['position'].copy(),
            'start_time': data['timestamp']
        }
        print(f"\n[移动开始] 位置: ({data['position']['x']:.2f}, {data['position']['y']:.2f}, {data['position']['z']:.2f})")

    def end_movement_action(self, data):
        """结束移动动作"""
        if self.current_action and self.current_action['type'] == 'movement':
            self.current_action['end_position'] = data['position'].copy()
            self.current_action['end_time'] = data['timestamp']
            
            # 生成移动区间
            self.generate_movement_range(self.current_action)
            self.recorded_actions.append(self.current_action)
            
            print(f"\n[移动结束] 位置: ({data['position']['x']:.2f}, {data['position']['y']:.2f}, {data['position']['z']:.2f})")
            self.print_movement_summary(self.current_action)
            self.current_action = None

    def start_turning_action(self, data):
        """开始转向动作"""
        # 如果有未完成的动作，先结束它
        if self.current_action:
            if self.current_action['type'] == 'turning':
                self.end_turning_action(data)
            elif self.current_action['type'] == 'movement':
                print("[动作切换] 强制结束移动动作，开始转向")
                self.is_moving = False
                self.end_movement_action(data)
            
        self.current_action = {
            'type': 'turning',
            'start_angle': data['angle']['fov_x'],
            'start_time': data['timestamp']
        }
        print(f"\n[转向开始] 视角: {data['angle']['fov_x']:.2f}°")

    def end_turning_action(self, data):
        """结束转向动作"""
        if self.current_action and self.current_action['type'] == 'turning':
            self.current_action['end_angle'] = data['angle']['fov_x']
            self.current_action['end_time'] = data['timestamp']
            
            # 生成视角区间
            self.generate_angle_range(self.current_action)
            self.recorded_actions.append(self.current_action)
            
            print(f"\n[转向结束] 视角: {data['angle']['fov_x']:.2f}°")
            self.print_turning_summary(self.current_action)
            self.current_action = None

    def generate_movement_range(self, action):
        """生成移动区间参数 - 基于结束位置设置小范围区间，只选择主要移动轴"""
        start_pos = action['start_position']
        end_pos = action['end_position']

        # 使用可配置的容差
        tolerance = self.position_tolerance

        # 计算各轴的移动距离
        dx = abs(end_pos['x'] - start_pos['x'])
        dy = abs(end_pos['y'] - start_pos['y'])
        dz = abs(end_pos['z'] - start_pos['z'])

        ranges = {}

        # 找到主要移动轴（移动距离最大的轴）
        max_distance = max(dx, dy, dz)

        # 只为主要移动轴生成区间（移动距离大于1单位且是最大移动距离）
        if dx >= 1.0 and dx == max_distance:
            ranges['x_range'] = (
                round(end_pos['x'] - tolerance),
                round(end_pos['x'] + tolerance)
            )
        elif dy >= 1.0 and dy == max_distance:
            ranges['y_range'] = (
                round(end_pos['y'] - tolerance),
                round(end_pos['y'] + tolerance)
            )
        elif dz >= 1.0 and dz == max_distance:
            ranges['z_range'] = (
                round(end_pos['z'] - tolerance),
                round(end_pos['z'] + tolerance)
            )

        action['ranges'] = ranges

    def generate_angle_range(self, action):
        """生成视角区间参数 - 基于结束视角设置小范围区间"""
        end_angle = action['end_angle']

        # 使用可配置的容差
        tolerance = self.angle_tolerance

        # 基于结束视角设置小区间
        min_angle = end_angle - tolerance
        max_angle = end_angle + tolerance

        # 归一化到-180到180范围
        while min_angle > 180:
            min_angle -= 360
        while min_angle < -180:
            min_angle += 360
        while max_angle > 180:
            max_angle -= 360
        while max_angle < -180:
            max_angle += 360

        # 转换为整数
        action['fov_range'] = (round(min_angle), round(max_angle))

    @staticmethod
    def print_movement_summary(action):
        """打印移动动作摘要"""
        ranges = action.get('ranges', {})
        if ranges:
            for axis, range_val in ranges.items():
                print(f"  移动区间 {axis}: ({range_val[0]:.1f}, {range_val[1]:.1f})")

    @staticmethod
    def print_turning_summary(action):
        """打印转向动作摘要"""
        fov_range = action.get('fov_range')
        if fov_range:
            print(f"  转向区间: ({fov_range[0]:.1f}°, {fov_range[1]:.1f}°)")

    def update_display(self, data):
        """更新控制台显示"""
        pos = data['position']
        angle = data['angle']['fov_x']

        status = []
        if self.is_moving:
            status.append("移动中")
        if self.is_turning:
            status.append("转向中")
        if not status:
            status.append("静止")

        # 显示当前按键状态
        active_keys = [key.upper() for key, pressed in self.key_states.items() if pressed]
        key_status = f"按键: {','.join(active_keys) if active_keys else '无'}"

        print(f"\r位置: ({pos['x']:.2f}, {pos['y']:.2f}, {pos['z']:.2f}) | "
              f"视角: {angle:.2f}° | 状态: {', '.join(status)} | "
              f"{key_status} | 已录制: {len(self.recorded_actions)}个动作", end="")

    def save_recorded_data(self):
        """保存录制数据 - 已简化，只显示统计信息"""
        # timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        # filename = f"recorded_paths_{timestamp}.json"
        #
        # # 添加录制会话信息
        # session_data = {
        #     'session_info': {
        #         'timestamp': timestamp,
        #         'total_actions': len(self.recorded_actions),
        #         'recording_settings': {
        #             'position_threshold': self.position_threshold,
        #             'angle_threshold': self.angle_threshold,
        #             'stable_duration': self.stable_duration,
        #             'noise_filter': self.noise_filter
        #         }
        #     },
        #     'actions': self.recorded_actions
        # }
        #
        # with open(filename, 'w', encoding='utf-8') as f:
        #     json.dump(session_data, f, indent=2, ensure_ascii=False)
        #
        # print(f"\n录制数据已保存到: {filename}")
        print(f"\n总共录制了 {len(self.recorded_actions)} 个动作")

    def generate_code(self):
        """生成可执行代码和路径配置"""
        if not self.recorded_actions:
            print("没有录制到任何动作")
            return

        # 生成传统的Python代码
        code_lines = [
            "# 自动生成的移动代码",
            "# 生成时间: " + datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            f"# 总动作数: {len(self.recorded_actions)}",
            "",
            "def execute_recorded_path(pid, pm, player_ptr, x_offset, y_offset, z_offset, ProcessHandle, client_dll, client_dll_offset_x):",
            '    """执行录制的路径"""'
        ]

        # 生成路径配置数据
        path_config = {
            "name": f"录制路径_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "description": f"自动录制生成的路径，包含{len(self.recorded_actions)}个动作",
            "actions": []
        }
        
        movement_count = 0
        turning_count = 0
        
        for action in self.recorded_actions:
            if action['type'] == 'movement':
                movement_count += 1
                ranges = action.get('ranges', {})
                params = []
                
                if 'x_range' in ranges:
                    params.append(f"x_range=({ranges['x_range'][0]:.1f}, {ranges['x_range'][1]:.1f})")
                if 'y_range' in ranges:
                    params.append(f"y_range=({ranges['y_range'][0]:.1f}, {ranges['y_range'][1]:.1f})")
                if 'z_range' in ranges:
                    params.append(f"z_range=({ranges['z_range'][0]:.1f}, {ranges['z_range'][1]:.1f})")
                
                code_lines.append(f"    # 移动动作 {movement_count}")
                if params:
                    code_lines.append(f"    if not move_forward_to_target(pid, pm, player_ptr, x_offset, y_offset, z_offset,")
                    code_lines.append(f"                                   {', '.join(params)}):")
                    code_lines.append(f"        return")

                    # 添加到路径配置
                    config_action = {"type": "movement"}
                    if 'x_range' in ranges:
                        config_action['x_range'] = ranges['x_range']
                    if 'y_range' in ranges:
                        config_action['y_range'] = ranges['y_range']
                    if 'z_range' in ranges:
                        config_action['z_range'] = ranges['z_range']
                    path_config['actions'].append(config_action)
                else:
                    code_lines.append(f"    # 警告: 移动距离过小，未生成有效区间")
                
            elif action['type'] == 'turning':
                turning_count += 1
                fov_range = action.get('fov_range')
                code_lines.append(f"    # 转向动作 {turning_count}")
                if fov_range:
                    code_lines.append(f"    x_turn_view_to_target(({fov_range[0]}, {fov_range[1]}), ProcessHandle, client_dll, client_dll_offset_x)")
                    # 添加到路径配置
                    path_config['actions'].append({
                        "type": "turning",
                        "fov_range": list(fov_range)
                    })
                else:
                    code_lines.append(f"    # 警告: 转向角度过小，未生成有效区间")

            code_lines.append("")

        code_lines.append("")
        code_lines.append("# 使用示例:")
        code_lines.append("# success = execute_recorded_path(pid, pm, player_ptr, x_offset, y_offset, z_offset, ProcessHandle, client_dll, client_dll_offset_x)")
        
        # 保存生成的代码 - 已注释，只保留path_config文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        # filename = f"generated_code_{timestamp}.py"
        #
        # with open(filename, 'w', encoding='utf-8') as f:
        #     f.write('\n'.join(code_lines))

        # 保存路径配置文件 - 保留这个
        config_filename = f"path_config_{timestamp}.json"
        with open(config_filename, 'w', encoding='utf-8') as f:
            json.dump(path_config, f, indent=2, ensure_ascii=False)

        # print(f"生成的代码已保存到: {filename}")
        print(f"路径配置已保存到: {config_filename}")
        print(f"包含 {movement_count} 个移动动作和 {turning_count} 个转向动作")

        # 生成paths.json格式 - 已注释
        # self.generate_paths_json_format()

        # print("\n生成的代码预览:")
        # print("-" * 50)
        # for line in code_lines[:25]:  # 显示前25行
        #     print(line)
        # if len(code_lines) > 25:
        #     print("...")

    def generate_paths_json_format(self):
        """生成paths.json可用的actions格式"""
        if not self.recorded_actions:
            print("没有录制到任何动作")
            return

        # 生成actions数组
        actions = []

        for action in self.recorded_actions:
            if action['type'] == 'movement':
                ranges = action.get('ranges', {})
                if ranges:  # 只有有效的移动才添加
                    config_action = {"type": "movement"}
                    if 'x_range' in ranges:
                        config_action['x_range'] = [int(ranges['x_range'][0]), int(ranges['x_range'][1])]
                    if 'y_range' in ranges:
                        config_action['y_range'] = [int(ranges['y_range'][0]), int(ranges['y_range'][1])]
                    if 'z_range' in ranges:
                        config_action['z_range'] = [int(ranges['z_range'][0]), int(ranges['z_range'][1])]
                    actions.append(config_action)

            elif action['type'] == 'turning':
                fov_range = action.get('fov_range')
                if fov_range:  # 只有有效的转向才添加
                    actions.append({
                        "type": "turning",
                        "fov_range": [int(fov_range[0]), int(fov_range[1])]
                    })

        if not actions:
            print("没有生成有效的动作")
            return

        # 生成单个路线的JSON格式
        route_data = {
            "name": f"录制路线_{datetime.now().strftime('%H%M%S')}",
            "description": f"自动录制生成，包含{len(actions)}个动作",
            "actions": actions
        }

        route_json = json.dumps(route_data, indent=10, ensure_ascii=False)

        # 保存到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"paths_json_route_{timestamp}.txt"

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("// 可直接复制到paths.json中的route对象\n")
            f.write("// 使用方法：复制下面的内容，替换paths.json中对应waypoint的routes数组中的某个路线\n\n")
            f.write(route_json)

        print(f"\n" + "="*60)
        print(" paths.json路线格式已生成！")
        print(f" 文件保存到: {filename}")
        print(f" 包含 {len(actions)} 个有效动作")
        print("="*60)
        print("\n 可直接复制的route对象:")
        print("-" * 40)
        print(route_json)
        print("-" * 40)
        print("\n 使用说明:")
        print("1. 复制上面的route对象内容")
        print("2. 打开paths.json文件")
        print("3. 找到对应的waypoint的routes数组")
        print("4. 替换其中一个路线对象（路线1、2或3）")
        print("5. 保存文件即可使用")
        print("\n 示例替换位置:")
        print('在routes数组中找到: {"name": "路线1-快速突破", "description": "...", "actions": []}')
        print("替换为录制的路线内容")


def test_range_generation():
    """测试新的区间生成逻辑"""
    print("测试区间生成逻辑:")

    # 测试移动区间
    movement_action = {
        'start_position': {'x': 100, 'y': 200, 'z': 300},
        'end_position': {'x': 1000, 'y': -500, 'z': 305}  # X轴大幅移动，Y轴大幅移动，Z轴小幅移动
    }
    recorder = RealtimeRecorder()
    recorder.generate_movement_range(movement_action)
    print(f"移动区间: {movement_action.get('ranges', {})}")

    # 测试视角区间 - 基于结束视角
    turning_action = {
        'start_angle': 45.0,
        'end_angle': 90.0  # 结束在90度，区间应该是(85, 95)
    }
    recorder.generate_angle_range(turning_action)
    print(f"视角区间 (结束在90°): {turning_action.get('fov_range', ())}")

    # 测试跨零角度 - 基于结束视角
    turning_action2 = {
        'start_angle': 170.0,
        'end_angle': -170.0  # 结束在-170度，区间应该是(-175, -165)
    }
    recorder.generate_angle_range(turning_action2)
    print(f"跨零视角区间 (结束在-170°): {turning_action2.get('fov_range', ())}")


if __name__ == "__main__":
    # 取消注释下面这行来测试区间生成
    # test_range_generation()
    # print("=" * 50)


    try:
        recorder = RealtimeRecorder()
        recorder.start_recording()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as runtime_error:
        print(f"\n程序运行错误: {runtime_error}")

