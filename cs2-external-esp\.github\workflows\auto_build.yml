name: Build Project

on:
  workflow_dispatch:
  push:
    branches:
      - main
    #   - auto-build # testing
    paths-ignore:
      - '**/*.json'

jobs:
  build:
    runs-on: windows-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Visual Studio
      uses: microsoft/setup-msbuild@v1
        
    # - name: Install vcpkg
    #   run: |
    #      git clone https://github.com/microsoft/vcpkg.git
    #      .\vcpkg\bootstrap-vcpkg.bat
 
    # - name: Install dependencies
    #   run: .\vcpkg\vcpkg.exe install xxxx:x64-windows-static

    # - name: Set VCPKG_ROOT
    #   run: echo "VCPKG_ROOT=$PWD\vcpkg" >> $GITHUB_ENV
 
    # - name: Integrate vcpkg with MSBuild
    #   run: .\vcpkg\vcpkg.exe integrate install

    - name: Build solution
      env:
        VCPKG_ROOT: ${{ env.VCPKG_ROOT }}
      run: msbuild cs2-external-esp.sln /p:Configuration=Release

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-artifacts
        path: |
          x64/Release/*