import json
import time

import keyboard
import psutil
import pymem
import win32con
import win32gui
import win32process
import Waypoints
with open("config.json", "r") as f:
    config = json.load(f)
with open("offsets.json", "r") as f:
    offsets_client_dll = json.load(f)["client.dll"]
client_dll_offset_human = offsets_client_dll["dwEntityList"]
client_dll_offset_y = offsets_client_dll["dwViewAngles"]
client_dll_offset_x = offsets_client_dll["dwViewAngles"]+ 0x4
dw_local_player_pawn = offsets_client_dll["dwLocalPlayerPawn"]
x_offset = int(config["x_offset"], 16)
y_offset = int(config["y_offset"], 16)
z_offset = int(config["z_offset"], 16)
blood_offset = int(config["blood_offset"], 16)

def get_cs_windows():
    """获取真正的CS2游戏主窗口（排除无标题的窗口和子窗口）"""
    cs_windows = []
    cs2_pids = []
    # 获取所有cs2.exe进程ID
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            if proc.info['name'].lower() == 'cs2.exe':
                cs2_pids.append(proc.info['pid'])
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue

    if not cs2_pids:
        print("未找到运行中的cs2.exe进程")
        return None

    def enum_windows_callback(hwnd, _):
        # 只处理可见窗口
        if not win32gui.IsWindowVisible(hwnd):
            return

        # 获取窗口所属进程ID
        _, pid = win32process.GetWindowThreadProcessId(hwnd)

        # 检查是否是cs2.exe进程
        if pid not in cs2_pids:
            return

        # 获取窗口标题（过滤掉空标题的窗口）
        window_title = win32gui.GetWindowText(hwnd)
        if not window_title.strip():  # 跳过空标题窗口
            return

        # 检查窗口样式（过滤掉子窗口）
        style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
        if not (style & win32con.WS_OVERLAPPEDWINDOW):  # 跳过非标准窗口
            return

        # 获取窗口坐标 (左上角和右下角坐标)
        left, top, right, bottom = win32gui.GetWindowRect(hwnd)
        # 计算坐标和
        window_sum = left + top + right + bottom
        cs_windows.append((hwnd, window_title))  # 只保留hwnd和window_title

    # 枚举窗口
    win32gui.EnumWindows(enum_windows_callback, None)

    # 按窗口的 top 坐标排序（从上到下）
    cs_windows.sort(
        key=lambda x: (win32gui.GetWindowRect(x[0])[1], win32gui.GetWindowRect(x[0])[0]))  # 使用GetWindowRect获取坐标进行排序
    return cs_windows
def move_forward_to_target(pid, pm, player_ptr, x_offset, y_offset, z_offset,
                           x_range=None, y_range=None, z_range=None):
    # 自动排好区间
    if x_range:
        x_range = sorted(x_range)
    if y_range:
        y_range = sorted(y_range)
    if z_range:
        z_range = sorted(z_range)

    while True:
        my_x = pm.read_float(player_ptr + x_offset)
        my_y = pm.read_float(player_ptr + y_offset)
        my_z = pm.read_float(player_ptr + z_offset)

        in_x = x_range and (x_range[0] <= my_x <= x_range[1])
        in_y = y_range and (y_range[0] <= my_y <= y_range[1])
        in_z = z_range and (z_range[0] <= my_z <= z_range[1])

        if (x_range is None or in_x) and (y_range is None or in_y) and (z_range is None or in_z):
            keyboard.release('w')
            break

        keyboard.press('w')

        time.sleep(0.005)

    keyboard.release('w')
    print(my_y)
    return True
def x_turn_view_to_target(fov_range, ProcessHandle, client_dll, client_dll_offset_x):
    min_fov, max_fov = sorted(fov_range)
    span = max_fov - min_fov

    keyboard.press('q')
    try:
        while True:
            fov_x = pymem.memory.read_float(ProcessHandle, client_dll + client_dll_offset_x)

            if span < 350:
                inside = (min_fov <= fov_x <= max_fov)
            else:
                inside = (fov_x >= max_fov) or (fov_x <= min_fov)

            if inside:
                keyboard.release('q')
                break

            keyboard.press('q')
            time.sleep(0.005)
    finally:
        keyboard.release('q')
def is_at_position(my_x, my_y, target, tolerance=1):
    return (abs(my_x - target['x']) <= tolerance and
            abs(my_y - target['y']) <= tolerance)
def path_test():
    cs_windows = get_cs_windows()
    first_cs_windows_hwnd = cs_windows[0][0]
    win32gui.ShowWindow(first_cs_windows_hwnd, win32con.SW_RESTORE)
    win32gui.SetForegroundWindow(first_cs_windows_hwnd)
    _, pid = win32process.GetWindowThreadProcessId(first_cs_windows_hwnd)
    pm = pymem.Pymem()
    pm.open_process_from_id(pid)
    client_dll = pymem.process.module_from_name(
        pm.process_handle, "client.dll"
    ).lpBaseOfDll
    player_ptr = pm.read_longlong(client_dll + dw_local_player_pawn)
    my_x = pm.read_float(player_ptr + x_offset)
    my_y = pm.read_float(player_ptr + y_offset)
    ProcessHandle = pm.process_handle
    at_known_pos = False  # 是否匹配已知路点
    # 判断是否在某个路点，转对应视角
    for Mirage in Waypoints.MirageWaypoints:
        if is_at_position(my_x, my_y, Mirage):
            x_turn_view_to_target(Mirage['fov_range'], ProcessHandle, client_dll, client_dll_offset_x)
            at_known_pos = True
            break  # 转好视角就继续下一轮
    time.sleep(0.1)
    # 如果当前位置不在已知列表中，添加进去（fov_range 留空）
    if not at_known_pos:
        new_wp = {'x': round(my_x, 2), 'y': round(my_y, 2), 'fov_range': ()}
        Waypoints.MirageWaypoints.append(new_wp)
        print(f"未知坐标点: {new_wp}")
        return

    # 转向动作 1
    x_turn_view_to_target((-18.0, -8.0), ProcessHandle, client_dll, client_dll_offset_x)

    # 移动动作 1
    if not move_forward_to_target(pid, pm, player_ptr, x_offset, y_offset, z_offset,
                                  x_range=(1326.0, 1346.0)):
        return

    # 转向动作 2
    x_turn_view_to_target((-92.0, -82.0), ProcessHandle, client_dll, client_dll_offset_x)

    # 移动动作 2
    if not move_forward_to_target(pid, pm, player_ptr, x_offset, y_offset, z_offset,
                                  y_range=(-473.0, -453.0)):
        return

    # 转向动作 3
    x_turn_view_to_target((-139.0, -129.0), ProcessHandle, client_dll, client_dll_offset_x)

    # 移动动作 3
    if not move_forward_to_target(pid, pm, player_ptr, x_offset, y_offset, z_offset,
                                  y_range=(-673.0, -653.0)):
        return

    # 转向动作 4
    x_turn_view_to_target((-101.0, -91.0), ProcessHandle, client_dll, client_dll_offset_x)

    # 移动动作 4
    if not move_forward_to_target(pid, pm, player_ptr, x_offset, y_offset, z_offset,
                                  y_range=(-1075.0, -1055.0)):
        return

    # 转向动作 5
    x_turn_view_to_target((-176.0, -166.0), ProcessHandle, client_dll, client_dll_offset_x)

    # 移动动作 5
    if not move_forward_to_target(pid, pm, player_ptr, x_offset, y_offset, z_offset,
                                  x_range=(733.0, 753.0)):
        return

    # 转向动作 6
    x_turn_view_to_target((-114.0, -104.0), ProcessHandle, client_dll, client_dll_offset_x)

    # 移动动作 6
    if not move_forward_to_target(pid, pm, player_ptr, x_offset, y_offset, z_offset,
                                  y_range=(-1588.0, -1568.0)):
        return

    # 转向动作 7
    x_turn_view_to_target((162.0, 172.0), ProcessHandle, client_dll, client_dll_offset_x)

    # 移动动作 7
    if not move_forward_to_target(pid, pm, player_ptr, x_offset, y_offset, z_offset,
                                  x_range=(78.0, 98.0)):
        return

    # 转向动作 8
    x_turn_view_to_target((-130.0, -120.0), ProcessHandle, client_dll, client_dll_offset_x)

    # 移动动作 8
    if not move_forward_to_target(pid, pm, player_ptr, x_offset, y_offset, z_offset,
                                  y_range=(-1859.0, -1839.0)):
        return



if __name__ == '__main__':
    path_test()