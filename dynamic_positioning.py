#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态站位调整
"""

import time
import keyboard
from front_detection_improved import FrontDetectorImproved


class DynamicPositioning:
    """动态站位调整器"""
    
    def __init__(self):
        # 前方检测器
        self.front_detector = FrontDetectorImproved()

        # 站位调整参数
        self.safe_distance = 80.0      # 安全距离（游戏单位）
        self.adjustment_step = 30.0    # 每次调整的步长
        self.max_adjustments = 5       # 最大调整次数
        self.adjustment_timeout = 10.0 # 调整超时时间
    
    def connect_to_game(self):
        """连接到游戏"""
        try:
            # 连接前方检测器
            if not self.front_detector.connect_to_game():
                return False

            print(" 动态站位系统已连接")
            return True

        except Exception as e:
            print(f" 连接失败: {e}")
            return False
    
    def get_current_position(self):
        """获取当前位置"""
        if not self.front_detector.update_local_player():
            return None
        return (self.front_detector.local_origin.x,
                self.front_detector.local_origin.y,
                self.front_detector.local_origin.z)

    def get_teammates_positions(self):
        """获取队友位置列表"""
        teammates = self.front_detector.get_all_players()
        return [{'name': t['name'],
                'position': (t['origin'].x, t['origin'].y, t['origin'].z),
                'distance': t['distance_2d']} for t in teammates]
    

    
    def execute_movement_adjustment(self, adjustment_type):
        """执行移动调整"""
        movement_time = 0.5  # 移动时间

        print(f" 执行调整: {adjustment_type}")

        if adjustment_type == 'move_left':
            keyboard.press('a')
            time.sleep(movement_time)
            keyboard.release('a')
        elif adjustment_type == 'move_right':
            keyboard.press('d')
            time.sleep(movement_time)
            keyboard.release('d')
        elif adjustment_type == 'move_front':
            keyboard.press('w')
            time.sleep(movement_time)
            keyboard.release('w')
        elif adjustment_type == 'move_back':
            keyboard.press('s')
            time.sleep(movement_time)
            keyboard.release('s')

        time.sleep(0.2)  # 等待位置更新
    
    def adjust_position_for_teammates(self):
        """根据队友位置调整站位（使用前方检测逻辑）"""
        print(" 开始动态站位调整...")

        start_time = time.time()
        adjustment_count = 0

        while (time.time() - start_time < self.adjustment_timeout and
               adjustment_count < self.max_adjustments):

            # 使用前方检测器检测前方队友
            front_teammates = self.front_detector.detect_front_teammates()

            if not front_teammates:
                print(" 前方无队友遮挡，无需调整")
                break

            # 检查是否有遮挡
            blocking_teammates = [t for t in front_teammates if t['is_blocking']]

            if not blocking_teammates:
                print(" 前方队友距离安全，无需调整")
                break

            # 显示遮挡信息
            closest_blocking = blocking_teammates[0]
            print(f" 前方队友遮挡: {closest_blocking['name']} "
                  f"距离:{closest_blocking['distance_2d']:.1f}m "
                  f"角度:{closest_blocking['angle_diff']:.1f}°")

            # 根据角度差决定调整方向
            angle_diff = closest_blocking['angle_diff']

            if abs(angle_diff) < 10:  # 正前方，左右移动
                if angle_diff >= 0:
                    adjustment = 'move_left'  # 队友在右前方，向左移动
                else:
                    adjustment = 'move_right'  # 队友在左前方，向右移动
            elif angle_diff > 0:
                adjustment = 'move_left'  # 队友在右侧，向左移动
            else:
                adjustment = 'move_right'  # 队友在左侧，向右移动

            print(f" 执行调整: {adjustment}")

            # 执行调整
            self.execute_movement_adjustment(adjustment)
            adjustment_count += 1

            print(f" 调整进度: {adjustment_count}/{self.max_adjustments}")
            time.sleep(0.5)  # 等待移动完成

        # 最终检查
        final_front_teammates = self.front_detector.detect_front_teammates()
        final_blocking = [t for t in final_front_teammates if t['is_blocking']]

        if not final_blocking:
            print(" 动态站位调整完成，前方无队友遮挡")
        else:
            print(f" 仍有 {len(final_blocking)} 个队友遮挡，但已达到调整限制")

        return True


def main():
    """测试动态站位调整功能"""
    print(" CS2 动态站位调整系统 - 基于前方检测")
    print("=" * 50)

    positioning = DynamicPositioning()

    if not positioning.connect_to_game():
        print(" 连接失败")
        return

    print(" 系统已连接，开始动态站位调整...")

    # 执行动态调整
    positioning.adjust_position_for_teammates()

    print("\n 调整完成！")


if __name__ == "__main__":
    main()
