#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
偏移管理器
"""

import json
import os


class OffsetManager:

    
    def __init__(self):
        self.base_offsets = {}      # 基础偏移量 (offsets.json)
        self.entity_offsets = {}    # 实体偏移量 (client_dll.json)
        self.loaded = False
        
        # 加载偏移量
        self.load_offsets()
    
    def load_offsets(self):
        """加载所有偏移量"""
        try:
            # 加载基础偏移量
            self._load_base_offsets()
            
            # 加载实体偏移量
            self._load_entity_offsets()
            
            self.loaded = True
            print(" 偏移量管理器加载成功")
            
        except Exception as e:
            print(f" 偏移量管理器加载失败: {e}")
            self.loaded = False
    
    def _load_base_offsets(self):
        """加载基础偏移量 (offsets.json)"""
        if not os.path.exists("offsets.json"):
            raise FileNotFoundError("未找到 offsets.json 文件")
        
        with open("offsets.json", "r", encoding="utf-8") as f:
            data = json.load(f)
            self.base_offsets = data["client.dll"]
    
    def _load_entity_offsets(self):
        """加载实体偏移量 (client_dll.json)"""
        if not os.path.exists("client_dll.json"):
            raise FileNotFoundError("未找到 client_dll.json 文件")
        
        with open("client_dll.json", "r", encoding="utf-8") as f:
            data = json.load(f)
            classes = data["client.dll"]["classes"]
            
            # 提取常用的偏移量
            self.entity_offsets = {
                # 玩家控制器相关
                "m_hPlayerPawn": self._get_field_offset(classes, "CCSPlayerController", "m_hPlayerPawn"),
                "m_hObserverPawn": self._get_field_offset(classes, "CCSPlayerController", "m_hObserverPawn"),
                "m_iszPlayerName": self._get_field_offset(classes, "CBasePlayerController", "m_iszPlayerName"),
                "m_iTeamNum": self._get_field_offset(classes, "C_BaseEntity", "m_iTeamNum"),
                "m_iHealth": self._get_field_offset(classes, "C_BaseEntity", "m_iHealth"),
                "m_lifeState": self._get_field_offset(classes, "C_BaseEntity", "m_lifeState"),
                "m_fFlags": self._get_field_offset(classes, "C_BaseEntity", "m_fFlags"),
                
                # 玩家Pawn相关
                "m_vOldOrigin": self._get_field_offset(classes, "C_BasePlayerPawn", "m_vOldOrigin"),
                "m_hController": self._get_field_offset(classes, "C_BasePlayerPawn", "m_hController"),
                "m_pGameSceneNode": self._get_field_offset(classes, "C_BaseEntity", "m_pGameSceneNode"),
                
                # CS玩家Pawn相关
                "m_vecViewOffset": self._get_field_offset(classes, "C_BaseModelEntity", "m_vecViewOffset"),
                "m_aimPunchAngle": self._get_field_offset(classes, "C_CSPlayerPawn", "m_aimPunchAngle"),
                "m_ArmorValue": self._get_field_offset(classes, "C_CSPlayerPawn", "m_ArmorValue"),
                "m_bHasDefuser": self._get_field_offset(classes, "CCSPlayer_ItemServices", "m_bHasDefuser"),
                "m_bHasHelmet": self._get_field_offset(classes, "CCSPlayer_ItemServices", "m_bHasHelmet"),
                "m_bIsScoped": self._get_field_offset(classes, "C_CSPlayerPawn", "m_bIsScoped"),
                "m_bIsDefusing": self._get_field_offset(classes, "C_CSPlayerPawn", "m_bIsDefusing"),
                "m_bIsGrabbingHostage": self._get_field_offset(classes, "C_CSPlayerPawn", "m_bIsGrabbingHostage"),
                "m_iShotsFired": self._get_field_offset(classes, "C_CSPlayerPawn", "m_iShotsFired"),
                "m_flFlashOverlayAlpha": self._get_field_offset(classes, "C_CSPlayerPawn", "m_flFlashOverlayAlpha"),
                
                # 武器相关
                "m_pClippingWeapon": self._get_field_offset(classes, "C_CSPlayerPawnBase", "m_pClippingWeapon"),
                "m_pWeaponServices": self._get_field_offset(classes, "C_BasePlayerPawn", "m_pWeaponServices"),
                
                # 经济相关
                "m_pInGameMoneyServices": self._get_field_offset(classes, "CCSPlayerController", "m_pInGameMoneyServices"),
                "m_iAccount": self._get_field_offset(classes, "CCSPlayerController_InGameMoneyServices", "m_iAccount"),
            }
    
    def _get_field_offset(self, classes, class_name, field_name):
        """从类中获取字段偏移量"""
        try:
            if class_name in classes and "fields" in classes[class_name]:
                if field_name in classes[class_name]["fields"]:
                    return classes[class_name]["fields"][field_name]
            
            # 如果在当前类中找不到，尝试在父类中查找
            if class_name in classes and "parent" in classes[class_name]:
                parent_class = classes[class_name]["parent"]
                if parent_class:
                    return self._get_field_offset(classes, parent_class, field_name)
            
            raise KeyError(f"未找到偏移量: {class_name}.{field_name}")
            
        except Exception as e:
            print(f" 获取偏移量失败 {class_name}.{field_name}: {e}")
            return 0
    
    def get_base_offset(self, name):
        """获取基础偏移量"""
        if not self.loaded:
            raise RuntimeError("偏移量管理器未加载")
        
        if name not in self.base_offsets:
            raise KeyError(f"未找到基础偏移量: {name}")
        
        return self.base_offsets[name]
    
    def get_entity_offset(self, name):
        """获取实体偏移量"""
        if not self.loaded:
            raise RuntimeError("偏移量管理器未加载")
        
        if name not in self.entity_offsets:
            raise KeyError(f"未找到实体偏移量: {name}")
        
        return self.entity_offsets[name]
    
    def get_all_base_offsets(self):
        """获取所有基础偏移量"""
        return self.base_offsets.copy()
    
    def get_all_entity_offsets(self):
        """获取所有实体偏移量"""
        return self.entity_offsets.copy()
    
    def print_offsets(self):
        """打印所有偏移量（用于调试）"""
        print("=" * 60)
        print("基础偏移量 (offsets.json):")
        print("-" * 30)
        for name, offset in self.base_offsets.items():
            print(f"  {name}: 0x{offset:X} ({offset})")
        
        print("\n实体偏移量 (client_dll.json):")
        print("-" * 30)
        for name, offset in self.entity_offsets.items():
            print(f"  {name}: 0x{offset:X} ({offset})")
        print("=" * 60)


# 全局偏移量管理器实例
offset_manager = OffsetManager()


def get_base_offset(name):
    """获取基础偏移量的便捷函数"""
    return offset_manager.get_base_offset(name)


def get_entity_offset(name):
    """获取实体偏移量的便捷函数"""
    return offset_manager.get_entity_offset(name)


def main():
    """测试偏移量管理器"""
    print(" CS2 偏移量管理器测试")
    print("=" * 50)
    
    if not offset_manager.loaded:
        print(" 偏移量管理器加载失败")
        return
    
    # 测试基础偏移量
    try:
        print("测试基础偏移量:")
        print(f"  dwEntityList: 0x{get_base_offset('dwEntityList'):X}")
        print(f"  dwLocalPlayerController: 0x{get_base_offset('dwLocalPlayerController'):X}")
        print(f"  dwViewAngles: 0x{get_base_offset('dwViewAngles'):X}")
    except Exception as e:
        print(f" 基础偏移量测试失败: {e}")
    
    # 测试实体偏移量
    try:
        print("\n测试实体偏移量:")
        print(f"  m_hPlayerPawn: 0x{get_entity_offset('m_hPlayerPawn'):X}")
        print(f"  m_iTeamNum: 0x{get_entity_offset('m_iTeamNum'):X}")
        print(f"  m_vOldOrigin: 0x{get_entity_offset('m_vOldOrigin'):X}")
        print(f"  m_iHealth: 0x{get_entity_offset('m_iHealth'):X}")
    except Exception as e:
        print(f" 实体偏移量测试失败: {e}")
    
    # 打印所有偏移量
    print("\n是否打印所有偏移量? (y/n): ", end="")
    if input().lower() == 'y':
        offset_manager.print_offsets()
    
    print("\n 测试完成")


if __name__ == "__main__":
    main()
