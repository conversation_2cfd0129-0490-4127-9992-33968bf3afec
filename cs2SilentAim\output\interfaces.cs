// Generated using https://github.com/a2x/cs2-dumper
// 2025-05-16 14:03:14.936842900 UTC

namespace CS2Dumper.Interfaces {
    // Module: animationsystem.dll
    public static class AnimationsystemDll {
        public const nint AnimationSystemUtils_001 = 0x60FE08;
        public const nint AnimationSystem_001 = 0x607D30;
    }
    // Module: client.dll
    public static class ClientDll {
        public const nint ClientToolsInfo_001 = 0x1843EE0;
        public const nint EmptyWorldService001_Client = 0x1800070;
        public const nint GameClientExports001 = 0x1840B98;
        public const nint LegacyGameUI001 = 0x18611D0;
        public const nint Source2Client002 = 0x1A5A570;
        public const nint Source2ClientConfig001 = 0x19F2FA0;
        public const nint Source2ClientPrediction001 = 0x184BF50;
        public const nint Source2ClientUI001 = 0x185F700;
    }
    // Module: engine2.dll
    public static class Engine2Dll {
        public const nint BenchmarkService001 = 0x547D20;
        public const nint BugService001 = 0x5E0CC0;
        public const nint ClientServerEngineLoopService_001 = 0x548A40;
        public const nint EngineGameUI001 = 0x545B80;
        public const nint EngineServiceMgr001 = 0x621BA0;
        public const nint GameEventSystemClientV001 = 0x621EC0;
        public const nint GameEventSystemServerV001 = 0x622000;
        public const nint GameResourceServiceClientV001 = 0x547E20;
        public const nint GameResourceServiceServerV001 = 0x547E80;
        public const nint GameUIService_001 = 0x5E0FC0;
        public const nint HostStateMgr001 = 0x548930;
        public const nint INETSUPPORT_001 = 0x541060;
        public const nint InputService_001 = 0x5E12D0;
        public const nint KeyValueCache001 = 0x5489E0;
        public const nint MapListService_001 = 0x620320;
        public const nint NetworkClientService_001 = 0x6204B0;
        public const nint NetworkP2PService_001 = 0x548130;
        public const nint NetworkServerService_001 = 0x620840;
        public const nint NetworkService_001 = 0x548280;
        public const nint RenderService_001 = 0x620AA0;
        public const nint ScreenshotService001 = 0x620D40;
        public const nint SimpleEngineLoopService_001 = 0x548B50;
        public const nint SoundService_001 = 0x5482C0;
        public const nint Source2EngineToClient001 = 0x545200;
        public const nint Source2EngineToClientStringTable001 = 0x545260;
        public const nint Source2EngineToServer001 = 0x5452F8;
        public const nint Source2EngineToServerStringTable001 = 0x545320;
        public const nint SplitScreenService_001 = 0x5485A0;
        public const nint StatsService_001 = 0x621170;
        public const nint ToolService_001 = 0x548760;
        public const nint VENGINE_GAMEUIFUNCS_VERSION005 = 0x545C10;
        public const nint VProfService_001 = 0x5487A0;
    }
    // Module: filesystem_stdio.dll
    public static class FilesystemStdioDll {
        public const nint VAsyncFileSystem2_001 = 0x20C590;
        public const nint VFileSystem017 = 0x211840;
    }
    // Module: host.dll
    public static class HostDll {
        public const nint DebugDrawQueueManager001 = 0x136FE0;
        public const nint GameModelInfo001 = 0x137020;
        public const nint GameSystem2HostHook = 0x137060;
        public const nint HostUtils001 = 0x137090;
        public const nint PredictionDiffManager001 = 0x1372E0;
        public const nint SaveRestoreDataVersion001 = 0x137410;
        public const nint SinglePlayerSharedMemory001 = 0x137440;
        public const nint Source2Host001 = 0x1374B0;
    }
    // Module: imemanager.dll
    public static class ImemanagerDll {
        public const nint IMEManager001 = 0x2EA50;
    }
    // Module: inputsystem.dll
    public static class InputsystemDll {
        public const nint InputStackSystemVersion001 = 0x36B70;
        public const nint InputSystemVersion001 = 0x387E0;
    }
    // Module: localize.dll
    public static class LocalizeDll {
        public const nint Localize_001 = 0x3AAD0;
    }
    // Module: matchmaking.dll
    public static class MatchmakingDll {
        public const nint GameTypes001 = 0x1A3190;
        public const nint MATCHFRAMEWORK_001 = 0x1AB360;
    }
    // Module: materialsystem2.dll
    public static class Materialsystem2Dll {
        public const nint FontManager_001 = 0x114330;
        public const nint MaterialUtils_001 = 0x10F4C0;
        public const nint PostProcessingSystem_001 = 0x10F3D0;
        public const nint TextLayout_001 = 0x10F450;
        public const nint VMaterialSystem2_001 = 0x113910;
    }
    // Module: meshsystem.dll
    public static class MeshsystemDll {
        public const nint MeshSystem001 = 0x19D600;
    }
    // Module: navsystem.dll
    public static class NavsystemDll {
        public const nint NavSystem001 = 0xFB730;
    }
    // Module: networksystem.dll
    public static class NetworksystemDll {
        public const nint FlattenedSerializersVersion001 = 0x244570;
        public const nint NetworkMessagesVersion001 = 0x2765E0;
        public const nint NetworkSystemVersion001 = 0x26E300;
        public const nint SerializedEntitiesVersion001 = 0x26E3F0;
    }
    // Module: panorama.dll
    public static class PanoramaDll {
        public const nint PanoramaUIEngine001 = 0x4E9250;
    }
    // Module: panorama_text_pango.dll
    public static class PanoramaTextPangoDll {
        public const nint PanoramaTextServices001 = 0x2B38E0;
    }
    // Module: panoramauiclient.dll
    public static class PanoramauiclientDll {
        public const nint PanoramaUIClient001 = 0x28D840;
    }
    // Module: particles.dll
    public static class ParticlesDll {
        public const nint ParticleSystemMgr003 = 0x629C70;
    }
    // Module: pulse_system.dll
    public static class PulseSystemDll {
        public const nint IPulseSystem_001 = 0x17D9A0;
    }
    // Module: rendersystemdx11.dll
    public static class Rendersystemdx11Dll {
        public const nint RenderDeviceMgr001 = 0x3EE1F0;
        public const nint RenderUtils_001 = 0x3EEA58;
        public const nint VRenderDeviceMgrBackdoor001 = 0x3EE290;
    }
    // Module: resourcesystem.dll
    public static class ResourcesystemDll {
        public const nint ResourceSystem013 = 0x72A40;
    }
    // Module: scenefilecache.dll
    public static class ScenefilecacheDll {
        public const nint ResponseRulesCache001 = 0x720F0;
        public const nint SceneFileCache002 = 0x72260;
    }
    // Module: scenesystem.dll
    public static class ScenesystemDll {
        public const nint RenderingPipelines_001 = 0x5CEB10;
        public const nint SceneSystem_002 = 0x7ADFF0;
        public const nint SceneUtils_001 = 0x5CF360;
    }
    // Module: schemasystem.dll
    public static class SchemasystemDll {
        public const nint SchemaSystem_001 = 0x616E0;
    }
    // Module: server.dll
    public static class ServerDll {
        public const nint EmptyWorldService001_Server = 0x136BD70;
        public const nint EntitySubclassUtilsV001 = 0x131C3D0;
        public const nint NavGameTest001 = 0x140AE48;
        public const nint ServerToolsInfo_001 = 0x13C07B8;
        public const nint Source2GameClients001 = 0x13BA680;
        public const nint Source2GameDirector001 = 0x14EE6A0;
        public const nint Source2GameEntities001 = 0x13C06E0;
        public const nint Source2Server001 = 0x13C0550;
        public const nint Source2ServerConfig001 = 0x15B9178;
        public const nint customnavsystem001 = 0x1300B48;
    }
    // Module: soundsystem.dll
    public static class SoundsystemDll {
        public const nint SoundOpSystem001 = 0x3A1C50;
        public const nint SoundOpSystemEdit001 = 0x3A1B20;
        public const nint SoundSystem001 = 0x3A15F0;
        public const nint VMixEditTool001 = 0x48289D0A;
    }
    // Module: steamaudio.dll
    public static class SteamaudioDll {
        public const nint SteamAudio001 = 0x2139F0;
    }
    // Module: steamclient64.dll
    public static class Steamclient64Dll {
        public static readonly nint CLIENTENGINE_INTERFACE_VERSION005 = unchecked((nint)0xFFFFFFFF8BAFD69A);
        public const nint IVALIDATE001 = 0x151D288;
        public const nint SteamClient006 = 0x151A9B0;
        public const nint SteamClient007 = 0x151A9B8;
        public const nint SteamClient008 = 0x151A9C0;
        public const nint SteamClient009 = 0x151A9C8;
        public const nint SteamClient010 = 0x151A9D0;
        public const nint SteamClient011 = 0x151A9D8;
        public const nint SteamClient012 = 0x151A9E0;
        public const nint SteamClient013 = 0x151A9E8;
        public const nint SteamClient014 = 0x151A9F0;
        public const nint SteamClient015 = 0x151A9F8;
        public const nint SteamClient016 = 0x151AA00;
        public const nint SteamClient017 = 0x151AA08;
        public const nint SteamClient018 = 0x151AA10;
        public const nint SteamClient019 = 0x151AA18;
        public const nint SteamClient020 = 0x151AA20;
        public const nint SteamClient021 = 0x151AA28;
        public const nint SteamClient022 = 0x151AA30;
        public const nint p2pvoice002 = 0x14E267F;
        public const nint p2pvoicesingleton002 = 0x14F80E0;
    }
    // Module: tier0.dll
    public static class Tier0Dll {
        public const nint TestScriptMgr001 = 0x37EA80;
        public const nint VEngineCvar007 = 0x38D4E0;
        public const nint VProcessUtils002 = 0x37E990;
        public const nint VStringTokenSystem001 = 0x3A5F00;
    }
    // Module: v8system.dll
    public static class V8systemDll {
        public const nint Source2V8System001 = 0x2C480;
    }
    // Module: vphysics2.dll
    public static class Vphysics2Dll {
        public const nint VPhysics2_Handle_Interface_001 = 0x391F50;
        public const nint VPhysics2_Interface_001 = 0x391F90;
    }
    // Module: vscript.dll
    public static class VscriptDll {
        public const nint VScriptManager010 = 0x128600;
    }
    // Module: vstdlib_s64.dll
    public static class VstdlibS64Dll {
        public const nint IVALIDATE001 = 0x6A990;
        public const nint VEngineCvar002 = 0x69070;
    }
    // Module: worldrenderer.dll
    public static class WorldrendererDll {
        public const nint WorldRendererMgr001 = 0x161D80;
    }
}
