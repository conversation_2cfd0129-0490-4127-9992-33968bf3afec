import time
import keyboard
import pymem
from random import uniform
from pynput.mouse import <PERSON><PERSON>, Controller
from front_detection_improved import FrontDetectorImproved
from cs2_auto_aim import CS2AutoAim
from cs2_common_lib import (
    CS2ProcessManager, CS2ConfigManager, CS2GameConnector, CS2Utils
)
from offset_manager import get_base_offset, get_entity_offset

class PathExecutor:
    def __init__(self):
        """初始化路径执行器"""
        # 加载配置（使用公共配置管理器）
        self.config = CS2ConfigManager.load_config()
        self.paths_data = CS2ConfigManager.load_paths_data()

        # 设置偏移量（使用偏移量管理器）
        self.client_dll_offset_x = get_base_offset("dwViewAngles") + 0x4
        self.dw_local_player_pawn = get_base_offset("dwLocalPlayerPawn")
        self.x_offset = int(self.config["x_offset"], 16)
        self.y_offset = int(self.config["y_offset"], 16)
        self.z_offset = int(self.config["z_offset"], 16)
        
        # 游戏连接相关
        self.pm = None
        self.client_dll = None
        self.player_ptr = None
        self.process_handle = None

        # 前方检测器（用于动态站位调整）
        self.front_detector = FrontDetectorImproved()

        # 自动瞄准系统
        self.auto_aim = CS2AutoAim()

        # 鼠标控制器（用于开枪）
        self.mouse = Controller()

        # 队友规避参数
        self.teammate_avoidance_enabled = True
        self.max_avoidance_attempts = 8  # 最大规避尝试次数
        self.avoidance_move_time = 0.3   # 每次移动时间（秒）
        self.crosshair_tolerance = 3.0   # 准心安全容差（度）

    @staticmethod
    def get_cs_windows():
        """获取CS2游戏窗口"""
        return CS2ProcessManager.get_cs_windows()

    def connect_to_game(self):
        """连接到游戏"""
        # 使用公共游戏连接器的窗口连接方法
        game_connector = CS2GameConnector()
        if not game_connector.connect_via_windows():
            return False

        # 设置兼容性属性
        self.pm = game_connector.pm
        self.client_dll = game_connector.client_dll
        self.process_handle = game_connector.process_handle

        # 读取玩家指针
        self.player_ptr = self.pm.read_longlong(self.client_dll + self.dw_local_player_pawn)

        return True

    def move_forward_to_target(self, x_range=None, y_range=None, z_range=None):
        """移动到目标位置"""
        # 自动排序区间
        if x_range:
            x_range = sorted(x_range)
        if y_range:
            y_range = sorted(y_range)
        if z_range:
            z_range = sorted(z_range)

        while True:
            my_x = self.pm.read_float(self.player_ptr + self.x_offset)
            my_y = self.pm.read_float(self.player_ptr + self.y_offset)
            my_z = self.pm.read_float(self.player_ptr + self.z_offset)

            in_x = x_range and (x_range[0] <= my_x <= x_range[1])
            in_y = y_range and (y_range[0] <= my_y <= y_range[1])
            in_z = z_range and (z_range[0] <= my_z <= z_range[1])

            if (x_range is None or in_x) and (y_range is None or in_y) and (z_range is None or in_z):
                keyboard.release('w')
                break

            keyboard.press('w')
            time.sleep(0.005)

        keyboard.release('w')
        print(f"到达位置: ({my_x:.1f}, {my_y:.1f}, {my_z:.1f})")
        return True

    def x_turn_view_to_target(self, fov_range):
        """转向到目标视角"""
        min_fov, max_fov = sorted(fov_range)
        span = max_fov - min_fov

        keyboard.press('q')
        try:
            while True:
                fov_x = pymem.memory.read_float(self.process_handle, self.client_dll + self.client_dll_offset_x)

                if span < 350:
                    inside = (min_fov <= fov_x <= max_fov)
                else:
                    inside = (fov_x >= max_fov) or (fov_x <= min_fov)

                if inside:
                    keyboard.release('q')
                    break

                keyboard.press('q')
                time.sleep(0.005)
        finally:
            keyboard.release('q')
            print(f"转向完成: {fov_x:.1f}°")

    def auto_aim_and_shoot(self, target_kill_count=1):
        """自动瞄准和开枪"""
        print(f"\n 开始自动瞄准和射击，目标击杀: {target_kill_count}")

        # 连接自动瞄准系统
        if not self.auto_aim.connect():
            print(" 自动瞄准系统连接失败")
            return False

        # 启用自动瞄准
        self.auto_aim.enabled = True
        self.auto_aim.debug_mode = True

        kill_count = 0
        last_enemy_scan_time = time.time()
        no_enemy_timeout = 15.0  # 15秒没有敌人就停止

        print(" 自动瞄准已启用，开始搜索敌人...")
        print(" cs2_auto_aim.py 将自动控制视角瞄准敌人")

        try:
            while kill_count < target_kill_count:
                current_time = time.time()

                # 让自动瞄准系统更新（这会自动瞄准敌人）
                self.auto_aim.update()

                # 扫描敌人状态
                scan_result = self.auto_aim.scan_enemies()

                if not scan_result or scan_result['enemy_count'] == 0:
                    # 没有敌人，检查超时
                    if current_time - last_enemy_scan_time > no_enemy_timeout:
                        print(f" {no_enemy_timeout}秒内未发现敌人，停止搜索")
                        break

                    # 等待敌人出现
                    time.sleep(0.05)
                    continue

                # 发现敌人，更新时间
                last_enemy_scan_time = current_time

                # 获取存活的敌人（修正数据结构）
                alive_enemies = scan_result.get('alive_enemies', [])

                if not alive_enemies:
                    time.sleep(0.05)
                    continue

                # 选择第一个敌人作为目标（cs2_auto_aim.py会自动选择最佳目标）
                # alive_enemies 格式: [(index, player_data), ...]
                target_index, target_player = alive_enemies[0]
                target_health = target_player.get('health', 0)

                print(f" 发现目标 {target_index}，血量: {target_health}")

                # 记录射击前的敌人数量
                initial_enemy_count = scan_result['enemy_count']

                # 开始射击循环
                shoot_start_time = time.time()
                max_shoot_time = 5.0  # 最多射击5秒
                shots_fired = 0

                while (time.time() - shoot_start_time < max_shoot_time and
                       kill_count < target_kill_count):

                    # 继续让自动瞄准系统更新瞄准
                    self.auto_aim.update()

                    # 检查是否还有敌人
                    current_scan = self.auto_aim.scan_enemies()
                    if not current_scan or current_scan['enemy_count'] == 0:
                        print(" 所有敌人已被击杀")
                        kill_count = target_kill_count  # 完成任务
                        break

                    # 检查敌人数量是否减少（表示击杀成功）
                    if current_scan['enemy_count'] < initial_enemy_count:
                        kills_this_round = initial_enemy_count - current_scan['enemy_count']
                        kill_count += kills_this_round
                        print(f" 击杀成功! +{kills_this_round}, 当前击杀: {kill_count}/{target_kill_count}")

                        if kill_count >= target_kill_count:
                            break

                        # 更新初始敌人数量，继续下一轮
                        initial_enemy_count = current_scan['enemy_count']
                        break

                    #  队友规避检查 - 射击前确保不会误伤队友
                    if not self.avoid_teammate_in_crosshair():
                        print("⚠️ 无法规避队友，跳过本次射击")
                        time.sleep(0.1)
                        continue

                    # 开枪（参考util.py的射击逻辑）
                    self.mouse.press(Button.left)
                    time.sleep(uniform(0.01, 0.03))
                    self.mouse.release(Button.left)
                    time.sleep(uniform(0.01, 0.05))

                    shots_fired += 1

                    # 每射击10发子弹检查一次状态
                    if shots_fired % 10 == 0:
                        print(f" 已射击 {shots_fired} 发子弹...")

                # 如果射击超时但没有击杀，继续寻找下一个目标
                if time.time() - shoot_start_time >= max_shoot_time:
                    print(" 射击超时，继续寻找下一个目标...")

                # 短暂等待再继续
                time.sleep(0.1)

        finally:
            # 清理：关闭自动瞄准
            self.auto_aim.enabled = False
            print(" 自动瞄准已关闭")

        print(f" 射击完成，总击杀: {kill_count}/{target_kill_count}")
        return kill_count >= target_kill_count

    def get_local_team(self):
        """获取本地玩家队伍编号"""
        try:
            # 获取本地玩家控制器
            local_player_controller = self.pm.read_longlong(
                self.client_dll + get_base_offset("dwLocalPlayerController")
            )
            if not local_player_controller:
                return None

            # 获取队伍编号
            team = self.pm.read_int(local_player_controller + get_entity_offset("m_iTeamNum"))
            return team
        except Exception as e:
            print(f" 获取队伍信息失败: {e}")
            return None

    def plant_or_defuse_bomb(self, route_number):
        """根据路线和阵营执行下包或拆包操作"""
        print(f"\n 开始执行路线{route_number}的炸弹操作...")

        # 获取本地玩家队伍
        local_team = self.get_local_team()
        if not local_team:
            print(" 无法获取队伍信息")
            return False

        # 判断阵营和操作类型
        if local_team == 2:  # T（匪徒）
            team_name = "T（匪徒）"
            if route_number == 4:
                action = "下包A点"
                hold_time = 6.0
            elif route_number == 5:
                action = "下包B点"
                hold_time = 6.0
            else:
                print(f" 路线{route_number}不支持T阵营操作")
                return False
        elif local_team == 3:  # CT（警察）
            team_name = "CT（警察）"
            if route_number == 4:
                action = "拆包A点"
                hold_time = 14.0
            elif route_number == 5:
                action = "拆包B点"
                hold_time = 14.0
            else:
                print(f" 路线{route_number}不支持CT阵营操作")
                return False
        else:
            print(f" 未知队伍编号: {local_team}")
            return False

        print(f" 检测到阵营: {team_name}")
        print(f" 执行操作: {action}")
        print(f" 需要长按J键 {hold_time} 秒")

        # 执行长按J键操作
        try:
            print(f" 开始长按J键...")
            keyboard.press('j')

            # 显示进度
            start_time = time.time()
            while time.time() - start_time < hold_time:
                elapsed = time.time() - start_time
                progress = (elapsed / hold_time) * 100
                print(f" 进度: {progress:.1f}% ({elapsed:.1f}/{hold_time}秒)", end='\r')
                time.sleep(0.1)

            keyboard.release('j')
            print(f"\n {action}完成！")
            return True

        except Exception as e:
            print(f"\n {action}失败: {e}")
            keyboard.release('j')  # 确保释放按键
            return False

    @staticmethod
    def is_at_position(my_x, my_y, target, tolerance=1):
        """检查是否在指定位置"""
        return CS2Utils.is_at_position(my_x, my_y, target, tolerance)

    def execute_avoidance_movement(self, direction):
        """执行规避移动

        Args:
            direction: 移动方向 ('left', 'right', 'back', 'forward')
        """
        move_time = self.avoidance_move_time

        print(f" 执行队友规避移动: {direction}")

        if direction == 'left':
            keyboard.press('a')
            time.sleep(move_time)
            keyboard.release('a')
        elif direction == 'right':
            keyboard.press('d')
            time.sleep(move_time)
            keyboard.release('d')
        elif direction == 'back':
            keyboard.press('s')
            time.sleep(move_time)
            keyboard.release('s')
        elif direction == 'forward':
            keyboard.press('w')
            time.sleep(move_time)
            keyboard.release('w')

        # 等待位置更新
        time.sleep(0.1)

    def avoid_teammate_in_crosshair(self):
        """规避准心中的队友

        Returns:
            bool: 是否成功规避队友
        """
        if not self.teammate_avoidance_enabled:
            return True

        # 连接前方检测器
        if not self.front_detector.connect_to_game():
            print(" 前方检测器连接失败，跳过队友规避")
            return True

        attempts = 0

        while attempts < self.max_avoidance_attempts:
            # 检查当前射击角度是否安全
            is_safe, teammate_info = self.front_detector.check_safe_shooting_angle(
                self.crosshair_tolerance
            )

            if is_safe:
                if attempts > 0:
                    print(f" 队友规避成功，共尝试 {attempts} 次")
                return True

            # 准心瞄准了队友，需要规避
            print(f" 检测到准心瞄准队友: {teammate_info['name']} "
                  f"(距离: {teammate_info['distance_2d']:.1f}m, "
                  f"角度差: {teammate_info['angle_diff']:.1f}°)")

            # 获取推荐的规避方向
            direction = self.front_detector.get_teammate_avoidance_direction(teammate_info)

            if not direction:
                print(" 无法确定规避方向")
                break

            # 执行规避移动
            self.execute_avoidance_movement(direction)
            attempts += 1

            print(f" 规避尝试: {attempts}/{self.max_avoidance_attempts}")

        # 规避失败
        print(f" 队友规避失败，已尝试 {attempts} 次")
        return False

    def set_teammate_avoidance_config(self, enabled=True, max_attempts=8,
                                    move_time=0.3, tolerance=3.0):
        """配置队友规避参数

        Args:
            enabled: 是否启用队友规避
            max_attempts: 最大规避尝试次数
            move_time: 每次移动时间（秒）
            tolerance: 准心安全容差（度）
        """
        self.teammate_avoidance_enabled = enabled
        self.max_avoidance_attempts = max_attempts
        self.avoidance_move_time = move_time
        self.crosshair_tolerance = tolerance

        print(f"   队友规避配置更新:")
        print(f"   启用状态: {enabled}")
        print(f"   最大尝试次数: {max_attempts}")
        print(f"   移动时间: {move_time}秒")
        print(f"   准心容差: {tolerance}度")

    def get_teammate_avoidance_status(self):
        """获取队友规避系统状态"""
        return {
            'enabled': self.teammate_avoidance_enabled,
            'max_attempts': self.max_avoidance_attempts,
            'move_time': self.avoidance_move_time,
            'tolerance': self.crosshair_tolerance
        }

    def execute_path(self, map_name, route_number=1, kill_count=1):
        """根据当前位置自动执行对应路径

        Args:
            map_name: 地图名称
            route_number: 路线编号 (1, 2, 3, 4, 5)
                         路线1-3: 执行路径 + 自动瞄准射击
                         路线4-5: 仅执行路径移动
            kill_count: 击杀目标数量 (仅路线1-3有效)
        """
        if not self.connect_to_game():
            return False

        # 验证路线编号
        if route_number not in [1, 2, 3,4,5]:
            print(f"无效的路线编号: {route_number}，请选择1、2或3 4 5 ")
            return False

        # 获取当前位置
        my_x = self.pm.read_float(self.player_ptr + self.x_offset)
        my_y = self.pm.read_float(self.player_ptr + self.y_offset)
        print(f"当前位置: ({my_x:.1f}, {my_y:.1f})")

        # 查找匹配的路点
        waypoints = self.paths_data["maps"].get(map_name, {}).get("waypoints", [])
        matched_waypoint = None

        for waypoint in waypoints:
            if self.is_at_position(my_x, my_y, waypoint):
                matched_waypoint = waypoint
                print(f"匹配到出生点: ({waypoint['x']}, {waypoint['y']})")
                break

        if not matched_waypoint:
            print(f"未找到匹配的出生点位置")
            return False

        # 调整初始视角
        if matched_waypoint.get('fov_range'):
            print(f"调整初始视角到: {matched_waypoint['fov_range']}")
            self.x_turn_view_to_target(matched_waypoint['fov_range'])

        # 获取指定路线
        routes = matched_waypoint.get('routes', [])
        if len(routes) < route_number:
            print(f"该出生点没有路线{route_number}")
            return False

        selected_route = routes[route_number - 1]  # 转换为0基索引
        route_name = selected_route.get('name', f'路线{route_number}')
        route_description = selected_route.get('description', '')
        actions = selected_route.get('actions', [])

        print(f"选择路线: {route_name}")
        if route_description:
            print(f"路线描述: {route_description}")

        if not actions:
            print(f"路线{route_number}暂无配置动作")
            return False

        print(f"开始执行路线，共{len(actions)}个动作")
        for i, action in enumerate(actions):
            print(f"执行动作 {i+1}: {action['type']}")

            if action["type"] == "turning":
                self.x_turn_view_to_target(action["fov_range"])
            elif action["type"] == "movement":
                kwargs = {}
                if "x_range" in action:
                    kwargs["x_range"] = action["x_range"]
                if "y_range" in action:
                    kwargs["y_range"] = action["y_range"]
                if "z_range" in action:
                    kwargs["z_range"] = action["z_range"]

                if not self.move_forward_to_target(**kwargs):
                    print(f"移动失败，停止执行")
                    return False

        print("路径执行完成！")

        # 动态站位调整（直接使用前方检测）
        print("\n 开始动态站位调整...")
        try:
            # 共享游戏连接给前方检测器
            if not hasattr(self.front_detector, 'pm') or not self.front_detector.pm:
                self.front_detector.pm = self.pm
                self.front_detector.client_dll = self.client_dll
                self.front_detector.process_handle = self.process_handle

            # 检测前方队友
            front_teammates = self.front_detector.detect_front_teammates()
            blocking_teammates = [t for t in front_teammates if t['is_blocking']]

            if not blocking_teammates:
                print(" 前方无队友遮挡，无需调整")
            else:
                print(f"检测到 {len(blocking_teammates)} 个队友遮挡，建议手动调整位置")
                for teammate in blocking_teammates:
                    print(f"   队友距离: {teammate['distance']:.1f}, 角度差: {teammate['angle_diff']:.1f}°")

        except Exception as e:
            print(f" 动态站位调整失败: {e}")
            print("  继续执行后续动作...")

        # 执行路径完成后的动作
        if route_number in [1, 2, 3]:
            # 路线1、2、3执行自动瞄准和开枪
            print(f"\n 路线{route_number}执行完成，开始自动瞄准和射击...")
            try:
                # 共享游戏连接给自动瞄准系统
                self.auto_aim.helper.pm = self.pm
                self.auto_aim.helper.client_dll = self.client_dll
                self.auto_aim.helper.process_handle = self.process_handle

                # 执行自动瞄准和开枪
                success = self.auto_aim_and_shoot(target_kill_count=kill_count)
                if success:
                    print(" 自动射击任务完成")
                else:
                    print(" 自动射击未完全完成")

            except Exception as e:
                print(f" 自动射击失败: {e}")

        elif route_number in [4, 5]:
            # 路线4、5执行下包/拆包操作
            print(f"\n 路线{route_number}执行完成，开始炸弹操作...")
            try:
                success = self.plant_or_defuse_bomb(route_number)
                if success:
                    print(" 炸弹操作完成")
                else:
                    print(" 炸弹操作未完全完成")

            except Exception as e:
                print(f" 炸弹操作失败: {e}")

        print(" 路径执行完成")
        return True

    def list_available_waypoints(self):
        """列出所有可用出生点和路线"""
        print("可用地图和出生点:")
        for map_name, map_info in self.paths_data["maps"].items():
            print(f"\n{map_name} ({map_info['name']}):")
            for i, waypoint in enumerate(map_info.get("waypoints", [])):
                print(f"  出生点{i+1}: ({waypoint['x']}, {waypoint['y']})")

                # 显示该出生点的所有路线
                routes = waypoint.get('routes', [])
                if routes:
                    for j, route in enumerate(routes):
                        route_name = route.get('name', f'路线{j+1}')
                        route_description = route.get('description', '')
                        actions_count = len(route.get('actions', []))
                        status = "已配置" if actions_count > 0 else "未配置"
                        print(f"    路线{j+1}: {route_name} - {route_description} [{status}]")
                else:
                    print(f"    暂无路线配置")

def main():
    executor = PathExecutor()

    # 列出可用出生点和路线
    executor.list_available_waypoints()

    # 显示队友规避系统状态
    print("\n" + "="*50)
    print("    队友规避系统状态:")
    status = executor.get_teammate_avoidance_status()
    print(f"   启用状态: {' 已启用' if status['enabled'] else ' 已禁用'}")
    print(f"   最大尝试次数: {status['max_attempts']}")
    print(f"   移动时间: {status['move_time']}秒")
    print(f"   准心容差: {status['tolerance']}度")

    # 示例用法
    print("\n" + "="*50)
    print("使用示例:")
    print("1. 路线1-3（带自动射击+队友规避）: executor.execute_path('mirage', route_number=1, kill_count=2)")
    print("2. 路线4（A点操作）: executor.execute_path('mirage', route_number=4)")
    print("   - T阵营: 去A点下包（长按J 6秒）")
    print("   - CT阵营: 去A点拆包（长按J 14秒）")
    print("3. 路线5（B点操作）: executor.execute_path('mirage', route_number=5)")
    print("   - T阵营: 去B点下包（长按J 6秒）")
    print("   - CT阵营: 去B点拆包（长按J 14秒）")

    print("\n   队友规避功能说明:")
    print("   - 射击前自动检测准心是否瞄准队友")
    print("   - 如果瞄准队友，自动调整位置（左/右/前/后移动）")
    print("   - 最多尝试8次规避，确保不会误伤队友")
    print("   - 可通过 set_teammate_avoidance_config() 调整参数")

    print("\n正在检测当前位置并执行路线1（自动射击+队友规避）...")
    print("提示: 路线1-3会自动射击并规避队友，路线4-5会根据阵营自动下包/拆包")

    # 默认执行：路线5（仅移动）
    executor.execute_path("mirage", route_number=1, kill_count=3)

if __name__ == '__main__':
    main()
