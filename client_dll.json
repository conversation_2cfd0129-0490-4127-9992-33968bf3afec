{"client.dll": {"classes": {"ActiveModelConfig_t": {"fields": {"m_AssociatedEntities": 64, "m_AssociatedEntityNames": 88, "m_Handle": 48, "m_Name": 56}, "metadata": [{"name": "m_<PERSON>le", "type": "NetworkVarNames", "type_name": "ModelConfigHandle_t"}, {"name": "m_Name", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_AssociatedEntities", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseModelEntity>"}, {"name": "m_AssociatedEntityNames", "type": "NetworkVarNames", "type_name": "string_t"}], "parent": ""}, "CAnimGraphNetworkedVariables": {"fields": {"m_OwnerOnlyPredNetBoolVariables": 248, "m_OwnerOnlyPredNetByteVariables": 272, "m_OwnerOnlyPredNetFloatVariables": 392, "m_OwnerOnlyPredNetGlobalSymbolVariables": 464, "m_OwnerOnlyPredNetIntVariables": 320, "m_OwnerOnlyPredNetQuaternionVariables": 440, "m_OwnerOnlyPredNetUInt16Variables": 296, "m_OwnerOnlyPredNetUInt32Variables": 344, "m_OwnerOnlyPredNetUInt64Variables": 368, "m_OwnerOnlyPredNetVectorVariables": 416, "m_PredNetBoolVariables": 8, "m_PredNetByteVariables": 32, "m_PredNetFloatVariables": 152, "m_PredNetGlobalSymbolVariables": 224, "m_PredNetIntVariables": 80, "m_PredNetQuaternionVariables": 200, "m_PredNetUInt16Variables": 56, "m_PredNetUInt32Variables": 104, "m_PredNetUInt64Variables": 128, "m_PredNetVectorVariables": 176, "m_flLastTeleportTime": 500, "m_nBoolVariablesCount": 488, "m_nOwnerOnlyBoolVariablesCount": 492, "m_nRandomSeedOffset": 496}, "metadata": [{"name": "m_PredNetBoolVariables", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_PredNetByteVariables", "type": "NetworkVarNames", "type_name": "byte"}, {"name": "m_PredNetUInt16Variables", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_PredNetIntVariables", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_PredNetUInt32Variables", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_PredNetUInt64Variables", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_PredNetFloatVariables", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_PredNetVectorVariables", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_PredNetQuaternionVariables", "type": "NetworkVarNames", "type_name": "Quaternion"}, {"name": "m_PredNetGlobalSymbolVariables", "type": "NetworkVarNames", "type_name": "CGlobalSymbol"}, {"name": "m_OwnerOnlyPredNetBoolVariables", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_OwnerOnlyPredNetByteVariables", "type": "NetworkVarNames", "type_name": "byte"}, {"name": "m_OwnerOnlyPredNetUInt16Variables", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_OwnerOnlyPredNetIntVariables", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_OwnerOnlyPredNetUInt32Variables", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_OwnerOnlyPredNetUInt64Variables", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_OwnerOnlyPredNetFloatVariables", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_OwnerOnlyPredNetVectorVariables", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_OwnerOnlyPredNetQuaternionVariables", "type": "NetworkVarNames", "type_name": "Quaternion"}, {"name": "m_OwnerOnlyPredNetGlobalSymbolVariables", "type": "NetworkVarNames", "type_name": "CGlobalSymbol"}, {"name": "m_nBoolVariablesCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nOwnerOnlyBoolVariablesCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nRandomSeedOffset", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flLastTeleportTime", "type": "NetworkVarNames", "type_name": "float"}], "parent": ""}, "CAttributeList": {"fields": {"m_Attributes": 8, "m_pManager": 112}, "metadata": [{"name": "m_Attributes", "type": "NetworkVarNames", "type_name": "CEconItemAttribute"}], "parent": ""}, "CAttributeManager": {"fields": {"m_CachedResults": 48, "m_ProviderType": 44, "m_Providers": 8, "m_bPreventLoopback": 40, "m_hOuter": 36, "m_iReapplyProvisionParity": 32}, "metadata": [{"name": "m_iReapplyProvisionParity", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_hOuter", "type": "NetworkVarNames", "type_name": "EHANDLE"}, {"name": "m_ProviderType", "type": "NetworkVarNames", "type_name": "attributeprovidertypes_t"}], "parent": ""}, "CAttributeManager__cached_attribute_float_t": {"fields": {"flIn": 0, "flOut": 16, "iAttribHook": 8}, "metadata": [], "parent": null}, "CBaseAnimGraph": {"fields": {"m_RagdollPose": 3992, "m_bAnimGraphUpdateEnabled": 3920, "m_bAnimationUpdateScheduled": 3940, "m_bBuiltRagdoll": 3968, "m_bHasAnimatedMaterialAttributes": 4080, "m_bInitiallyPopulateInterpHistory": 3904, "m_bRagdollClientSide": 4065, "m_bRagdollEnabled": 4064, "m_bSuppressAnimEventSounds": 3906, "m_flMaxSlopeDistance": 3924, "m_nForceBone": 3956, "m_pClientsideRagdoll": 3960, "m_vLastSlopeCheckPos": 3928, "m_vecForce": 3944}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_bInitiallyPopulateInterpHistory", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bAnimGraphUpdateEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vecForce", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nForceBone", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "PhysicsRagdollPose_t"}, {"name": "m_b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>bled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bRagdollClientSide", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseModelEntity"}, "CBaseAnimGraphController": {"fields": {"m_animGraphNetworkedVars": 24, "m_bIsUsingAG2": 6248, "m_bLastUpdateSkipped": 5332, "m_bNetworkedAnimationInputsChanged": 5330, "m_bNetworkedSequenceChanged": 5331, "m_bSequenceFinished": 5288, "m_flPlaybackRate": 5316, "m_flPrevAnimUpdateTime": 5336, "m_flSeqFixedCycle": 5308, "m_flSeqStartTime": 5304, "m_flSoundSyncTime": 5292, "m_hGraphDefinitionAG2": 6240, "m_hSequence": 5300, "m_nActiveIKChainMask": 5296, "m_nAnimLoopMode": 5312, "m_nGraphCreationFlagsAG2": 6284, "m_nNotifyState": 5328, "m_nSerializePoseRecipeSizeAG2": 6280, "m_nServerGraphDefReloadCountAG2": 6356, "m_serializedPoseRecipeAG2": 6256}, "metadata": [{"name": "m_animGraphNetworkedVars", "type": "NetworkVarNames", "type_name": "CAnimGraphNetworkedVariables"}, {"name": "m_hSequence", "type": "NetworkVarNames", "type_name": "HSequence"}, {"name": "m_flSeqStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flSeqFixedCycle", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nAnimLoopMode", "type": "NetworkVarNames", "type_name": "AnimLoopMode_t"}, {"name": "m_hGraphDefinitionAG2", "type": "NetworkVarNames", "type_name": "HNmGraphDefinitionStrong"}, {"name": "m_bIsUsingAG2", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_serializedPoseRecipeAG2", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nSerializePoseRecipeSizeAG2", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nGraphCreationFlagsAG2", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nServerGraphDefReloadCountAG2", "type": "NetworkVarNames", "type_name": "int"}], "parent": "CSkeletonAnimationController"}, "CBaseFilter": {"fields": {"m_OnFail": 1576, "m_OnPass": 1536, "m_bNegated": 1528}, "metadata": [], "parent": "CLogicalEntity"}, "CBasePlayerController": {"fields": {"m_CommandContext": 1536, "m_bIsHLTV": 1760, "m_bIsLocalPlayerController": 1912, "m_bKnownTeamMismatch": 1720, "m_bNoClipEnabled": 1913, "m_hPawn": 1716, "m_hPredictedPawn": 1724, "m_hSplitOwner": 1732, "m_hSplitScreenPlayers": 1736, "m_iConnected": 1764, "m_iDesiredFOV": 1916, "m_iszPlayerName": 1768, "m_nInButtonsWhichAreToggles": 1704, "m_nSplitScreenSlot": 1728, "m_nTickBase": 1712, "m_steamID": 1904}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkUserGroupProxy", "type": "Unknown"}, {"name": "MNetworkUserGroupProxy", "type": "Unknown"}, {"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "m_nTickBase", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_h<PERSON>awn", "type": "NetworkVarNames", "type_name": "CHandle<CBasePlayerPawn>"}, {"name": "m_bKnownTeamMismatch", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iConnected", "type": "NetworkVarNames", "type_name": "PlayerConnectedState"}, {"name": "m_iszPlayerName", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_steamID", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_bNoClipEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iDesiredFOV", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "MNetworkReplayCompatField", "type": "Unknown"}], "parent": "C_BaseEntity"}, "CBasePlayerControllerAPI": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}], "parent": null}, "CBasePlayerVData": {"fields": {"m_flArmDamageMultiplier": 312, "m_flChestDamageMultiplier": 280, "m_flCrouchTime": 372, "m_flDrowningDamageInterval": 348, "m_flHeadDamageMultiplier": 264, "m_flHoldBreathTime": 344, "m_flLegDamageMultiplier": 328, "m_flStomachDamageMultiplier": 296, "m_flUseAngleTolerance": 368, "m_flUseRange": 364, "m_nDrowningDamageInitial": 352, "m_nDrowningDamageMax": 356, "m_nWaterSpeed": 360, "m_sModelName": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CBasePlayerWeaponVData": {"fields": {"m_aShootSounds": 1040, "m_bAllowFlipping": 489, "m_bAutoSwitchFrom": 1017, "m_bAutoSwitchTo": 1016, "m_bBuiltRightHanded": 488, "m_bLinkedCooldowns": 988, "m_bReserveAmmoAsClips": 1008, "m_bTreatAsSingleClip": 1009, "m_flDropSpeed": 1024, "m_flMuzzleSmokeDecrementRate": 984, "m_flMuzzleSmokeTimeout": 980, "m_iDefaultClip1": 1000, "m_iDefaultClip2": 1004, "m_iFlags": 989, "m_iMaxClip1": 992, "m_iMaxClip2": 996, "m_iPosition": 1032, "m_iRumbleEffect": 1020, "m_iSlot": 1028, "m_iWeight": 1012, "m_nMuzzleSmokeShotThreshold": 976, "m_nPrimaryAmmoType": 990, "m_nSecondaryAmmoType": 991, "m_sMuzzleAttachment": 496, "m_sToolsOnlyOwnerModelName": 264, "m_szBarrelSmokeParticle": 752, "m_szMuzzleFlashParticle": 528, "m_szWorldModel": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CBaseProp": {"fields": {"m_bConformToCollisionBounds": 4464, "m_bModelOverrodeBlockLOS": 4456, "m_iShapeType": 4460, "m_mPreferredCatchTransform": 4480}, "metadata": [], "parent": "CBaseAnimGraph"}, "CBasePulseGraphInstance": {"fields": {}, "metadata": [{"name": "MPulseInstanceDomainInfo", "type": "Unknown"}], "parent": null}, "CBaseTriggerAPI": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}], "parent": null}, "CBodyComponent": {"fields": {"__m_pChainEntity": 72, "m_pSceneNode": 8}, "metadata": [], "parent": "CEntityComponent"}, "CBodyComponentBaseAnimGraph": {"fields": {"m_animationController": 1424}, "metadata": [{"name": "m_animationController", "type": "NetworkVarNames", "type_name": "CBaseAnimGraphController"}], "parent": "CBodyComponentSkeletonInstance"}, "CBodyComponentBaseModelEntity": {"fields": {}, "metadata": [], "parent": "CBodyComponentSkeletonInstance"}, "CBodyComponentPoint": {"fields": {"m_sceneNode": 128}, "metadata": [{"name": "m_sceneNode", "type": "NetworkVarNames", "type_name": "CGameSceneNode"}], "parent": "CBodyComponent"}, "CBodyComponentSkeletonInstance": {"fields": {"m_skeletonInstance": 128}, "metadata": [{"name": "m_skeletonInstance", "type": "NetworkVarNames", "type_name": "CSkeletonInstance"}], "parent": "CBodyComponent"}, "CBombTarget": {"fields": {"m_bBombPlantedHere": 4096}, "metadata": [{"name": "m_bBombPlantedHere", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseTrigger"}, "CBuoyancyHelper": {"fields": {"m_bNeutrallyBuoyant": 44, "m_flFluidDensity": 28, "m_flNeutrallyBuoyantAngularDamping": 40, "m_flNeutrallyBuoyantGravity": 32, "m_flNeutrallyBuoyantLinearDamping": 36, "m_nFluidType": 24, "m_vecFractionOfWheelSubmergedForWheelDrag": 96, "m_vecFractionOfWheelSubmergedForWheelFriction": 48, "m_vecWheelDrag": 120, "m_vecWheelFrictionScales": 72}, "metadata": [], "parent": ""}, "CCSClientPointScriptEntity": {"fields": {}, "metadata": [], "parent": "CCSPointScriptEntity"}, "CCSGO_WingmanIntroCharacterPosition": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamIntroCharacterPosition"}, "CCSGO_WingmanIntroCounterTerroristPosition": {"fields": {}, "metadata": [], "parent": "CCSGO_WingmanIntroCharacterPosition"}, "CCSGO_WingmanIntroTerroristPosition": {"fields": {}, "metadata": [], "parent": "CCSGO_WingmanIntroCharacterPosition"}, "CCSGameModeRules": {"fields": {"__m_pChainEntity": 8}, "metadata": [], "parent": null}, "CCSGameModeRules_ArmsRace": {"fields": {"m_WeaponSequence": 48}, "metadata": [{"name": "m_WeaponSequence", "type": "NetworkVarNames", "type_name": "CUtlString"}], "parent": null}, "CCSGameModeRules_Deathmatch": {"fields": {"m_flDMBonusStartTime": 48, "m_flDMBonusTimeLength": 52, "m_sDMBonusWeapon": 56}, "metadata": [{"name": "m_flDMBonusStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flDMBonusTimeLength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_sDMBonusWeapon", "type": "NetworkVarNames", "type_name": "CUtlString"}], "parent": null}, "CCSGameModeRules_Noop": {"fields": {}, "metadata": [], "parent": null}, "CCSObserver_CameraServices": {"fields": {}, "metadata": [], "parent": "CCSPlayerBase_CameraServices"}, "CCSObserver_MovementServices": {"fields": {}, "metadata": [], "parent": "CPlayer_MovementServices"}, "CCSObserver_ObserverServices": {"fields": {"m_bObserverInterpolationNeedsDeferredSetup": 164, "m_flObsInterp_PathLength": 116, "m_hLastObserverTarget": 88, "m_obsInterpState": 160, "m_qObsInterp_OrientationStart": 128, "m_qObsInterp_OrientationTravelDir": 144, "m_vecObserverInterpStartPos": 104, "m_vecObserverInterpolateOffset": 92}, "metadata": [], "parent": "CPlayer_ObserverServices"}, "CCSObserver_UseServices": {"fields": {}, "metadata": [], "parent": "CPlayer_UseServices"}, "CCSPlayerBase_CameraServices": {"fields": {"m_flFOVRate": 660, "m_flFOVTime": 656, "m_flLastShotFOV": 668, "m_hZoomOwner": 664, "m_iFOV": 648, "m_iFOVStart": 652}, "metadata": [{"name": "m_iFOV", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_iFOVStart", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_flFOVTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flFOVRate", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_h<PERSON><PERSON>Owner", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}], "parent": "CPlayer_CameraServices"}, "CCSPlayerController": {"fields": {"m_bAbandonAllowsSurrender": 2266, "m_bAbandonOffersInstantSurrender": 2267, "m_bCanControlObservedBot": 2296, "m_bCannotBeKicked": 2264, "m_bControllingBot": 2288, "m_bDisconnection1MinWarningPrinted": 2268, "m_bEverFullyConnected": 2265, "m_bEverPlayedOnTeam": 2108, "m_bFireBulletsSeedSynchronized": 2373, "m_bHasBeenControlledByPlayerThisRound": 2290, "m_bHasCommunicationAbuseMute": 2076, "m_bHasControlledBotThisRound": 2289, "m_bIsPlayerNameDirty": 2372, "m_bMvpNoMusic": 2354, "m_bPawnHasDefuser": 2320, "m_bPawnHasHelmet": 2321, "m_bPawnIsAlive": 2308, "m_bScoreReported": 2269, "m_eMvpReason": 2356, "m_eNetworkDisconnectionReason": 2260, "m_flForceTeamTime": 2100, "m_flPreviousForceJoinTeamTime": 2112, "m_hObserverPawn": 2304, "m_hOriginalControllerOfCurrentPawn": 2336, "m_hPlayerPawn": 2300, "m_iCoachingTeam": 2136, "m_iCompTeammateColor": 2104, "m_iCompetitiveRankType": 2168, "m_iCompetitiveRanking": 2160, "m_iCompetitiveRankingPredicted_Loss": 2176, "m_iCompetitiveRankingPredicted_Tie": 2180, "m_iCompetitiveRankingPredicted_Win": 2172, "m_iCompetitiveWins": 2164, "m_iDraftIndex": 2248, "m_iMVPs": 2368, "m_iMusicKitID": 2360, "m_iMusicKitMVPs": 2364, "m_iPawnArmor": 2316, "m_iPawnBotDifficulty": 2332, "m_iPawnHealth": 2312, "m_iPawnLifetimeEnd": 2328, "m_iPawnLifetimeStart": 2324, "m_iPendingTeamNum": 2096, "m_iPing": 2072, "m_iScore": 2340, "m_msQueuedModeDisconnectionTimestamp": 2252, "m_nBotsControlledThisRound": 2292, "m_nDisconnectionTick": 2272, "m_nEndMatchNextMapVote": 2184, "m_nFirstKill": 2352, "m_nKillCount": 2353, "m_nPawnCharacterDefIndex": 2322, "m_nPlayerDominated": 2144, "m_nPlayerDominatingMe": 2152, "m_nQuestProgressReason": 2196, "m_pActionTrackingServices": 2056, "m_pDamageServices": 2064, "m_pInGameMoneyServices": 2040, "m_pInventoryServices": 2048, "m_recentKillQueue": 2344, "m_rtActiveMissionPeriod": 2192, "m_sSanitizedPlayerName": 2128, "m_szClan": 2120, "m_szCrosshairCodes": 2088, "m_uiAbandonRecordedReason": 2256, "m_uiCommunicationMuteFlags": 2080, "m_unActiveQuestId": 2188, "m_unPlayerTvControlFlags": 2200}, "metadata": [{"name": "MNetworkUserGroupProxy", "type": "Unknown"}, {"name": "MNetworkUserGroupProxy", "type": "Unknown"}, {"name": "MNetworkUserGroupProxy", "type": "Unknown"}, {"name": "m_pInGameMoneyServices", "type": "NetworkVarNames", "type_name": "CCSPlayerController_InGameMoneyServices*"}, {"name": "m_pInventoryServices", "type": "NetworkVarNames", "type_name": "CCSPlayerController_InventoryServices*"}, {"name": "m_pActionTrackingServices", "type": "NetworkVarNames", "type_name": "CCSPlayerController_ActionTrackingServices*"}, {"name": "m_pDamageServices", "type": "NetworkVarNames", "type_name": "CCSPlayerController_DamageServices*"}, {"name": "m_iPing", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_bHasCommunicationAbuseMute", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_uiCommunicationMuteFlags", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_szCrosshairCodes", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iPendingTeamNum", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_flForceTeamTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_iCompTeammateColor", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bEverPlayedOnTeam", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_sz<PERSON>lan", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iCoachingTeam", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPlayerDominated", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_nPlayerDominatingMe", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_iCompetitiveRanking", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iCompetitiveWins", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iCompetitiveRankType", "type": "NetworkVarNames", "type_name": "int8"}, {"name": "m_iCompetitiveRankingPredicted_Win", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iCompetitiveRankingPredicted_Loss", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iCompetitiveRankingPredicted_Tie", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nEndMatchNextMapVote", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_unActiveQuestId", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_rtActiveMissionPeriod", "type": "NetworkVarNames", "type_name": "RTime32"}, {"name": "m_nQuestProgressReason", "type": "NetworkVarNames", "type_name": "QuestProgress::Reason"}, {"name": "m_unPlayerTvControlFlags", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nDisconnectionTick", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bControllingBot", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bHasControlledBotThisRound", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bCanControlObservedBot", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_hPlayerPawn", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_hObserverPawn", "type": "NetworkVarNames", "type_name": "CHandle<CCSObserverPawn>"}, {"name": "m_bPawnIsAlive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iPawnHealth", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_iPawnArmor", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bPawnHasDefuser", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bPawnHasHelmet", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nPawnCharacterDefIndex", "type": "NetworkVarNames", "type_name": "item_definition_index_t"}, {"name": "m_iPawnLifetimeStart", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iPawnLifetimeEnd", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iPawnBotDifficulty", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_hOriginalControllerOfCurrentPawn", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerController>"}, {"name": "m_iScore", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_recentKillQueue", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nFirstKill", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nKillCount", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_bMvpNoMusic", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_eMvpReason", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iMusicKitID", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iMusicKitMVPs", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iMVPs", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bFireBulletsSeedSynchronized", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "CBasePlayerController"}, "CCSPlayerController_ActionTrackingServices": {"fields": {"m_iNumRoundKills": 296, "m_iNumRoundKillsHeadshots": 300, "m_matchStats": 168, "m_perRoundStats": 64, "m_unTotalRoundDamageDealt": 304}, "metadata": [{"name": "m_perRoundStats", "type": "NetworkVarNames", "type_name": "CSPerRoundStats_t"}, {"name": "m_matchStats", "type": "NetworkVarNames", "type_name": "CSMatchStats_t"}, {"name": "m_iNumRoundKills", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iNumRoundKillsHeadshots", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_unTotalRoundDamageDealt", "type": "NetworkVarNames", "type_name": "uint32"}], "parent": "CPlayerControllerComponent"}, "CCSPlayerController_DamageServices": {"fields": {"m_DamageList": 72, "m_nSendUpdate": 64}, "metadata": [{"name": "m_nSendUpdate", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_DamageList", "type": "NetworkVarNames", "type_name": "CDamageRecord"}], "parent": "CPlayerControllerComponent"}, "CCSPlayerController_InGameMoneyServices": {"fields": {"m_iAccount": 64, "m_iCashSpentThisRound": 76, "m_iStartAccount": 68, "m_iTotalCashSpent": 72}, "metadata": [{"name": "m_iAccount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iStartAccount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iTotalCashSpent", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iCashSpentThisRound", "type": "NetworkVarNames", "type_name": "int"}], "parent": "CPlayerControllerComponent"}, "CCSPlayerController_InventoryServices": {"fields": {"m_nPersonaDataPublicCommendsFriendly": 104, "m_nPersonaDataPublicCommendsLeader": 96, "m_nPersonaDataPublicCommendsTeacher": 100, "m_nPersonaDataPublicLevel": 92, "m_nPersonaDataXpTrailLevel": 108, "m_rank": 68, "m_unMusicID": 64, "m_vecServerAuthoritativeWeaponSlots": 112}, "metadata": [{"name": "m_unMusicID", "type": "NetworkVarNames", "type_name": "item_definition_index_t"}, {"name": "m_rank", "type": "NetworkVarNames", "type_name": "MedalRank_t"}, {"name": "m_nPersonaDataPublicLevel", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPersonaDataPublicCommendsLeader", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPersonaDataPublicCommendsTeacher", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPersonaDataPublicCommendsFriendly", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPersonaDataXpTrailLevel", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vecServerAuthoritativeWeaponSlots", "type": "NetworkVarNames", "type_name": "ServerAuthoritativeWeaponSlot_t"}], "parent": "CPlayerControllerComponent"}, "CCSPlayer_ActionTrackingServices": {"fields": {"m_bIsRescuing": 68, "m_hLastWeaponBeforeC4AutoSwitch": 64, "m_weaponPurchasesThisMatch": 72, "m_weaponPurchasesThisRound": 184}, "metadata": [{"name": "m_bIsRescuing", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_weaponPurchasesThisMatch", "type": "NetworkVarNames", "type_name": "WeaponPurchaseTracker_t"}, {"name": "m_weaponPurchasesThisRound", "type": "NetworkVarNames", "type_name": "WeaponPurchaseTracker_t"}], "parent": "CPlayerPawnComponent"}, "CCSPlayer_BulletServices": {"fields": {"m_totalHitsOnServer": 64}, "metadata": [{"name": "m_totalHitsOnServer", "type": "NetworkVarNames", "type_name": "int32"}], "parent": "CPlayerPawnComponent"}, "CCSPlayer_BuyServices": {"fields": {"m_vecSellbackPurchaseEntries": 64}, "metadata": [{"name": "m_vecSellbackPurchaseEntries", "type": "NetworkVarNames", "type_name": "SellbackPurchaseEntry_t"}], "parent": "CPlayerPawnComponent"}, "CCSPlayer_CameraServices": {"fields": {"m_flDeathCamTilt": 672, "m_vClientScopeInaccuracy": 680}, "metadata": [], "parent": "CCSPlayerBase_CameraServices"}, "CCSPlayer_DamageReactServices": {"fields": {}, "metadata": [], "parent": "CPlayerPawnComponent"}, "CCSPlayer_GlowServices": {"fields": {}, "metadata": [], "parent": "CPlayerPawnComponent"}, "CCSPlayer_HostageServices": {"fields": {"m_hCarriedHostage": 64, "m_hCarriedHostageProp": 68}, "metadata": [{"name": "m_hCarriedHostage", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_hCarriedHostageProp", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}], "parent": "CPlayerPawnComponent"}, "CCSPlayer_ItemServices": {"fields": {"m_bHasDefuser": 64, "m_bHasHelmet": 65}, "metadata": [{"name": "m_bHasDefuser", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bHas<PERSON>elmet", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "CPlayer_ItemServices"}, "CCSPlayer_MovementServices": {"fields": {"m_StuckLast": 1236, "m_bDesiresDuck": 665, "m_bDuckOverride": 664, "m_bHasWalkMovedSinceLastJump": 705, "m_bInStuckTest": 706, "m_bOldJumpPressed": 1296, "m_bSpeedCropped": 1240, "m_bWasSurfing": 1356, "m_duckUntilOnGround": 704, "m_fStashGrenadeParameterWhen": 1304, "m_flAccumulatedJumpError": 1348, "m_flDuckAmount": 656, "m_flDuckOffset": 668, "m_flDuckSpeed": 660, "m_flGroundMoveEfficiency": 1244, "m_flHeightAtJumpStart": 1332, "m_flJumpPressedTime": 1300, "m_flLastDuckTime": 684, "m_flMaxJumpHeightLastJump": 1340, "m_flMaxJumpHeightThisJump": 1336, "m_flOffsetTickCompleteTime": 1320, "m_flOffsetTickStashedSpeed": 1324, "m_flStamina": 1328, "m_flStaminaAtJumpStart": 1344, "m_flTicksSinceLastSurfingDetected": 1352, "m_flWaterEntryTime": 1252, "m_nButtonDownMaskPrev": 1312, "m_nDuckJumpTimeMsecs": 676, "m_nDuckTimeMsecs": 672, "m_nGameCodeHasMovedPlayerAfterCommand": 1292, "m_nJumpTimeMsecs": 680, "m_nLadderSurfacePropIndex": 652, "m_nOldWaterLevel": 1248, "m_nTraceCount": 1232, "m_vecForward": 1256, "m_vecLadderNormal": 640, "m_vecLastPositionAtFullCrouchSpeed": 696, "m_vecLeft": 1268, "m_vecUp": 1280}, "metadata": [{"name": "m_vecLadderNormal", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nLadderSurfacePropIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flDuckAmount", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDuckSpeed", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bDuckOverride", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bDesiresDuck", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flDuckOffset", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nDuckTimeMsecs", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nDuckJumpTimeMsecs", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nJumpTimeMsecs", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_flLastDuckTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nGameCodeHasMovedPlayerAfterCommand", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bOldJumpPressed", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fStashGrenadeParameterWhen", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_nButtonDownMaskPrev", "type": "NetworkVarNames", "type_name": "ButtonBitMask_t"}, {"name": "m_flOffsetTickCompleteTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flOffsetTickStashedSpeed", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flStamina", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bWasSurfing", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "CPlayer_MovementServices_Humanoid"}, "CCSPlayer_PingServices": {"fields": {"m_hPlayerPing": 64}, "metadata": [{"name": "m_hPlayerPing", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}], "parent": "CPlayerPawnComponent"}, "CCSPlayer_UseServices": {"fields": {}, "metadata": [], "parent": "CPlayer_UseServices"}, "CCSPlayer_WaterServices": {"fields": {"m_flSwimSoundTime": 80, "m_flWaterJumpTime": 64, "m_vecWaterJumpVel": 68}, "metadata": [], "parent": "CPlayer_WaterServices"}, "CCSPlayer_WeaponServices": {"fields": {"m_bBlockInspectUntilNextGraphUpdate": 6728, "m_bIsHoldingLookAtWeapon": 205, "m_bIsLookingAtWeapon": 204, "m_flNextAttack": 200, "m_nOldTotalInputHistoryCount": 1256, "m_nOldTotalShootPositionHistoryCount": 208, "m_networkAnimTiming": 6704}, "metadata": [{"name": "m_flNextAttack", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bIsLookingAtWeapon", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsHoldingLookAtWeapon", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_networkAnimTiming", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_bBlockInspectUntilNextGraphUpdate", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "CPlayer_WeaponServices"}, "CCSPointScript": {"fields": {"m_pParent": 272}, "metadata": [{"name": "MPulseInstanceDomainInfo", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}, {"name": "MPulseDomainScopeInfo", "type": "Unknown"}, {"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseDomainOptInFeatureTag", "type": "Unknown"}], "parent": null}, "CCSPointScriptEntity": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "CCSPointScriptExtensions_CCSWeaponBaseVData": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}], "parent": null}, "CCSPointScriptExtensions_entity": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}], "parent": null}, "CCSPointScriptExtensions_observer": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}], "parent": null}, "CCSPointScriptExtensions_player": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}], "parent": null}, "CCSPointScriptExtensions_player_controller": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}], "parent": null}, "CCSPointScriptExtensions_weapon_cs_base": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}], "parent": null}, "CCSWeaponBaseVData": {"fields": {"m_DefaultLoadoutSlot": 1792, "m_GearSlot": 1784, "m_GearSlotPosition": 1788, "m_WeaponCategory": 1084, "m_WeaponType": 1080, "m_bCannotShootUnderwater": 1815, "m_bHasBurstMode": 1813, "m_bHideViewModelWhenZoomed": 2017, "m_bIsFullAuto": 1836, "m_bIsRevolver": 1814, "m_bMeleeWeapon": 1812, "m_bReloadsSingleShells": 1844, "m_bUnzoomsAfterShot": 2016, "m_eSilencerType": 1824, "m_flArmorRatio": 2072, "m_flAttackMovespeedFactor": 1996, "m_flCycleTime": 1848, "m_flDeployDuration": 1980, "m_flDisallowAttackAfterReloadStartDuration": 1984, "m_flFlinchVelocityModifierLarge": 2088, "m_flFlinchVelocityModifierSmall": 2092, "m_flHeadshotMultiplier": 2068, "m_flInaccuracyAltSoundThreshold": 2004, "m_flInaccuracyCrouch": 1872, "m_flInaccuracyFire": 1912, "m_flInaccuracyJump": 1888, "m_flInaccuracyJumpApex": 1972, "m_flInaccuracyJumpInitial": 1968, "m_flInaccuracyLadder": 1904, "m_flInaccuracyLand": 1896, "m_flInaccuracyMove": 1920, "m_flInaccuracyPitchShift": 2000, "m_flInaccuracyReload": 1976, "m_flInaccuracyStand": 1880, "m_flIronSightFOV": 2052, "m_flIronSightLooseness": 2060, "m_flIronSightPivotForward": 2056, "m_flIronSightPullUpSpeed": 2044, "m_flIronSightPutDownSpeed": 2048, "m_flMaxSpeed": 1856, "m_flPenetration": 2076, "m_flRange": 2080, "m_flRangeModifier": 2084, "m_flRecoilAngle": 1928, "m_flRecoilAngleVariance": 1936, "m_flRecoilMagnitude": 1944, "m_flRecoilMagnitudeVariance": 1952, "m_flRecoveryTimeCrouch": 2096, "m_flRecoveryTimeCrouchFinal": 2104, "m_flRecoveryTimeStand": 2100, "m_flRecoveryTimeStandFinal": 2108, "m_flSpread": 1864, "m_flThrowVelocity": 2120, "m_flZoomTime0": 2032, "m_flZoomTime1": 2036, "m_flZoomTime2": 2040, "m_nCrosshairDeltaDistance": 1832, "m_nCrosshairMinDistance": 1828, "m_nDamage": 2064, "m_nKillAward": 1800, "m_nNumBullets": 1840, "m_nPrice": 1796, "m_nPrimaryReserveAmmoMax": 1804, "m_nRecoilSeed": 1988, "m_nRecoveryTransitionEndBullet": 2116, "m_nRecoveryTransitionStartBullet": 2112, "m_nSecondaryReserveAmmoMax": 1808, "m_nSpreadSeed": 1992, "m_nTracerFrequency": 1960, "m_nZoomFOV1": 2024, "m_nZoomFOV2": 2028, "m_nZoomLevels": 2020, "m_szAnimClass": 2136, "m_szAnimSkeleton": 1312, "m_szModel_AG2": 1088, "m_szName": 1816, "m_szTracerParticle": 1560, "m_szUseRadioSubtitle": 2008, "m_vSmokeColor": 2124, "m_vecMuzzlePos0": 1536, "m_vecMuzzlePos1": 1548}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertySuppressBaseClassField", "type": "Unknown"}, {"name": "MPropertySuppressBaseClassField", "type": "Unknown"}], "parent": null}, "CCS_PortraitWorldCallbackHandler": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "CCitadelSoundOpvarSetOBB": {"fields": {"m_iszOperatorName": 1560, "m_iszOpvarName": 1568, "m_iszStackName": 1552, "m_nAABBDirection": 1624, "m_vDistanceInnerMaxs": 1588, "m_vDistanceInnerMins": 1576, "m_vDistanceOuterMaxs": 1612, "m_vDistanceOuterMins": 1600}, "metadata": [{"name": "m_iszStackName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iszOperatorName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iszOpvarName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_vDistanceInnerMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vDistanceInnerMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vDistanceOuterMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vDistanceOuterMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nAABBDirection", "type": "NetworkVarNames", "type_name": "int"}], "parent": "C_BaseEntity"}, "CCollisionProperty": {"fields": {"m_CollisionGroup": 94, "m_collisionAttribute": 16, "m_flBoundingRadius": 96, "m_flCapsuleRadius": 172, "m_nEnablePhysics": 95, "m_nSolidType": 91, "m_nSurroundType": 93, "m_triggerBloat": 92, "m_usSolidFlags": 90, "m_vCapsuleCenter1": 148, "m_vCapsuleCenter2": 160, "m_vecMaxs": 76, "m_vecMins": 64, "m_vecSpecifiedSurroundingMaxs": 112, "m_vecSpecifiedSurroundingMins": 100, "m_vecSurroundingMaxs": 124, "m_vecSurroundingMins": 136}, "metadata": [{"name": "m_collisionAttribute", "type": "NetworkVarNames", "type_name": "VPhysicsCollisionAttribute_t"}, {"name": "m_vecMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vecMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_usSolidFlags", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nSolidType", "type": "NetworkVarNames", "type_name": "SolidType_t"}, {"name": "m_triggerBloat", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nSurroundType", "type": "NetworkVarNames", "type_name": "SurroundingBoundsType_t"}, {"name": "m_CollisionGroup", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nEnablePhysics", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_vecSpecifiedSurroundingMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vecSpecifiedSurroundingMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vCapsuleCenter1", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vCapsuleCenter2", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_flCapsuleRadius", "type": "NetworkVarNames", "type_name": "float"}], "parent": ""}, "CDamageRecord": {"fields": {"m_DamagerXuid": 80, "m_PlayerDamager": 48, "m_PlayerRecipient": 52, "m_RecipientXuid": 88, "m_bIsOtherEnemy": 116, "m_hPlayerControllerDamager": 56, "m_hPlayerControllerRecipient": 60, "m_iActualHealthRemoved": 104, "m_iBulletsDamage": 96, "m_iDamage": 100, "m_iLastBulletUpdate": 112, "m_iNumHits": 108, "m_killType": 117, "m_szPlayerDamagerName": 64, "m_szPlayerRecipientName": 72}, "metadata": [{"name": "m_PlayerDamager", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_PlayerRecipient", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_hPlayerControllerDamager", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerController>"}, {"name": "m_hPlayerControllerRecipient", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerController>"}, {"name": "m_szPlayerDamagerName", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_szPlayerRecipientName", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_DamagerXuid", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_RecipientXuid", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_iDamage", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iActualHealthRemoved", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iNumHits", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iLastBulletUpdate", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bIsOtherEnemy", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_killType", "type": "NetworkVarNames", "type_name": "EKillTypes_t"}], "parent": null}, "CDestructiblePartsSystemComponent": {"fields": {"__m_pChainEntity": 0, "m_DamageLevelTakenByHitGroup": 72, "m_hOwner": 96, "m_nLastHitDamageLevel": 100}, "metadata": [{"name": "m_DamageLevelTakenByHitGroup", "type": "NetworkVarNames", "type_name": "uint16_t"}, {"name": "m_hOwner", "type": "NetworkVarNames", "type_name": "CHandle<CBaseModelEntity>"}, {"name": "m_nLastHitDamageLevel", "type": "NetworkVarNames", "type_name": "int"}], "parent": ""}, "CEconItemAttribute": {"fields": {"m_bSetBonus": 64, "m_flInitialValue": 56, "m_flValue": 52, "m_iAttributeDefinitionIndex": 48, "m_nRefundableCurrency": 60}, "metadata": [{"name": "m_iAttributeDefinitionIndex", "type": "NetworkVarNames", "type_name": "attrib_definition_index_t"}, {"name": "m_flValue", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flInitialValue", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nRefundableCurrency", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bSetBonus", "type": "NetworkVarNames", "type_name": "bool"}], "parent": null}, "CEffectData": {"fields": {"m_fFlags": 99, "m_flMagnitude": 68, "m_flRadius": 72, "m_flScale": 64, "m_hEntity": 56, "m_hOtherEntity": 60, "m_iEffectName": 108, "m_nAttachmentIndex": 100, "m_nAttachmentName": 104, "m_nColor": 98, "m_nDamageType": 88, "m_nEffectIndex": 80, "m_nExplosionType": 110, "m_nHitBox": 96, "m_nMaterial": 94, "m_nPenetrate": 92, "m_nSurfaceProp": 76, "m_vAngles": 44, "m_vNormal": 32, "m_vOrigin": 8, "m_vStart": 20}, "metadata": [{"name": "m_v<PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vStart", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vNormal", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vAngles", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_hEntity", "type": "NetworkVarNames", "type_name": "CEntityHandle"}, {"name": "m_hOtherEntity", "type": "NetworkVarNames", "type_name": "CEntityHandle"}, {"name": "m_flScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flMagnitude", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flRadius", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nSurfaceProp", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}, {"name": "m_nEffectIndex", "type": "NetworkVarNames", "type_name": "HParticleSystemDefinition"}, {"name": "m_nDamageType", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nPenetrate", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nMaterial", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_nHitBox", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_nColor", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_fFlags", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nAttachmentIndex", "type": "NetworkVarNames", "type_name": "AttachmentHandle_t"}, {"name": "m_nAttachmentName", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}, {"name": "m_iEffectName", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_nExplosionType", "type": "NetworkVarNames", "type_name": "uint8"}], "parent": null}, "CEntityComponent": {"fields": {}, "metadata": [], "parent": ""}, "CEntityIdentity": {"fields": {"m_PathIndex": 64, "m_designerName": 32, "m_fDataObjectTypes": 60, "m_flags": 48, "m_name": 24, "m_nameStringableIndex": 20, "m_pNext": 96, "m_pNextByClass": 112, "m_pPrev": 88, "m_pPrevByClass": 104, "m_worldGroupId": 56}, "metadata": [{"name": "m_nameStringableIndex", "type": "NetworkVarNames", "type_name": "int32"}], "parent": ""}, "CEntityInstance": {"fields": {"m_CScriptComponent": 48, "m_iszPrivateVScripts": 8, "m_pEntity": 16}, "metadata": [{"name": "m_pEntity", "type": "NetworkVarNames", "type_name": "CEntityIdentity*"}, {"name": "m_CScriptComponent", "type": "NetworkVarNames", "type_name": "CScriptComponent::Storage_t"}], "parent": null}, "CEnvSoundscape": {"fields": {"m_OnPlay": 1528, "m_bDisabled": 1668, "m_bOverrideWithEvent": 1584, "m_flRadius": 1568, "m_hProxySoundscape": 1664, "m_positionNames": 1600, "m_soundEventHash": 1680, "m_soundEventName": 1576, "m_soundscapeEntityListId": 1592, "m_soundscapeIndex": 1588, "m_soundscapeName": 1672}, "metadata": [], "parent": "C_BaseEntity"}, "CEnvSoundscapeAlias_snd_soundscape": {"fields": {}, "metadata": [], "parent": "CEnvSoundscape"}, "CEnvSoundscapeProxy": {"fields": {"m_MainSoundscapeName": 1688}, "metadata": [], "parent": "CEnvSoundscape"}, "CEnvSoundscapeProxyAlias_snd_soundscape_proxy": {"fields": {}, "metadata": [], "parent": "CEnvSoundscapeProxy"}, "CEnvSoundscapeTriggerable": {"fields": {}, "metadata": [], "parent": "CEnvSoundscape"}, "CEnvSoundscapeTriggerableAlias_snd_soundscape_triggerable": {"fields": {}, "metadata": [], "parent": "CEnvSoundscapeTriggerable"}, "CFilterAttributeInt": {"fields": {"m_sAttributeName": 1616}, "metadata": [], "parent": "CBaseFilter"}, "CFilterClass": {"fields": {"m_iFilterClass": 1616}, "metadata": [], "parent": "CBaseFilter"}, "CFilterLOS": {"fields": {}, "metadata": [], "parent": "CBaseFilter"}, "CFilterMassGreater": {"fields": {"m_fFilterMass": 1616}, "metadata": [], "parent": "CBaseFilter"}, "CFilterModel": {"fields": {"m_iFilterModel": 1616}, "metadata": [], "parent": "CBaseFilter"}, "CFilterMultiple": {"fields": {"m_hFilter": 1704, "m_iFilterName": 1624, "m_nFilterType": 1616}, "metadata": [], "parent": "CBaseFilter"}, "CFilterMultipleAPI": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}], "parent": null}, "CFilterName": {"fields": {"m_iFilterName": 1616}, "metadata": [], "parent": "CBaseFilter"}, "CFilterProximity": {"fields": {"m_flRadius": 1616}, "metadata": [], "parent": "CBaseFilter"}, "CFilterTeam": {"fields": {"m_iFilterTeam": 1616}, "metadata": [], "parent": "CBaseFilter"}, "CFuncWater": {"fields": {"m_BuoyancyHelper": 3776}, "metadata": [], "parent": "C_BaseModelEntity"}, "CGameSceneNode": {"fields": {"m_angAbsRotation": 220, "m_angRotation": 192, "m_bBoneMergeFlex": 0, "m_bDebugAbsOriginChanges": 238, "m_bDirtyBoneMergeBoneToRoot": 0, "m_bDirtyBoneMergeInfo": 0, "m_bDirtyHierarchy": 0, "m_bDormant": 239, "m_bForceParentToBeNetworked": 240, "m_bNetworkedAnglesChanged": 0, "m_bNetworkedPositionChanged": 0, "m_bNetworkedScaleChanged": 0, "m_bWillBeCallingPostDataUpdate": 0, "m_flAbsScale": 232, "m_flClientLocalScale": 320, "m_flScale": 204, "m_flZOffset": 316, "m_hParent": 120, "m_hierarchyAttachName": 312, "m_nDoNotSetAnimTimeInInvalidatePhysicsCount": 245, "m_nHierarchicalDepth": 243, "m_nHierarchyType": 244, "m_nLatchAbsOrigin": 0, "m_nParentAttachmentOrBone": 236, "m_name": 248, "m_nodeToWorld": 16, "m_pChild": 64, "m_pNextSibling": 72, "m_pOwner": 48, "m_pParent": 56, "m_vRenderOrigin": 324, "m_vecAbsOrigin": 208, "m_vecOrigin": 136}, "metadata": [{"name": "m_hParent", "type": "NetworkVarNames", "type_name": "CGameSceneNodeHandle"}, {"name": "m_vec<PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "CNetworkOriginCellCoordQuantizedVector"}, {"name": "m_angRotation", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_flScale", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_name", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}, {"name": "m_hierarchyAttachName", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}], "parent": ""}, "CGameSceneNodeHandle": {"fields": {"m_hOwner": 8, "m_name": 12}, "metadata": [{"name": "m_hOwner", "type": "NetworkVarNames", "type_name": "CEntityHandle"}, {"name": "m_name", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}], "parent": ""}, "CGlowProperty": {"fields": {"m_bEligibleForScreenHighlight": 80, "m_bFlashing": 68, "m_bGlowing": 81, "m_fGlowColor": 8, "m_flGlowStartTime": 76, "m_flGlowTime": 72, "m_glowColorOverride": 64, "m_iGlowTeam": 52, "m_iGlowType": 48, "m_nGlowRange": 56, "m_nGlowRangeMin": 60}, "metadata": [{"name": "m_iGlowType", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_iGlowTeam", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_nGlowRange", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_nGlowRangeMin", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_glowColorOverride", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_bFlashing", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flGlowTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flGlowStartTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bEligibleForScreenHighlight", "type": "NetworkVarNames", "type_name": "bool"}], "parent": ""}, "CGrenadeTracer": {"fields": {"m_flTracerDuration": 3800, "m_nType": 3804}, "metadata": [], "parent": "C_BaseModelEntity"}, "CHitboxComponent": {"fields": {"m_bvDisabledHitGroups": 36}, "metadata": [{"name": "m_bvDisabledHitGroups", "type": "NetworkVarNames", "type_name": "uint32"}], "parent": "CEntityComponent"}, "CHostageRescueZone": {"fields": {}, "metadata": [], "parent": "CHostageRescueZoneShim"}, "CHostageRescueZoneShim": {"fields": {}, "metadata": [], "parent": "C_BaseTrigger"}, "CInfoDynamicShadowHint": {"fields": {"m_bDisabled": 1528, "m_flRange": 1532, "m_hLight": 1544, "m_nImportance": 1536, "m_nLightChoice": 1540}, "metadata": [], "parent": "C_PointEntity"}, "CInfoDynamicShadowHintBox": {"fields": {"m_vBoxMaxs": 1564, "m_vBoxMins": 1552}, "metadata": [], "parent": "CInfoDynamicShadowHint"}, "CInfoFan": {"fields": {"m_FanForceCurveString": 1608, "m_fFanForceMaxRadius": 1592, "m_fFanForceMinRadius": 1596, "m_flCurveDistRange": 1600}, "metadata": [{"name": "m_fFanForceMaxRadius", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fFanForceMinRadius", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flCurveDistRange", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_FanForceCurveString", "type": "NetworkVarNames", "type_name": "string_t"}], "parent": "C_PointEntity"}, "CInfoOffscreenPanoramaTexture": {"fields": {"m_RenderAttrName": 1552, "m_TargetEntities": 1560, "m_bCheckCSSClasses": 1968, "m_bDisabled": 1528, "m_nResolutionX": 1532, "m_nResolutionY": 1536, "m_nTargetChangeCount": 1584, "m_szLayoutFileName": 1544, "m_vecCSSClasses": 1592}, "metadata": [{"name": "m_bDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nResolutionX", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nResolutionY", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_szLayoutFileName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_RenderAttrName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_TargetEntities", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseModelEntity>"}, {"name": "m_nTargetChangeCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vecCSSClasses", "type": "NetworkVarNames", "type_name": "string_t"}], "parent": "C_PointEntity"}, "CInfoParticleTarget": {"fields": {}, "metadata": [], "parent": "C_PointEntity"}, "CInfoTarget": {"fields": {}, "metadata": [], "parent": "C_PointEntity"}, "CInfoWorldLayer": {"fields": {"m_bCreateAsChildSpawnGroup": 1586, "m_bEntitiesSpawned": 1585, "m_bWorldLayerActuallyVisible": 1592, "m_bWorldLayerVisible": 1584, "m_hLayerSpawnGroup": 1588, "m_layerName": 1576, "m_pOutputOnEntitiesSpawned": 1528, "m_worldName": 1568}, "metadata": [{"name": "m_worldName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_layerName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_bWorldLayerVisible", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bEntitiesSpawned", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "CLightComponent": {"fields": {"__m_pChainEntity": 48, "m_Color": 109, "m_Pattern": 208, "m_SecondaryColor": 113, "m_SkyAmbientBounce": 396, "m_SkyColor": 388, "m_bAllowSSTGeneration": 281, "m_bEnabled": 308, "m_bFlicker": 309, "m_bMixedShadows": 401, "m_bPrecomputedFieldsValid": 310, "m_bRenderDiffuse": 184, "m_bRenderToCubemaps": 280, "m_bRenderTransmissive": 192, "m_bUseSecondaryColor": 400, "m_bUsesBakedShadowing": 260, "m_flAttenuation0": 140, "m_flAttenuation1": 144, "m_flAttenuation2": 148, "m_flBrightness": 120, "m_flBrightnessMult": 128, "m_flBrightnessScale": 124, "m_flCapsuleLength": 408, "m_flFadeMaxDist": 296, "m_flFadeMinDist": 292, "m_flFalloff": 136, "m_flFogContributionStength": 380, "m_flLightStyleStartTime": 404, "m_flMinRoughness": 412, "m_flNearClipPlane": 384, "m_flOrthoLightHeight": 200, "m_flOrthoLightWidth": 196, "m_flPhi": 156, "m_flPrecomputedMaxRange": 372, "m_flRange": 132, "m_flShadowCascadeCrossFade": 220, "m_flShadowCascadeDistance0": 228, "m_flShadowCascadeDistance1": 232, "m_flShadowCascadeDistance2": 236, "m_flShadowCascadeDistance3": 240, "m_flShadowCascadeDistanceFade": 224, "m_flShadowFadeMaxDist": 304, "m_flShadowFadeMinDist": 300, "m_flSkyIntensity": 392, "m_flTheta": 152, "m_hLightCookie": 160, "m_nBakedShadowIndex": 268, "m_nCascadeRenderStaticObjects": 216, "m_nCascades": 168, "m_nCastShadows": 172, "m_nDirectLight": 284, "m_nFogLightingMode": 376, "m_nIndirectLight": 288, "m_nLightMapUniqueId": 276, "m_nLightPathUniqueId": 272, "m_nRenderSpecular": 188, "m_nShadowCascadeResolution0": 244, "m_nShadowCascadeResolution1": 248, "m_nShadowCascadeResolution2": 252, "m_nShadowCascadeResolution3": 256, "m_nShadowHeight": 180, "m_nShadowPriority": 264, "m_nShadowWidth": 176, "m_nStyle": 204, "m_vPrecomputedBoundsMaxs": 324, "m_vPrecomputedBoundsMins": 312, "m_vPrecomputedOBBAngles": 348, "m_vPrecomputedOBBExtent": 360, "m_vPrecomputedOBBOrigin": 336}, "metadata": [{"name": "m_Color", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_SecondaryColor", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_flBrightness", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flBrightnessScale", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flBrightnessMult", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flRange", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fl<PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flAttenuation0", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flAttenuation1", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flAttenuation2", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flTheta", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flPhi", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_hLightCookie", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_nCascades", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nCastShadows", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nShadowWidth", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nShadowHeight", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bRenderDiffuse", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nRenderSpecular", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bRenderTransmissive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flOrthoLightWidth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flOrthoLightHeight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nStyle", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_<PERSON>tern", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_nCascadeRenderStaticObjects", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flShadowCascadeCrossFade", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowCascadeDistanceFade", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowCascadeDistance0", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowCascadeDistance1", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowCascadeDistance2", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowCascadeDistance3", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nShadowCascadeResolution0", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nShadowCascadeResolution1", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nShadowCascadeResolution2", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nShadowCascadeResolution3", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bUsesBakedShadowing", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nShadowPriority", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nBakedShadowIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nLightPathUniqueId", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_nLightMapUniqueId", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_bRenderToCubemaps", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bAllowSSTGeneration", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nDirectLight", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nIndirectLight", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFadeMinDist", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFadeMaxDist", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowFadeMinDist", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowFadeMaxDist", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bFlicker", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bPrecomputed<PERSON><PERSON>sValid", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vPrecomputedBoundsMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedBoundsMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBOrigin", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBAngles", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_vPrecomputedOBBExtent", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_flPrecomputedMaxRange", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nFogLightingMode", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFogContributionStength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flNearClipPlane", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_SkyColor", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_flSkyIntensity", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_SkyAmbientBounce", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_bUseSecondaryColor", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bMixedShadows", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flLightStyleStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flCapsuleLength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flMinRoughness", "type": "NetworkVarNames", "type_name": "float"}], "parent": "CEntityComponent"}, "CLogicRelay": {"fields": {"m_bDisabled": 1528, "m_bFastRetrigger": 1531, "m_bPassthoughCaller": 1532, "m_bTriggerOnce": 1530, "m_bWaitForRefire": 1529}, "metadata": [], "parent": "CLogicalEntity"}, "CLogicRelayAPI": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}], "parent": null}, "CLogicalEntity": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "CMapInfo": {"fields": {"m_bDisableAutoGeneratedDMSpawns": 1541, "m_bFadePlayerVisibilityFarZ": 1552, "m_bRainTraceToSkyEnabled": 1553, "m_bUseNormalSpawnsForDM": 1540, "m_flBombRadius": 1532, "m_flBotMaxVisionDistance": 1544, "m_iBuyingStatus": 1528, "m_iHostageCount": 1548, "m_iPetPopulation": 1536}, "metadata": [], "parent": "C_PointEntity"}, "CModelState": {"fields": {"m_MeshGroupMask": 592, "m_ModelName": 216, "m_bClientClothCreationSuppressed": 425, "m_hModel": 208, "m_nBodyGroupChoices": 672, "m_nClothUpdateFlags": 748, "m_nForceLOD": 747, "m_nIdealMotionType": 746}, "metadata": [{"name": "m_hModel", "type": "NetworkVarNames", "type_name": "HModelStrong"}, {"name": "m_bClientClothCreationSuppressed", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_MeshGroupMask", "type": "NetworkVarNames", "type_name": "MeshGroupMask_t"}, {"name": "m_nBodyGroupChoices", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_nIdealMotionType", "type": "NetworkVarNames", "type_name": "int8"}], "parent": ""}, "CNetworkedSequenceOperation": {"fields": {"m_bDiscontinuity": 29, "m_bSequenceChangeNetworked": 28, "m_flCycle": 16, "m_flPrevCycle": 12, "m_flPrevCycleForAnimEventDetection": 36, "m_flPrevCycleFromDiscontinuity": 32, "m_flWeight": 20, "m_hSequence": 8}, "metadata": [{"name": "m_hSequence", "type": "NetworkVarNames", "type_name": "HSequence"}, {"name": "m_flPrevCycle", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flCycle", "type": "NetworkVarNames", "type_name": "float32"}], "parent": ""}, "CPathQueryComponent": {"fields": {}, "metadata": [], "parent": "CEntityComponent"}, "CPathSimple": {"fields": {"m_CPathQueryComponent": 1536, "m_bClosedLoop": 1784, "m_pathString": 1776}, "metadata": [{"name": "m_CPathQueryComponent", "type": "NetworkVarNames", "type_name": "CPathQueryComponent::Storage_t"}, {"name": "m_pathString", "type": "NetworkVarNames", "type_name": "CUtlString"}], "parent": "C_BaseEntity"}, "CPathSimpleAPI": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}], "parent": null}, "CPlayer_AutoaimServices": {"fields": {}, "metadata": [], "parent": "CPlayerPawnComponent"}, "CPlayer_CameraServices": {"fields": {"m_CurrentFog": 320, "m_OverrideFogColor": 433, "m_PlayerFog": 88, "m_PostProcessingVolumes": 288, "m_angDemoViewAngles": 504, "m_audio": 168, "m_bOverrideFogColor": 428, "m_bOverrideFogStartEnd": 453, "m_fOverrideFogEnd": 480, "m_fOverrideFogStart": 460, "m_flCsViewPunchAngleTickRatio": 80, "m_flOldPlayerViewOffsetZ": 316, "m_flOldPlayerZ": 312, "m_hActivePostProcessingVolume": 500, "m_hColorCorrectionCtrl": 152, "m_hOldFogController": 424, "m_hTonemapController": 160, "m_hViewEntity": 156, "m_nCsViewPunchAngleTick": 76, "m_vecCsViewPunchAngle": 64}, "metadata": [{"name": "m_vecCsViewPunchAngle", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_nCsViewPunchAngleTick", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "m_flCsViewPunchAngleTickRatio", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_PlayerFog", "type": "NetworkVarNames", "type_name": "fogplayerparams_t"}, {"name": "m_hColorCorrectionCtrl", "type": "NetworkVarNames", "type_name": "CHandle<CColorCorrection>"}, {"name": "m_hViewEntity", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_hTonemapController", "type": "NetworkVarNames", "type_name": "CHandle<CTonemapController2>"}, {"name": "m_audio", "type": "NetworkVarNames", "type_name": "audioparams_t"}, {"name": "m_PostProcessingVolumes", "type": "NetworkVarNames", "type_name": "CHandle<C_PostProcessingVolume>"}], "parent": "CPlayerPawnComponent"}, "CPlayer_FlashlightServices": {"fields": {}, "metadata": [], "parent": "CPlayerPawnComponent"}, "CPlayer_ItemServices": {"fields": {}, "metadata": [], "parent": "CPlayerPawnComponent"}, "CPlayer_MovementServices": {"fields": {"m_arrForceSubtickMoveWhen": 412, "m_flForwardMove": 428, "m_flLeftMove": 432, "m_flMaxspeed": 408, "m_flUpMove": 436, "m_nButtonDoublePressed": 120, "m_nButtons": 72, "m_nImpulse": 64, "m_nLastCommandNumberProcessed": 384, "m_nQueuedButtonChangeMask": 112, "m_nQueuedButtonDownMask": 104, "m_nToggleButtonDownMask": 392, "m_pButtonPressedCmdNumber": 128, "m_vecLastFinishTickViewAngles": 544, "m_vecLastMovementImpulses": 440, "m_vecOldViewAngles": 556}, "metadata": [{"name": "m_nToggleButtonDownMask", "type": "NetworkVarNames", "type_name": "ButtonBitMask_t"}, {"name": "m_flMaxspeed", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_arrForceSubtickMoveWhen", "type": "NetworkVarNames", "type_name": "float32"}], "parent": "CPlayerPawnComponent"}, "CPlayer_MovementServices_Humanoid": {"fields": {"m_bDucked": 596, "m_bDucking": 597, "m_bInCrouch": 584, "m_bInDuckJump": 598, "m_flCrouchTransitionStartTime": 592, "m_flFallVelocity": 580, "m_flStepSoundTime": 576, "m_flSurfaceFriction": 612, "m_groundNormal": 600, "m_nCrouchState": 588, "m_nStepside": 632, "m_surfaceProps": 616}, "metadata": [{"name": "m_flFallVelocity", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_bIn<PERSON>rouch", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nCrouchState", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_flCrouchTransitionStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bDucked", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bDucking", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bInDuckJump", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "CPlayer_MovementServices"}, "CPlayer_ObserverServices": {"fields": {"m_bForcedObserverMode": 76, "m_flObserverChaseDistance": 80, "m_flObserverChaseDistanceCalcTime": 84, "m_hObserverTarget": 68, "m_iObserverLastMode": 72, "m_iObserverMode": 64}, "metadata": [{"name": "m_iObserverMode", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_hObserverTarget", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}], "parent": "CPlayerPawnComponent"}, "CPlayer_UseServices": {"fields": {}, "metadata": [], "parent": "CPlayerPawnComponent"}, "CPlayer_WaterServices": {"fields": {}, "metadata": [], "parent": "CPlayerPawnComponent"}, "CPlayer_WeaponServices": {"fields": {"m_hActiveWeapon": 88, "m_hLastWeapon": 92, "m_hMyWeapons": 64, "m_iAmmo": 96}, "metadata": [{"name": "m_hMyWeapons", "type": "NetworkVarNames", "type_name": "CHandle<C_BasePlayerWeapon>"}, {"name": "m_hActiveWeapon", "type": "NetworkVarNames", "type_name": "CHandle<CBasePlayerWeapon>"}, {"name": "m_hLastWeapon", "type": "NetworkVarNames", "type_name": "CHandle<CBasePlayerWeapon>"}, {"name": "m_iAmmo", "type": "NetworkVarNames", "type_name": "uint16"}], "parent": "CPlayerPawnComponent"}, "CPointChildModifier": {"fields": {"m_bOrphanInsteadOfDeletingChildrenOnRemove": 1528}, "metadata": [], "parent": "C_PointEntity"}, "CPointOffScreenIndicatorUi": {"fields": {"m_bBeenEnabled": 4384, "m_bHide": 4385, "m_flSeenTargetTime": 4388, "m_pTargetPanel": 4392}, "metadata": [], "parent": "C_PointClientUIWorldPanel"}, "CPointOrient": {"fields": {"m_bActive": 1540, "m_flLastGameTime": 1556, "m_flMaxTurnRate": 1552, "m_hTarget": 1536, "m_iszSpawnTargetName": 1528, "m_nConstraint": 1548, "m_nGoalDirection": 1544}, "metadata": [], "parent": "C_BaseEntity"}, "CPointTemplate": {"fields": {"m_ScriptCallbackScope": 1624, "m_ScriptSpawnCallback": 1616, "m_SpawnedEntityHandles": 1592, "m_bAsynchronouslySpawnEntities": 1556, "m_clientOnlyEntityBehavior": 1560, "m_createdSpawnGroupHandles": 1568, "m_flTimeoutInterval": 1552, "m_iszEntityFilterName": 1544, "m_iszSource2EntityLumpName": 1536, "m_iszWorldName": 1528, "m_ownerSpawnGroupType": 1564}, "metadata": [], "parent": "CLogicalEntity"}, "CPointTemplateAPI": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}], "parent": null}, "CPrecipitationVData": {"fields": {"m_bBatchSameVolumeType": 272, "m_flInnerDistance": 264, "m_nAttachType": 268, "m_nRTEnvCP": 276, "m_nRTEnvCPComponent": 280, "m_szModifier": 288, "m_szParticlePrecipitationEffect": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CProjectedTextureBase": {"fields": {"m_LightColor": 36, "m_SpotlightTextureName": 84, "m_bAlwaysUpdate": 17, "m_bCameraSpace": 28, "m_bEnableShadows": 24, "m_bFlipHorizontal": 620, "m_bLightOnlyTarget": 26, "m_bLightWorld": 27, "m_bSimpleProjection": 25, "m_bState": 16, "m_bVolumetric": 52, "m_flAmbient": 80, "m_flBrightnessScale": 32, "m_flColorTransitionTime": 76, "m_flFarZ": 608, "m_flFlashlightTime": 64, "m_flIntensity": 40, "m_flLightFOV": 20, "m_flLinearAttenuation": 44, "m_flNearZ": 604, "m_flNoiseStrength": 60, "m_flPlaneOffset": 72, "m_flProjectionSize": 612, "m_flQuadraticAttenuation": 48, "m_flRotation": 616, "m_flVolumetricIntensity": 56, "m_hTargetEntity": 12, "m_nNumPlanes": 68, "m_nShadowQuality": 600, "m_nSpotlightTextureFrame": 596}, "metadata": [{"name": "m_hTargetEntity", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseEntity>"}, {"name": "m_bState", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bAlwaysUpdate", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flLightFOV", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_bEnableShadows", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bSimpleProjection", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bLightOnlyTarget", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bLightWorld", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bCameraSpace", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flBrightnessScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_LightColor", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_flIntensity", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flLinearAttenuation", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flQuadraticAttenuation", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_bVolumetric", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flVolumetricIntensity", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flNoiseStrength", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFlashlightTime", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nNumPlanes", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_flPlaneOffset", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flColorTransitionTime", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flAmbient", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_SpotlightTextureName", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_nSpotlightTextureFrame", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_nShadowQuality", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_flNearZ", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFarZ", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flProjectionSize", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flRotation", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_bFlipHorizontal", "type": "NetworkVarNames", "type_name": "bool"}], "parent": null}, "CPropDataComponent": {"fields": {"m_bSpawnMotionDisabled": 52, "m_flDmgModBullet": 16, "m_flDmgModClub": 20, "m_flDmgModExplosive": 24, "m_flDmgModFire": 28, "m_iszBasePropData": 40, "m_iszPhysicsDamageTableName": 32, "m_nDisableTakePhysicsDamageSpawnFlag": 56, "m_nInteractions": 48, "m_nMotionDisabledSpawnFlag": 60}, "metadata": [], "parent": "CEntityComponent"}, "CPulseAnimFuncs": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}], "parent": null}, "CPulseArraylib": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPropertyDescription", "type": "Unknown"}], "parent": null}, "CPulseCell_Base": {"fields": {"m_nEditorNodeID": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulseCell_BaseFlow": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulseCell_BaseLerp": {"fields": {"m_WakeResume": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulseCell_BaseLerp__CursorState_t": {"fields": {"m_EndTime": 4, "m_StartTime": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulseCell_BaseRequirement": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulseCell_BaseState": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPulseEditorHeaderIcon", "type": "Unknown"}], "parent": null}, "CPulseCell_BaseValue": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulseCell_BaseYieldingInflow": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulseCell_BooleanSwitchState": {"fields": {"m_Condition": 72, "m_SubGraph": 192, "m_WhenFalse": 336, "m_WhenTrue": 264}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}, {"name": "MPropertyDescription", "type": "Unknown"}, {"name": "MPulseEditorCanvasItemSpecKV3", "type": "Unknown"}], "parent": null}, "CPulseCell_CursorQueue": {"fields": {"m_nCursorsAllowedToRunParallel": 152}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}, {"name": "MPropertyDescription", "type": "Unknown"}, {"name": "MPulseEditorHeaderIcon", "type": "Unknown"}], "parent": null}, "CPulseCell_FireCursors": {"fields": {"m_OnCanceled": 176, "m_OnFinished": 104, "m_Outflows": 72, "m_bWaitForChildOutflows": 96}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_Inflow_BaseEntrypoint": {"fields": {"m_EntryChunk": 72, "m_RegisterMap": 80}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulseCell_Inflow_EntOutputHandler": {"fields": {"m_ExpectedParamType": 160, "m_SourceEntity": 128, "m_SourceOutput": 144}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_Inflow_EventHandler": {"fields": {"m_EventName": 128}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_Inflow_GraphHook": {"fields": {"m_HookName": 128}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_Inflow_Method": {"fields": {"m_Args": 184, "m_Description": 144, "m_MethodName": 128, "m_ReturnType": 160, "m_bIsPublic": 152}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_Inflow_ObservableVariableListener": {"fields": {"m_bSelfReference": 130, "m_nBlackboardReference": 128}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_Inflow_Wait": {"fields": {"m_WakeResume": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}, {"name": "MPropertyDescription", "type": "Unknown"}, {"name": "MPulseEditorHeaderIcon", "type": "Unknown"}, {"name": "MPulseEditorCanvasItemSpecKV3", "type": "Unknown"}], "parent": null}, "CPulseCell_Inflow_Yield": {"fields": {"m_UnyieldResume": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_InlineNodeSkipSelector": {"fields": {"m_FailOutflow": 104, "m_PassOutflow": 80, "m_bAnd": 76, "m_nFlowNodeID": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}, {"name": "MPulseSelectorAllowRequirementCriteria", "type": "Unknown"}, {"name": "MPulseSelectorAllowRequirementCriteria", "type": "Unknown"}], "parent": null}, "CPulseCell_IntervalTimer": {"fields": {"m_Completed": 72, "m_OnInterval": 144}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}, {"name": "MPropertyDescription", "type": "Unknown"}, {"name": "MPulseEditorHeaderIcon", "type": "Unknown"}], "parent": null}, "CPulseCell_IntervalTimer__CursorState_t": {"fields": {"m_EndTime": 4, "m_StartTime": 0, "m_bCompleteOnNextWake": 16, "m_flWaitInterval": 8, "m_flWaitIntervalHigh": 12}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulseCell_IsRequirementValid": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_IsRequirementValid__Criteria_t": {"fields": {"m_bIsValid": 0}, "metadata": [], "parent": null}, "CPulseCell_LerpCameraSettings": {"fields": {"m_End": 164, "m_Start": 148, "m_flSeconds": 144}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_LerpCameraSettings__CursorState_t": {"fields": {"m_OverlaidEnd": 28, "m_OverlaidStart": 12, "m_hCamera": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulseCell_LimitCount": {"fields": {"m_nLimitCount": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}, {"name": "MPropertyDescription", "type": "Unknown"}, {"name": "MPulseRequirementPass", "type": "Unknown"}, {"name": "MPulseRequirementSummaryExpr", "type": "Unknown"}], "parent": null}, "CPulseCell_LimitCount__Criteria_t": {"fields": {"m_bLimitCountPasses": 0}, "metadata": [], "parent": null}, "CPulseCell_LimitCount__InstanceState_t": {"fields": {"m_nCurrentCount": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulseCell_Outflow_CycleOrdered": {"fields": {"m_Outputs": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_Outflow_CycleOrdered__InstanceState_t": {"fields": {"m_nNextIndex": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulseCell_Outflow_CycleRandom": {"fields": {"m_Outputs": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_Outflow_CycleShuffled": {"fields": {"m_Outputs": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_Outflow_CycleShuffled__InstanceState_t": {"fields": {"m_Shuffle": 0, "m_nNextShuffle": 32}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulseCell_Outflow_IntSwitch": {"fields": {"m_CaseOutflows": 144, "m_DefaultCaseOutflow": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_Outflow_StringSwitch": {"fields": {"m_CaseOutflows": 144, "m_DefaultCaseOutflow": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_PickBestOutflowSelector": {"fields": {"m_OutflowList": 80, "m_nCheckType": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}, {"name": "MPropertyDescription", "type": "Unknown"}, {"name": "MPulseEditorHeaderIcon", "type": "Unknown"}, {"name": "MPulseEditorCanvasItemSpecKV3", "type": "Unknown"}, {"name": "MPulseSelectorAllowRequirementCriteria", "type": "Unknown"}, {"name": "MPulseSelectorAllowRequirementCriteria", "type": "Unknown"}], "parent": null}, "CPulseCell_PlaySequence": {"fields": {"m_OnCanceled": 176, "m_OnFinished": 104, "m_PulseAnimEvents": 80, "m_SequenceName": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}, {"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}, {"name": "MPropertyDescription", "type": "Unknown"}], "parent": null}, "CPulseCell_PlaySequence__CursorState_t": {"fields": {"m_hTarget": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulseCell_Step_CallExternalMethod": {"fields": {"m_ExpectedArgs": 104, "m_GameBlackboard": 88, "m_MethodName": 72, "m_OnFinished": 128, "m_nAsyncCallMode": 120}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_Step_DebugLog": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_Step_EntFire": {"fields": {"m_Input": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_Step_PublicOutput": {"fields": {"m_OutputIndex": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_Timeline": {"fields": {"m_OnCanceled": 176, "m_OnFinished": 104, "m_TimelineEvents": 72, "m_bWaitForChildOutflows": 96}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}], "parent": null}, "CPulseCell_Timeline__TimelineEvent_t": {"fields": {"m_EventOutflow": 8, "m_flTimeFromPrevious": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulseCell_Unknown": {"fields": {"m_UnknownKeys": 72}, "metadata": [], "parent": null}, "CPulseCell_Value_Curve": {"fields": {"m_Curve": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": null}, "CPulseCell_Value_Gradient": {"fields": {"m_Gradient": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}], "parent": null}, "CPulseCell_Value_RandomFloat": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}, {"name": "MPropertyDescription", "type": "Unknown"}, {"name": "MPulseEditorHeaderIcon", "type": "Unknown"}], "parent": null}, "CPulseCell_Value_RandomInt": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}, {"name": "MPropertyDescription", "type": "Unknown"}, {"name": "MPulseEditorHeaderIcon", "type": "Unknown"}], "parent": null}, "CPulseCell_WaitForCursorsWithTag": {"fields": {"m_bTagSelfWhenComplete": 152, "m_nDesiredKillPriority": 156}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MCellForDomain", "type": "Unknown"}, {"name": "MPulseCellMethodBindings", "type": "Unknown"}, {"name": "MPulseCellOutflowHookInfo", "type": "Unknown"}, {"name": "MProper<PERSON>FriendlyName", "type": "Unknown"}, {"name": "MPropertyDescription", "type": "Unknown"}, {"name": "MPulseEditorHeaderIcon", "type": "Unknown"}], "parent": null}, "CPulseCell_WaitForCursorsWithTagBase": {"fields": {"m_WaitComplete": 80, "m_nCursorsAllowedToWait": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPulseEditorCanvasItemSpecKV3", "type": "Unknown"}], "parent": null}, "CPulseCell_WaitForCursorsWithTagBase__CursorState_t": {"fields": {"m_TagName": 0}, "metadata": [], "parent": null}, "CPulseCursorFuncs": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPropertyDescription", "type": "Unknown"}], "parent": null}, "CPulseExecCursor": {"fields": {}, "metadata": [], "parent": null}, "CPulseGraphDef": {"fields": {"m_BlackboardReferences": 272, "m_CallInfos": 200, "m_Cells": 104, "m_Chunks": 80, "m_Constants": 224, "m_DomainIdentifier": 8, "m_DomainSubType": 24, "m_DomainValues": 248, "m_InvokeBindings": 176, "m_OutputConnections": 296, "m_ParentMapName": 48, "m_ParentXmlName": 64, "m_PublicOutputs": 152, "m_Vars": 128}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulseMathlib": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPropertyDescription", "type": "Unknown"}], "parent": null}, "CPulseRuntimeLibModuleLocalTypeQueryRegistration": {"fields": {}, "metadata": [{"name": "MPulseTypeQueriesForScopeSingleton", "type": "Unknown"}], "parent": null}, "CPulseTestScriptLib": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPropertyDescription", "type": "Unknown"}], "parent": null}, "CPulse_BlackboardReference": {"fields": {"m_BlackboardResource": 8, "m_NodeName": 32, "m_hBlackboardResource": 0, "m_nNodeID": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulse_CallInfo": {"fields": {"m_CallMethodID": 72, "m_PortName": 0, "m_RegisterMap": 24, "m_nEditorNodeID": 16, "m_nSrcChunk": 76, "m_nSrcInstruction": 80}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulse_InvokeBinding": {"fields": {"m_FuncName": 48, "m_RegisterMap": 0, "m_nCellIndex": 64, "m_nSrcChunk": 68, "m_nSrcInstruction": 72}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPulse_OutflowConnection": {"fields": {"m_OutflowRegisterMap": 24, "m_SourceOutflowName": 0, "m_nDestChunk": 16, "m_nInstruction": 20}, "metadata": [], "parent": null}, "CPulse_ResumePoint": {"fields": {}, "metadata": [], "parent": null}, "CRagdollManager": {"fields": {"m_iCurrentMaxRagdollCount": 1528}, "metadata": [{"name": "m_iCurrentMaxRagdollCount", "type": "NetworkVarNames", "type_name": "int8"}], "parent": "C_BaseEntity"}, "CRenderComponent": {"fields": {"__m_pChainEntity": 16, "m_bEnableRendering": 88, "m_bInterpolationReadyToDraw": 168, "m_bIsRenderingWithViewModels": 80, "m_nSplitscreenFlags": 84}, "metadata": [], "parent": "CEntityComponent"}, "CSMatchStats_t": {"fields": {"m_iEnemy3Ks": 112, "m_iEnemy4Ks": 108, "m_iEnemy5Ks": 104, "m_iEnemyKnifeKills": 116, "m_iEnemyTaserKills": 120}, "metadata": [{"name": "m_iEnemy5Ks", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iEnemy4Ks", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iEnemy3Ks", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iEnemyKnifeKills", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iEnemyTaserKills", "type": "NetworkVarNames", "type_name": "int"}], "parent": null}, "CSPerRoundStats_t": {"fields": {"m_iAssists": 56, "m_iCashEarned": 88, "m_iDamage": 60, "m_iDeaths": 52, "m_iEnemiesFlashed": 96, "m_iEquipmentValue": 64, "m_iHeadShotKills": 80, "m_iKillReward": 72, "m_iKills": 48, "m_iLiveTime": 76, "m_iMoneySaved": 68, "m_iObjective": 84, "m_iUtilityDamage": 92}, "metadata": [{"name": "m_i<PERSON><PERSON>s", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iDeaths", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iAssists", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iDamage", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iEquipmentValue", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iMoneySaved", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iKillReward", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iLiveTime", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iHeadShotKills", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iObjective", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iCashEarned", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iUtilityDamage", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iEnemiesFlashed", "type": "NetworkVarNames", "type_name": "int"}], "parent": null}, "CScriptComponent": {"fields": {"m_scriptClassName": 48}, "metadata": [], "parent": "CEntityComponent"}, "CServerOnlyModelEntity": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "CSharedPulseTypeQueryRegistration": {"fields": {}, "metadata": [{"name": "MPulseTypeQueriesForScopeSingleton", "type": "Unknown"}], "parent": null}, "CSkeletonInstance": {"fields": {"m_bDirtyMotionType": 0, "m_bDisableSolidCollisionsForHierarchy": 1138, "m_bIsAnimationEnabled": 1136, "m_bIsGeneratingLatchedParentSpaceState": 0, "m_bUseParentRenderBounds": 1137, "m_materialGroup": 1140, "m_modelState": 368, "m_nHitboxSet": 1144}, "metadata": [{"name": "m_modelState", "type": "NetworkVarNames", "type_name": "CModelState"}, {"name": "m_bIsAnimationEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bUseParentRenderBounds", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_materialGroup", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}, {"name": "m_nHitboxSet", "type": "NetworkVarNames", "type_name": "uint8"}], "parent": "CGameSceneNode"}, "CSkyboxReference": {"fields": {"m_hSkyCamera": 1532, "m_worldGroupId": 1528}, "metadata": [], "parent": "C_BaseEntity"}, "CSpriteOriented": {"fields": {}, "metadata": [], "parent": "C_Sprite"}, "CTakeDamageInfoAPI": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}], "parent": null}, "CTimeline": {"fields": {"m_bStopped": 544, "m_flFinalValue": 536, "m_flInterval": 532, "m_flValues": 16, "m_nBucketCount": 528, "m_nCompressionType": 540, "m_nValueCounts": 272}, "metadata": [{"name": "m_flValues", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nValueCounts", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nBucketCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flInterval", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFinalValue", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nCompressionType", "type": "NetworkVarNames", "type_name": "TimelineCompression_t"}, {"name": "m_bStopped", "type": "NetworkVarNames", "type_name": "bool"}], "parent": ""}, "CTriggerFan": {"fields": {"m_RampTimer": 4192, "m_bFalloff": 4184, "m_bPushAwayFromInfoTarget": 4157, "m_bPushTowardsInfoTarget": 4156, "m_flForce": 4180, "m_hInfoFan": 4176, "m_qNoiseDelta": 4160, "m_vDirection": 4144, "m_vFanEnd": 4120, "m_vFanOrigin": 4096, "m_vFanOriginOffset": 4108, "m_vNoiseDirectionTarget": 4132}, "metadata": [{"name": "m_v<PERSON>an<PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vFanOriginOffset", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vFanEnd", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vNoiseDirectionTarget", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vDirection", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_bPushTowardsInfoTarget", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bPushAwayFromInfoTarget", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_q<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "Quaternion"}, {"name": "m_hInfoFan", "type": "NetworkVarNames", "type_name": "CHandle<CInfoFan>"}, {"name": "m_flForce", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_b<PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_RampTimer", "type": "NetworkVarNames", "type_name": "CountdownTimer"}], "parent": "C_BaseTrigger"}, "CWaterSplasher": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_AK47": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_AttributeContainer": {"fields": {"m_Item": 80, "m_iExternalItemProviderRegisteredToken": 1224, "m_ullRegisteredAsItemID": 1232}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_Item", "type": "NetworkVarNames", "type_name": "CEconItemView"}], "parent": "CAttributeManager"}, "C_BarnLight": {"fields": {"m_Color": 3784, "m_LightStyleEvents": 3872, "m_LightStyleString": 3832, "m_LightStyleTargets": 3896, "m_QueuedLightStyleStrings": 3848, "m_StyleEvent": 3920, "m_VisClusters": 4592, "m_bContactShadow": 4164, "m_bEnabled": 3776, "m_bFogMixedShadows": 4212, "m_bForceShadowsEnabled": 4165, "m_bInitialBoneSetup": 4584, "m_bPrecomputedFieldsValid": 4232, "m_fAlternateColorBrightness": 4192, "m_flBounceScale": 4172, "m_flBrightness": 3792, "m_flBrightnessScale": 3796, "m_flColorTemperature": 3788, "m_flFadeSizeEnd": 4220, "m_flFadeSizeStart": 4216, "m_flFogScale": 4208, "m_flFogStrength": 4200, "m_flLightStyleStartTime": 3840, "m_flLuminaireAnisotropy": 3824, "m_flLuminaireSize": 3820, "m_flMinRoughness": 4176, "m_flRange": 4120, "m_flShadowFadeSizeEnd": 4228, "m_flShadowFadeSizeStart": 4224, "m_flShape": 4088, "m_flSkirt": 4100, "m_flSkirtNear": 4104, "m_flSoftX": 4092, "m_flSoftY": 4096, "m_hLightCookie": 4080, "m_nBakeSpecularToCubemaps": 4136, "m_nBakedShadowIndex": 3804, "m_nBounceLight": 4168, "m_nCastShadows": 4152, "m_nColorMode": 3780, "m_nDirectLight": 3800, "m_nFog": 4196, "m_nFogShadows": 4204, "m_nLightMapUniqueId": 3812, "m_nLightPathUniqueId": 3808, "m_nLuminaireShape": 3816, "m_nPrecomputedSubFrusta": 4296, "m_nShadowMapSize": 4156, "m_nShadowPriority": 4160, "m_vAlternateColor": 4180, "m_vBakeSpecularToCubemapsSize": 4140, "m_vPrecomputedBoundsMaxs": 4248, "m_vPrecomputedBoundsMins": 4236, "m_vPrecomputedOBBAngles": 4272, "m_vPrecomputedOBBAngles0": 4312, "m_vPrecomputedOBBAngles1": 4348, "m_vPrecomputedOBBAngles2": 4384, "m_vPrecomputedOBBAngles3": 4420, "m_vPrecomputedOBBAngles4": 4456, "m_vPrecomputedOBBAngles5": 4492, "m_vPrecomputedOBBExtent": 4284, "m_vPrecomputedOBBExtent0": 4324, "m_vPrecomputedOBBExtent1": 4360, "m_vPrecomputedOBBExtent2": 4396, "m_vPrecomputedOBBExtent3": 4432, "m_vPrecomputedOBBExtent4": 4468, "m_vPrecomputedOBBExtent5": 4504, "m_vPrecomputedOBBOrigin": 4260, "m_vPrecomputedOBBOrigin0": 4300, "m_vPrecomputedOBBOrigin1": 4336, "m_vPrecomputedOBBOrigin2": 4372, "m_vPrecomputedOBBOrigin3": 4408, "m_vPrecomputedOBBOrigin4": 4444, "m_vPrecomputedOBBOrigin5": 4480, "m_vShear": 4124, "m_vSizeParams": 4108}, "metadata": [{"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nColorMode", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Color", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_flColorTemperature", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flBrightness", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flBrightnessScale", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nDirectLight", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nBakedShadowIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nLightPathUniqueId", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_nLightMapUniqueId", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_nLuminaireShape", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flLuminaireSize", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flLuminaireAnisotropy", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_LightStyleString", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_flLightStyleStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_QueuedLightStyleStrings", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_LightStyleEvents", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_LightStyleTargets", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseModelEntity>"}, {"name": "m_hLightCookie", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_flShape", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flSoftX", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flSoftY", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flSkirt", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flSkirtNear", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_vSizeParams", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_flRange", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_vShear", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nBakeSpecularToCubemaps", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vBakeSpecularToCubemapsSize", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nCastShadows", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nShadowMapSize", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nShadowPriority", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bContactShadow", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bForceShadowsEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nBounceLight", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flBounceScale", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flMinRoughness", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_vAlternateColor", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_fAlternateColorBrightness", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nFog", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFogStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nFogShadows", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFogScale", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bFogMixedShadows", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flFadeSizeStart", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFadeSizeEnd", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowFadeSizeStart", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowFadeSizeEnd", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bPrecomputed<PERSON><PERSON>sValid", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vPrecomputedBoundsMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedBoundsMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBOrigin", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBAngles", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_vPrecomputedOBBExtent", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nPrecomputedSubFrusta", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vPrecomputedOBBOrigin0", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBAngles0", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_vPrecomputedOBBExtent0", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBOrigin1", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBAngles1", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_vPrecomputedOBBExtent1", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBOrigin2", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBAngles2", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_vPrecomputedOBBExtent2", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBOrigin3", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBAngles3", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_vPrecomputedOBBExtent3", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBOrigin4", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBAngles4", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_vPrecomputedOBBExtent4", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBOrigin5", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBAngles5", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_vPrecomputedOBBExtent5", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_VisClusters", "type": "NetworkVarNames", "type_name": "uint16"}], "parent": "C_BaseModelEntity"}, "C_BaseButton": {"fields": {"m_glowEntity": 3776, "m_szDisplayText": 3784, "m_usable": 3780}, "metadata": [{"name": "m_glowEntity", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseModelEntity>"}, {"name": "m_usable", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_szDisplayText", "type": "NetworkVarNames", "type_name": "string_t"}], "parent": "C_BaseToggle"}, "C_BaseCSGrenade": {"fields": {"m_bClientPredictDelete": 7664, "m_bIsHeldByPlayer": 7666, "m_bJumpThrow": 7668, "m_bJustPulledPin": 7808, "m_bPinPulled": 7667, "m_bRedraw": 7665, "m_bThrowAnimating": 7669, "m_fDropTime": 7800, "m_fPinPullTime": 7804, "m_fThrowTime": 7672, "m_flNextHoldFrac": 7816, "m_flThrowStrength": 7680, "m_hSwitchToWeaponAfterThrow": 7820, "m_nNextHoldTick": 7812}, "metadata": [{"name": "m_bRedraw", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsHeldByPlayer", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bPinPulled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bJumpThrow", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bThrowAnimating", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fThrowTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_fDropTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_fPinPullTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bJustPulledPin", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nNextHoldTick", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "m_flNextHoldFrac", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_hSwitchToWeaponAfterThrow", "type": "NetworkVarNames", "type_name": "CHandle<CCSWeaponBase>"}], "parent": "C_CSWeaponBase"}, "C_BaseCSGrenadeProjectile": {"fields": {"flNextTrailLineTime": 5136, "m_arrTrajectoryTrailPointCreationTimes": 5184, "m_arrTrajectoryTrailPoints": 5160, "m_bCanCreateGrenadeTrail": 5141, "m_bExplodeEffectBegan": 5140, "m_flSpawnTime": 5120, "m_flTrajectoryTrailEffectCreationTime": 5208, "m_hSnapshotTrajectoryParticleSnapshot": 5152, "m_nBounces": 5088, "m_nExplodeEffectIndex": 5096, "m_nExplodeEffectTickBegin": 5104, "m_nSnapshotTrajectoryEffectIndex": 5144, "m_vInitialPosition": 5064, "m_vInitialVelocity": 5076, "m_vecExplodeEffectOrigin": 5108, "vecLastTrailLinePos": 5124}, "metadata": [{"name": "m_vInitialPosition", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vInitialVelocity", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nBounces", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nExplodeEffectIndex", "type": "NetworkVarNames", "type_name": "HParticleSystemDefinitionStrong"}, {"name": "m_nExplodeEffectTickBegin", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vecExplodeEffectOrigin", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": "C_BaseGrenade"}, "C_BaseClientUIEntity": {"fields": {"m_DialogXMLName": 3792, "m_PanelClassName": 3800, "m_PanelID": 3808, "m_bEnabled": 3784}, "metadata": [{"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_DialogXMLName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_PanelClassName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_PanelID", "type": "NetworkVarNames", "type_name": "string_t"}], "parent": "C_BaseModelEntity"}, "C_BaseCombatCharacter": {"fields": {"m_flWaterNextTraceTime": 5020, "m_flWaterWorldZ": 5016, "m_hMyWearables": 4984, "m_leftFootAttachment": 5008, "m_nWaterWakeMode": 5012, "m_rightFootAttachment": 5009}, "metadata": [{"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "m_hMyWearables", "type": "NetworkVarNames", "type_name": "CHandle<C_EconWearable>"}], "parent": "C_BaseFlex"}, "C_BaseDoor": {"fields": {"m_bIsUsable": 3776}, "metadata": [{"name": "m_bIsUsable", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseToggle"}, "C_BaseEntity": {"fields": {"m_CBodyComponent": 56, "m_DataChangeEventRef": 1460, "m_EntClientFlags": 1000, "m_ListEntry": 968, "m_MoveCollide": 1316, "m_MoveType": 1317, "m_NetworkTransmitComponent": 64, "m_Particles": 1400, "m_aThinkFunctions": 920, "m_bAnimTimeChanged": 1501, "m_bAnimatedEveryTick": 1352, "m_bApplyLayerMatchIDToModel": 891, "m_bClientSideRagdoll": 1002, "m_bDisabledContextThinks": 944, "m_bGravityActuallyDisabled": 1384, "m_bGravityDisabled": 1353, "m_bHasAddedVarsToInterpolation": 958, "m_bHasSuccessfullyInterpolated": 957, "m_bInterpolateEvenWithNoModel": 889, "m_bPredictable": 1385, "m_bPredictionEligible": 890, "m_bRenderEvenWhenNotSuccessfullyInterpolated": 959, "m_bRenderWithViewModels": 1386, "m_bSimulationTimeChanged": 1502, "m_bTakesDamage": 849, "m_dependencies": 1464, "m_fBBoxVisFlags": 1376, "m_fEffects": 1324, "m_fFlags": 1016, "m_flActualGravityScale": 1380, "m_flAnimTime": 948, "m_flCreateTime": 992, "m_flElasticity": 1340, "m_flFriction": 1336, "m_flGravityScale": 1344, "m_flNavIgnoreUntilTime": 1356, "m_flProxyRandomValue": 880, "m_flSimulationTime": 952, "m_flSpeed": 996, "m_flTimeScale": 1348, "m_flWaterLevel": 1320, "m_hEffectEntity": 1308, "m_hGroundEntity": 1328, "m_hOldMoveParent": 1396, "m_hOwnerEntity": 1312, "m_hSceneObjectController": 868, "m_hThink": 1360, "m_iCurrentThinkContext": 916, "m_iEFlags": 884, "m_iHealth": 844, "m_iMaxHealth": 840, "m_iTeamNum": 1003, "m_lifeState": 848, "m_nActualMoveType": 1318, "m_nBloodType": 1520, "m_nCreationTick": 1488, "m_nFirstPredictableCommand": 1388, "m_nGroundBodyIndex": 1332, "m_nInterpolationLatchDirtyFlags": 960, "m_nLastPredictableCommand": 1392, "m_nLastThinkTick": 808, "m_nNextThinkTick": 1008, "m_nNoInterpolationTick": 872, "m_nPlatformType": 864, "m_nSceneObjectOverrideFlags": 956, "m_nSimulationTick": 912, "m_nSubclassID": 896, "m_nTakeDamageFlags": 856, "m_nVisibilityNoInterpolationTick": 876, "m_nWaterType": 888, "m_pCollision": 832, "m_pGameSceneNode": 816, "m_pRenderComponent": 824, "m_sUniqueHammerID": 1512, "m_spawnflags": 1004, "m_tokLayerMatchID": 892, "m_ubInterpolationFrame": 865, "m_vecAbsVelocity": 1020, "m_vecAngVelocity": 1448, "m_vecBaseVelocity": 1296, "m_vecServerVelocity": 1032, "m_vecVelocity": 1072}, "metadata": [{"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "m_CBodyComponent", "type": "NetworkVarNames", "type_name": "CBodyComponent::Storage_t"}, {"name": "m_iMaxHealth", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_iHealth", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_lifeState", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_bTakesDamage", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nTakeDamageFlags", "type": "NetworkVarNames", "type_name": "TakeDamageFlags_t"}, {"name": "m_nPlatformType", "type": "NetworkVarNames", "type_name": "EntityPlatformTypes_t"}, {"name": "m_ubInterpolationFrame", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nSubclassID", "type": "NetworkVarNames", "type_name": "EntitySubclassID_t"}, {"name": "m_flAnimTime", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flSimulationTime", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flCreateTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flSpeed", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bClientSideRagdoll", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iTeamNum", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_spawnflags", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nNextThinkTick", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "m_fFlags", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_vecBaseVelocity", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_hEffectEntity", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_hOwnerEntity", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_MoveCollide", "type": "NetworkVarNames", "type_name": "MoveCollide_t"}, {"name": "m_MoveType", "type": "NetworkVarNames", "type_name": "MoveType_t"}, {"name": "m_flWaterLevel", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fEffects", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_hGroundEntity", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_nGroundBodyIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFriction", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flElasticity", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flGravityScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flTimeScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_bAnimatedEveryTick", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bGravityDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flNavIgnoreUntilTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_nBloodType", "type": "NetworkVarNames", "type_name": "BloodType"}], "parent": ""}, "C_BaseEntityAPI": {"fields": {}, "metadata": [{"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}], "parent": null}, "C_BaseFlex": {"fields": {"m_CachedViewTarget": 4740, "m_PhonemeClasses": 4888, "m_bResetFlexWeightsOnModelChange": 4782, "m_blinktime": 4760, "m_blinktoggle": 4640, "m_flBlinkAmount": 4776, "m_flJawOpenAmount": 4772, "m_flexWeight": 4472, "m_iBlink": 4756, "m_iEyeAttachment": 4781, "m_iJawOpen": 4768, "m_iMouthAttachment": 4780, "m_mEyeOcclusionRendererCameraToBoneTransform": 4812, "m_nEyeOcclusionRendererBone": 4808, "m_nLastFlexUpdateFrameCount": 4736, "m_nNextSceneEventId": 4752, "m_prevblinktoggle": 4764, "m_vEyeOcclusionRendererHalfExtent": 4860, "m_vLookTargetPosition": 4496}, "metadata": [{"name": "m_flexWeight", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_blinktoggle", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "CBaseAnimGraph"}, "C_BaseFlex__Emphasized_Phoneme": {"fields": {"m_bBasechecked": 29, "m_bRequired": 28, "m_bValid": 30, "m_flAmount": 24, "m_sClassName": 0}, "metadata": [], "parent": null}, "C_BaseGrenade": {"fields": {"m_DmgRadius": 4988, "m_ExplosionSound": 5016, "m_bHasWarnedAI": 4984, "m_bIsLive": 4986, "m_bIsSmokeGrenade": 4985, "m_flDamage": 5000, "m_flDetonateTime": 4992, "m_flNextAttack": 5052, "m_flWarnAITime": 4996, "m_hOriginalThrower": 5056, "m_hThrower": 5028, "m_iszBounceSound": 5008}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "m_bIsLive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_DmgRadius", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flDetonateTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flDamage", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_hThrower", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}], "parent": "C_BaseFlex"}, "C_BaseModelEntity": {"fields": {"m_CHitboxComponent": 2792, "m_CRenderComponent": 2784, "m_ClientOverrideTint": 3712, "m_Collision": 3088, "m_ConfigEntitiesToPropagateMaterialDecalsTo": 3416, "m_Glow": 3264, "m_LastHitGroup": 2840, "m_bAllowFadeInView": 2914, "m_bInitModelEffects": 2896, "m_bIsStaticProp": 2897, "m_bNoInterpolate": 3081, "m_bRenderToCubemaps": 3080, "m_bUseClientOverrideTint": 3716, "m_clrRender": 2944, "m_fadeMaxDist": 3360, "m_fadeMinDist": 3356, "m_flDecalHealBloodRate": 3404, "m_flDecalHealHeightRate": 3408, "m_flFadeScale": 3364, "m_flGlowBackfaceMult": 3352, "m_flShadowStrength": 3368, "m_iOldHealth": 2908, "m_nAddDecal": 3376, "m_nDecalsAdded": 2904, "m_nLastAddDecal": 2900, "m_nObjectCulling": 3372, "m_nRenderFX": 2913, "m_nRenderMode": 2912, "m_pClientAlphaProperty": 3704, "m_pDestructiblePartsSystemComponent": 2832, "m_sLastDamageSourceName": 2848, "m_vDecalForwardAxis": 3392, "m_vDecalPosition": 3380, "m_vLastDamagePosition": 2856, "m_vecRenderAttributes": 2952, "m_vecViewOffset": 3480}, "metadata": [{"name": "m_CRenderComponent", "type": "NetworkVarNames", "type_name": "CRenderComponent::Storage_t"}, {"name": "m_CHitboxComponent", "type": "NetworkVarNames", "type_name": "CHitboxComponent::Storage_t"}, {"name": "m_pDestructiblePartsSystemComponent", "type": "NetworkVarNames", "type_name": "CDestructiblePartsSystemComponent*"}, {"name": "m_nRenderMode", "type": "NetworkVarNames", "type_name": "RenderMode_t"}, {"name": "m_nRenderFX", "type": "NetworkVarNames", "type_name": "RenderFx_t"}, {"name": "m_clr<PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_vecRenderAttributes", "type": "NetworkVarNames", "type_name": "EntityRenderAttribute_t"}, {"name": "m_bRenderToCubemaps", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bNoInterpolate", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Collision", "type": "NetworkVarNames", "type_name": "CCollisionProperty"}, {"name": "m_<PERSON>low", "type": "NetworkVarNames", "type_name": "CGlowProperty"}, {"name": "m_flGlowBackfaceMult", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fadeMinDist", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fadeMaxDist", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flShadowStrength", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nObjectCulling", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nAddDecal", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vDecalPosition", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vDecalForwardAxis", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_flDecalHealBloodRate", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDecalHealHeightRate", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_ConfigEntitiesToPropagateMaterialDecalsTo", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseModelEntity>"}], "parent": "C_BaseEntity"}, "C_BasePlayerPawn": {"fields": {"m_ServerViewAngleChanges": 5200, "m_bIsSwappingToPredictableController": 5584, "m_flDeathTime": 5480, "m_flFOVSensitivityAdjust": 5544, "m_flLastCameraSetupTime": 5540, "m_flMouseSensitivity": 5548, "m_flOldSimulationTime": 5564, "m_flPredictionErrorTime": 5496, "m_hController": 5576, "m_hDefaultController": 5580, "m_iHideHUD": 5328, "m_nLastExecutedCommandNumber": 5568, "m_nLastExecutedCommandTick": 5572, "m_pAutoaimServices": 5136, "m_pCameraServices": 5176, "m_pFlashlightServices": 5168, "m_pItemServices": 5128, "m_pMovementServices": 5184, "m_pObserverServices": 5144, "m_pUseServices": 5160, "m_pWaterServices": 5152, "m_pWeaponServices": 5120, "m_skybox3d": 5336, "m_vOldOrigin": 5552, "m_vecLastCameraSetupLocalOrigin": 5528, "m_vecPredictionError": 5484, "v_angle": 5304, "v_anglePrevious": 5316}, "metadata": [{"name": "MNetworkUserGroupProxy", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "m_pWeaponServices", "type": "NetworkVarNames", "type_name": "CPlayer_WeaponServices*"}, {"name": "m_pItemServices", "type": "NetworkVarNames", "type_name": "CPlayer_ItemServices*"}, {"name": "m_pAutoaimServices", "type": "NetworkVarNames", "type_name": "CPlayer_AutoaimServices*"}, {"name": "m_pObserverServices", "type": "NetworkVarNames", "type_name": "CPlayer_ObserverServices*"}, {"name": "m_pWaterServices", "type": "NetworkVarNames", "type_name": "CPlayer_WaterServices*"}, {"name": "m_pUseServices", "type": "NetworkVarNames", "type_name": "CPlayer_UseServices*"}, {"name": "m_pFlashlightServices", "type": "NetworkVarNames", "type_name": "CPlayer_FlashlightServices*"}, {"name": "m_pCameraServices", "type": "NetworkVarNames", "type_name": "CPlayer_CameraServices*"}, {"name": "m_pMovementServices", "type": "NetworkVarNames", "type_name": "CPlayer_MovementServices*"}, {"name": "m_ServerViewAngleChanges", "type": "NetworkVarNames", "type_name": "ViewAngleServerChange_t"}, {"name": "m_iHideHUD", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_skybox3d", "type": "NetworkVarNames", "type_name": "sky3dparams_t"}, {"name": "m_flDeathTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_hController", "type": "NetworkVarNames", "type_name": "CHandle<CBasePlayerController>"}, {"name": "m_hDefaultController", "type": "NetworkVarNames", "type_name": "CHandle<CBasePlayerController>"}], "parent": "C_BaseCombatCharacter"}, "C_BasePlayerWeapon": {"fields": {"m_flNextPrimaryAttackTickRatio": 6388, "m_flNextSecondaryAttackTickRatio": 6396, "m_iClip1": 6400, "m_iClip2": 6404, "m_nNextPrimaryAttackTick": 6384, "m_nNextSecondaryAttackTick": 6392, "m_pReserveAmmo": 6408}, "metadata": [{"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkUserGroupProxy", "type": "Unknown"}, {"name": "m_nNextPrimaryAttackTick", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "m_flNextPrimaryAttackTickRatio", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nNextSecondaryAttackTick", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "m_flNextSecondaryAttackTickRatio", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_iClip1", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_iClip2", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_pReserveAmmo", "type": "NetworkVarNames", "type_name": "int"}], "parent": "C_EconEntity"}, "C_BasePropDoor": {"fields": {"m_bLocked": 5189, "m_bNoNPCs": 5190, "m_closedAngles": 5204, "m_closedPosition": 5192, "m_eDoorState": 5184, "m_hMaster": 5216, "m_modelChanged": 5188, "m_vWhereToSetLightingOrigin": 5220}, "metadata": [{"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_eDoorState", "type": "NetworkVarNames", "type_name": "DoorState_t"}, {"name": "m_bLocked", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bNoNPCs", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_closedPosition", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_closedAngles", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_hMaster", "type": "NetworkVarNames", "type_name": "CHandle<C_BasePropDoor>"}], "parent": "C_DynamicProp"}, "C_BaseToggle": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_BaseTrigger": {"fields": {"m_OnEndTouch": 3856, "m_OnEndTouchAll": 3896, "m_OnNotTouching": 4016, "m_OnStartTouch": 3776, "m_OnStartTouchAll": 3816, "m_OnTouching": 3936, "m_OnTouchingEachEntity": 3976, "m_bDisabled": 4092, "m_hFilter": 4088, "m_hTouchingEntities": 4056, "m_iFilterName": 4080}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_bDisabled", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseToggle"}, "C_Beam": {"fields": {"m_bTurnedOff": 3944, "m_fAmplitude": 3924, "m_fEndWidth": 3912, "m_fFadeLength": 3916, "m_fHaloScale": 3920, "m_fSpeed": 3932, "m_fStartFrame": 3928, "m_fWidth": 3908, "m_flDamage": 3788, "m_flFireTime": 3784, "m_flFrame": 3936, "m_flFrameRate": 3776, "m_flHDRColorScale": 3780, "m_hAttachEntity": 3856, "m_hBaseMaterial": 3832, "m_hEndEntity": 3960, "m_nAttachIndex": 3896, "m_nBeamFlags": 3852, "m_nBeamType": 3848, "m_nClipStyle": 3940, "m_nHaloIndex": 3840, "m_nNumBeamEnts": 3792, "m_queryHandleHalo": 3796, "m_vecEndPos": 3948}, "metadata": [{"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "m_flFrameRate", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flHDRColorScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nNumBeamEnts", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_hBaseMaterial", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_nHaloIndex", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_nBeamType", "type": "NetworkVarNames", "type_name": "BeamType_t"}, {"name": "m_nBeamFlags", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_hAttachEntity", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_nAttachIndex", "type": "NetworkVarNames", "type_name": "AttachmentHandle_t"}, {"name": "m_fWidth", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fEndWidth", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fFadeLength", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fHaloScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fAmplitude", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fStartFrame", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fSpeed", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFrame", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nClipStyle", "type": "NetworkVarNames", "type_name": "BeamClipStyle_t"}, {"name": "m_bTurnedOff", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vecEndPos", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": "C_BaseModelEntity"}, "C_Breakable": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_BreakableProp": {"fields": {"m_BreakableContentsType": 4776, "m_CPropDataComponent": 4512, "m_OnBreak": 4616, "m_OnHealthChanged": 4656, "m_OnStartDeath": 4576, "m_OnTakeDamage": 4696, "m_PerformanceMode": 4768, "m_bHasBreakPiecesOrCommands": 4800, "m_explodeDamage": 4804, "m_explodeRadius": 4808, "m_explosionBuildupSound": 4824, "m_explosionCustomEffect": 4832, "m_explosionCustomSound": 4840, "m_explosionDelay": 4816, "m_explosionModifier": 4848, "m_flDefBurstScale": 4748, "m_flDefaultFadeScale": 4864, "m_flLastPhysicsInfluenceTime": 4860, "m_flPressureDelay": 4744, "m_flPreventDamageBeforeTime": 4772, "m_hBreaker": 4764, "m_hLastAttacker": 4868, "m_hPhysicsAttacker": 4856, "m_iMinHealthDmg": 4740, "m_impactEnergyScale": 4736, "m_strBreakableContentsParticleOverride": 4792, "m_strBreakableContentsPropGroupOverride": 4784, "m_vDefBurstOffset": 4752}, "metadata": [{"name": "m_CPropDataComponent", "type": "NetworkVarNames", "type_name": "CPropDataComponent::Storage_t"}], "parent": "CBaseProp"}, "C_BulletHitModel": {"fields": {"m_bIsHit": 4512, "m_flTimeCreated": 4516, "m_hPlayerParent": 4508, "m_iBoneIndex": 4504, "m_matLocal": 4456, "m_vecStartPos": 4520}, "metadata": [], "parent": null}, "C_C4": {"fields": {"m_activeLightParticleIndex": 7664, "m_bBombPlacedAnimation": 7680, "m_bBombPlanted": 7723, "m_bIsPlantingViaUse": 7681, "m_bPlayedArmingBeeps": 7716, "m_bStartedArming": 7672, "m_eActiveLightEffect": 7668, "m_entitySpottedState": 7688, "m_fArmedTime": 7676, "m_nSpotRules": 7712}, "metadata": [{"name": "m_bStartedArming", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fArmedTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bBombPlacedAnimation", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsPlantingViaUse", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_entitySpottedState", "type": "NetworkVarNames", "type_name": "EntitySpottedState_t"}], "parent": "C_CSWeaponBase"}, "C_CS2HudModelAddon": {"fields": {}, "metadata": [], "parent": "C_LateUpdatedAnimating"}, "C_CS2HudModelArms": {"fields": {}, "metadata": [], "parent": "C_CS2HudModelBase"}, "C_CS2HudModelBase": {"fields": {}, "metadata": [], "parent": "C_LateUpdatedAnimating"}, "C_CS2HudModelWeapon": {"fields": {}, "metadata": [], "parent": "C_CS2HudModelBase"}, "C_CS2WeaponModuleBase": {"fields": {}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_CSGO_CounterTerroristTeamIntroCamera": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCamera"}, "C_CSGO_CounterTerroristWingmanIntroCamera": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCamera"}, "C_CSGO_EndOfMatchCamera": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCamera"}, "C_CSGO_EndOfMatchCharacterPosition": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCharacterPosition"}, "C_CSGO_EndOfMatchLineupEnd": {"fields": {}, "metadata": [], "parent": "C_CSGO_EndOfMatchLineupEndpoint"}, "C_CSGO_EndOfMatchLineupEndpoint": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "C_CSGO_EndOfMatchLineupStart": {"fields": {}, "metadata": [], "parent": "C_CSGO_EndOfMatchLineupEndpoint"}, "C_CSGO_MapPreviewCameraPath": {"fields": {"m_bConstantSpeed": 1538, "m_bDofEnabled": 1636, "m_bLoop": 1536, "m_bVerticalFOV": 1537, "m_flDofFarBlurry": 1652, "m_flDofFarCrisp": 1648, "m_flDofNearBlurry": 1640, "m_flDofNearCrisp": 1644, "m_flDofTiltToGround": 1656, "m_flDuration": 1540, "m_flPathDuration": 1612, "m_flPathLength": 1608, "m_flZFar": 1528, "m_flZNear": 1532}, "metadata": [], "parent": "C_BaseEntity"}, "C_CSGO_MapPreviewCameraPathNode": {"fields": {"m_flCameraSpeed": 1568, "m_flEaseIn": 1572, "m_flEaseOut": 1576, "m_flFOV": 1564, "m_nPathIndex": 1536, "m_szParentPathUniqueID": 1528, "m_vInTangentLocal": 1540, "m_vInTangentWorld": 1580, "m_vOutTangentLocal": 1552, "m_vOutTangentWorld": 1592}, "metadata": [], "parent": "C_BaseEntity"}, "C_CSGO_PreviewModel": {"fields": {"m_animgraph": 4984, "m_animgraphCharacterModeString": 4992, "m_defaultAnim": 5000, "m_flInitialModelScale": 5012, "m_nDefaultAnimLoopMode": 5008, "m_sInitialWeaponState": 5016}, "metadata": [], "parent": "C_BaseFlex"}, "C_CSGO_PreviewModelAlias_csgo_item_previewmodel": {"fields": {}, "metadata": [], "parent": "C_CSGO_PreviewModel"}, "C_CSGO_PreviewPlayer": {"fields": {"m_animgraph": 16176, "m_animgraphCharacterModeString": 16184, "m_flInitialModelScale": 16192}, "metadata": [], "parent": "C_CSPlayerPawn"}, "C_CSGO_PreviewPlayerAlias_csgo_player_previewmodel": {"fields": {}, "metadata": [], "parent": "C_CSGO_PreviewPlayer"}, "C_CSGO_TeamIntroCharacterPosition": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCharacterPosition"}, "C_CSGO_TeamIntroCounterTerroristPosition": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamIntroCharacterPosition"}, "C_CSGO_TeamIntroTerroristPosition": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamIntroCharacterPosition"}, "C_CSGO_TeamPreviewCamera": {"fields": {"m_nVariant": 1664}, "metadata": [], "parent": "C_CSGO_MapPreviewCameraPath"}, "C_CSGO_TeamPreviewCharacterPosition": {"fields": {"m_agentItem": 1560, "m_glovesItem": 2704, "m_nOrdinal": 1536, "m_nRandom": 1532, "m_nVariant": 1528, "m_sWeaponName": 1544, "m_weaponItem": 3848, "m_xuid": 1552}, "metadata": [{"name": "m_nVariant", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nRandom", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nOrdinal", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_sWeaponName", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_xuid", "type": "NetworkVarNames", "type_name": "XUID"}, {"name": "m_agentItem", "type": "NetworkVarNames", "type_name": "CEconItemView"}, {"name": "m_glovesItem", "type": "NetworkVarNames", "type_name": "CEconItemView"}, {"name": "m_weaponItem", "type": "NetworkVarNames", "type_name": "CEconItemView"}], "parent": "C_BaseEntity"}, "C_CSGO_TeamPreviewModel": {"fields": {}, "metadata": [], "parent": "C_CSGO_PreviewPlayer"}, "C_CSGO_TeamSelectCamera": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCamera"}, "C_CSGO_TeamSelectCharacterPosition": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCharacterPosition"}, "C_CSGO_TeamSelectCounterTerroristPosition": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamSelectCharacterPosition"}, "C_CSGO_TeamSelectTerroristPosition": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamSelectCharacterPosition"}, "C_CSGO_TerroristTeamIntroCamera": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCamera"}, "C_CSGO_TerroristWingmanIntroCamera": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCamera"}, "C_CSGameRules": {"fields": {"m_MatchDevice": 168, "m_MinimapVerticalSectionHeights": 3124, "m_RetakeRules": 3496, "m_TeamRespawnWaveTimes": 2844, "m_arrProhibitedItemIndices": 2244, "m_arrTournamentActiveCasterAccounts": 2444, "m_bAnyHostageReached": 148, "m_bBombDropped": 2468, "m_bBombPlanted": 2469, "m_bCTCantBuy": 2481, "m_bCTTimeOutActive": 77, "m_bFreezePeriod": 64, "m_bGameRestart": 116, "m_bHasMatchStarted": 172, "m_bHasTriggeredRoundStartMusic": 3460, "m_bIsDroppingItems": 2240, "m_bIsHltvActive": 2242, "m_bIsQuestEligible": 2241, "m_bIsQueuedMatchmaking": 152, "m_bIsValveDS": 160, "m_bLogoMap": 161, "m_bMapHasBombTarget": 149, "m_bMapHasBuyZone": 151, "m_bMapHasRescueZone": 150, "m_bMarkClientStopRecordAtRoundEnd": 3288, "m_bMatchWaitingForResume": 97, "m_bPlayAllStepSoundsOnServer": 162, "m_bRoundEndNoMusic": 3844, "m_bRoundEndShowTimerDefend": 3800, "m_bSwitchingTeamsAtRoundReset": 3461, "m_bTCantBuy": 2480, "m_bTeamIntroPeriod": 3788, "m_bTechnicalTimeOut": 96, "m_bTerroristTimeOutActive": 76, "m_bWarmupPeriod": 65, "m_eRoundEndReason": 3796, "m_eRoundWinReason": 2476, "m_fMatchStartTime": 104, "m_fRoundStartTime": 108, "m_fWarmupPeriodEnd": 68, "m_fWarmupPeriodStart": 72, "m_flCMMItemDropRevealEndTime": 2236, "m_flCMMItemDropRevealStartTime": 2232, "m_flCTTimeOutRemaining": 84, "m_flGameStartTime": 120, "m_flLastPerfSampleTime": 20256, "m_flNextRespawnWave": 2972, "m_flRestartRoundTime": 112, "m_flTerroristTimeOutRemaining": 80, "m_gamePhase": 128, "m_iHostagesRemaining": 144, "m_iMatchStats_PlayersAlive_CT": 2604, "m_iMatchStats_PlayersAlive_T": 2724, "m_iMatchStats_RoundResults": 2484, "m_iNumConsecutiveCTLoses": 3252, "m_iNumConsecutiveTerroristLoses": 3256, "m_iRoundEndFunFactData1": 3820, "m_iRoundEndFunFactData2": 3824, "m_iRoundEndFunFactData3": 3828, "m_iRoundEndFunFactPlayerSlot": 3816, "m_iRoundEndLegacy": 3848, "m_iRoundEndPlayerCount": 3840, "m_iRoundEndTimerTime": 3804, "m_iRoundEndWinnerTeam": 3792, "m_iRoundStartRoundNumber": 3856, "m_iRoundTime": 100, "m_iRoundWinStatus": 2472, "m_iSpectatorSlotCount": 164, "m_nCTTeamIntroVariant": 3784, "m_nCTTimeOuts": 92, "m_nEndMatchMapGroupVoteOptions": 3208, "m_nEndMatchMapGroupVoteTypes": 3168, "m_nEndMatchMapVoteWinner": 3248, "m_nHalloweenMaskListSeed": 2464, "m_nMatchAbortedEarlyReason": 3456, "m_nMatchEndCount": 3776, "m_nNextMapInMapgroup": 176, "m_nOvertimePlaying": 140, "m_nQueuedMatchmakingMode": 156, "m_nRoundEndCount": 3852, "m_nRoundStartCount": 3860, "m_nRoundsPlayedThisPhase": 136, "m_nTTeamIntroVariant": 3780, "m_nTerroristTimeOuts": 88, "m_nTournamentPredictionsPct": 2228, "m_numBestOfMaps": 2460, "m_pGameModeRules": 3488, "m_sRoundEndFunFactToken": 3808, "m_sRoundEndMessage": 3832, "m_szMatchStatTxt": 1204, "m_szTournamentEventName": 180, "m_szTournamentEventStage": 692, "m_szTournamentPredictionsTxt": 1716, "m_timeUntilNextPhaseStarts": 124, "m_totalRoundsPlayed": 132, "m_ullLocalMatchID": 3160, "m_vMinimapMaxs": 3112, "m_vMinimapMins": 3100}, "metadata": [{"name": "m_bFreezePeriod", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bWarmupPeriod", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fWarmupPeriodEnd", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_fWarmupPeriodStart", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bTerroristTimeOutActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bCTTimeOutActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flTerroristTimeOutRemaining", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flCTTimeOutRemaining", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nTerroristTimeOuts", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nCTTimeOuts", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bTechnicalTimeOut", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bMatchWaitingForResume", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iRoundTime", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_fMatchStartTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fRoundStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flRestartRoundTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bGameRestart", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flGameStartTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_timeUntilNextPhaseStarts", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_gamePhase", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_totalRoundsPlayed", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nRoundsPlayedThisPhase", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nOvertimePlaying", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iHostagesRemaining", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bAnyHostageReached", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bMapHasBombTarget", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bMapHasRescueZone", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bMapHasBuyZone", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsQueuedMatchmaking", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nQueuedMatchmakingMode", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bIsValveDS", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bLogoMap", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bPlayAllStepSoundsOnServer", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iSpectatorSlotCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_MatchDevice", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bHasMatchStarted", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nNextMapInMapgroup", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_szTournamentEventName", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_szTournamentEventStage", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_szMatchStatTxt", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_szTournamentPredictionsTxt", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_nTournamentPredictionsPct", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flCMMItemDropRevealStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flCMMItemDropRevealEndTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bIsDroppingItems", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsQuestEligible", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsHltvActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_arrProhibitedItemIndices", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_arrTournamentActiveCasterAccounts", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_numBestOfMaps", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nHalloweenMaskListSeed", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bBombDropped", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bBombPlanted", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iRoundWinStatus", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_eRoundWinReason", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_b<PERSON>ant<PERSON>uy", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bCTCantBuy", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iMatchStats_RoundResults", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iMatchStats_PlayersAlive_CT", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iMatchStats_PlayersAlive_T", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_TeamRespawnWaveTimes", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flNextRespawnWave", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_vMinimapMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vMinimapMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_MinimapVerticalSectionHeights", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nEndMatchMapGroupVoteTypes", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nEndMatchMapGroupVoteOptions", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nEndMatchMapVoteWinner", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iNumConsecutiveCTLoses", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iNumConsecutiveTerroristLoses", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nMatchAbortedEarlyReason", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_pGameModeRules", "type": "NetworkVarNames", "type_name": "CCSGameModeRules*"}, {"name": "m_RetakeRules", "type": "NetworkVarNames", "type_name": "CRetakeGameRules"}, {"name": "m_nMatchEndCount", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nTTeamIntroVariant", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nCTTeamIntroVariant", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bTeamIntroPeriod", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iRoundEndWinnerTeam", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_eRoundEndReason", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bRoundEndShowTimerDefend", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iRoundEndTimerTime", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_sRoundEndFunFactToken", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_iRoundEndFunFactPlayerSlot", "type": "NetworkVarNames", "type_name": "CPlayerSlot"}, {"name": "m_iRoundEndFunFactData1", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iRoundEndFunFactData2", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iRoundEndFunFactData3", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_sRoundEndMessage", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_iRoundEndPlayerCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bRoundEndNoMusic", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iRoundEndLegacy", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nRoundEndCount", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_iRoundStartRoundNumber", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nRoundStartCount", "type": "NetworkVarNames", "type_name": "uint8"}], "parent": null}, "C_CSGameRulesProxy": {"fields": {"m_pGameRules": 1528}, "metadata": [{"name": "m_pGameRules", "type": "NetworkVarNames", "type_name": "C_CSGameRules*"}], "parent": "C_GameRulesProxy"}, "C_CSMinimapBoundary": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "C_CSObserverPawn": {"fields": {"m_hDetectParentChange": 6112}, "metadata": [{"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}], "parent": "C_CSPlayerPawnBase"}, "C_CSPetPlacement": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "C_CSPlayerPawn": {"fields": {"m_ArmorValue": 10468, "m_EconGloves": 6720, "m_GunGameImmunityColor": 9752, "m_PredictedDamageTags": 10624, "m_RetakesMVPBoostExtraUtility": 6680, "m_aimPunchAngle": 6236, "m_aimPunchAngleVel": 6248, "m_aimPunchCache": 6272, "m_aimPunchTickBase": 6260, "m_aimPunchTickFraction": 6264, "m_angShootAngleHistory": 10552, "m_angStashedShootAngles": 10516, "m_bGrenadeParametersStashed": 10512, "m_bGunGameImmunity": 16164, "m_bHasDeathInfo": 10485, "m_bHasFemaleVoice": 6184, "m_bInBombZone": 6313, "m_bInBuyZone": 6232, "m_bInHostageRescueZone": 6312, "m_bInLanding": 6304, "m_bInNoDefuseArea": 10428, "m_bIsBuyMenuOpen": 6314, "m_bIsDefusing": 10418, "m_bIsGrabbingHostage": 10419, "m_bIsScoped": 10416, "m_bIsWalking": 9856, "m_bKilledByHeadshot": 10465, "m_bLastHeadBoneTransformIsValid": 9624, "m_bLeftHanded": 9677, "m_bMustSyncRagdollState": 7865, "m_bNeedToReApplyGloves": 6713, "m_bOldIsScoped": 10484, "m_bOnGroundLastTick": 9632, "m_bPrevDefuser": 6214, "m_bPrevHelmet": 6215, "m_bPreviouslyInBuyZone": 6233, "m_bRagdollDamageHeadshot": 7960, "m_bResumeZoom": 10417, "m_bRetakesHasDefuseKit": 6672, "m_bRetakesMVPLastRound": 6673, "m_bShouldAutobuyDMWeapons": 16156, "m_bSkipOneHeadConstraintUpdate": 9676, "m_bWaitForNoAttack": 10456, "m_entitySpottedState": 10392, "m_fImmuneToGunGameDamageTime": 16160, "m_fImmuneToGunGameDamageTimeLast": 16168, "m_fMolotovDamageTime": 16172, "m_fSwitchedHandednessTime": 9680, "m_flDeathInfoTime": 10488, "m_flEmitSoundTime": 10424, "m_flFlinchStack": 10440, "m_flHealthShotBoostExpirationTime": 6176, "m_flHitHeading": 10448, "m_flLandingStartTime": 6308, "m_flLandingTimeSeconds": 6188, "m_flLastFiredWeaponTime": 6180, "m_flNextSprayDecalTime": 6320, "m_flOldFallVelocity": 6192, "m_flSlopeDropHeight": 10128, "m_flSlopeDropOffset": 10008, "m_flTimeOfLastInjury": 6316, "m_flVelocityModifier": 10444, "m_flViewmodelFOV": 9696, "m_flViewmodelOffsetX": 9684, "m_flViewmodelOffsetY": 9688, "m_flViewmodelOffsetZ": 9692, "m_grenadeParameterStashTime": 10508, "m_hHudModelArms": 9660, "m_iBlockingUseActionInProgress": 10420, "m_iRetakesMVPBoostItem": 6676, "m_iRetakesOffering": 6664, "m_iRetakesOfferingCard": 6668, "m_iShotsFired": 10436, "m_ignoreLadderJumpTime": 10460, "m_lastLandTime": 9628, "m_nEconGlovesChanged": 7864, "m_nHighestAppliedDamageTagTick": 10732, "m_nHitBodyPart": 10452, "m_nLastKillerIndex": 10480, "m_nPrevArmorVal": 6216, "m_nPrevGrenadeAmmoCount": 6220, "m_nPrevHighestReceivedDamageTagTick": 10728, "m_nRagdollDamageBone": 7868, "m_nWhichBombZone": 10432, "m_pActionTrackingServices": 6160, "m_pBulletServices": 6128, "m_pBuyServices": 6144, "m_pDamageReactServices": 6168, "m_pGlowServices": 6152, "m_pHostageServices": 6136, "m_qDeathEyeAngles": 9664, "m_szLastPlaceName": 6196, "m_szRagdollDamageWeaponName": 7896, "m_thirdPersonHeading": 9864, "m_unCurrentEquipmentValue": 10472, "m_unFreezetimeEndEquipmentValue": 10476, "m_unPreviousWeaponHash": 6224, "m_unRoundStartEquipmentValue": 10474, "m_unWeaponHash": 6228, "m_vHeadConstraintOffset": 10248, "m_vRagdollDamageForce": 7872, "m_vRagdollDamagePosition": 7884, "m_vRagdollServerOrigin": 7964, "m_vecBulletHitModels": 9832, "m_vecDeathInfoOrigin": 10492, "m_vecPlayerPatchEconIndices": 9700, "m_vecStashedGrenadeThrowPosition": 10528, "m_vecStashedVelocity": 10540, "m_vecThrowPositionHistory": 10576, "m_vecVelocityHistory": 10600}, "metadata": [{"name": "m_pBulletServices", "type": "NetworkVarNames", "type_name": "CCSPlayer_BulletServices*"}, {"name": "m_pHostageServices", "type": "NetworkVarNames", "type_name": "CCSPlayer_HostageServices*"}, {"name": "m_pBuyServices", "type": "NetworkVarNames", "type_name": "CCSPlayer_BuyServices*"}, {"name": "m_pGlowServices", "type": "NetworkVarNames", "type_name": "CCSPlayer_GlowServices*"}, {"name": "m_pActionTrackingServices", "type": "NetworkVarNames", "type_name": "CCSPlayer_ActionTrackingServices*"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_flHealthShotBoostExpirationTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bHasFemaleVoice", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_szLastPlaceName", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_bInBuyZone", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_aimPunchAngle", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_aimPunchAngleVel", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_aimPunchTickBase", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_aimPunchTickFraction", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bInHostageRescueZone", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bInBombZone", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsBuyMenuOpen", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flTimeOfLastInjury", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flNextSprayDecalTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_iRetakesOffering", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iRetakesOfferingCard", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bRetakesHasDefuseKit", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bRetakesMVPLastRound", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iRetakesMVPBoostItem", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_RetakesMVPBoostExtraUtility", "type": "NetworkVarNames", "type_name": "loadout_slot_t"}, {"name": "m_EconGloves", "type": "NetworkVarNames", "type_name": "CEconItemView"}, {"name": "m_nEconGlovesChanged", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nRagdollDamageBone", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vRagdollDamageForce", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vRagdollDamagePosition", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_szRagdollDamageWeaponName", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_bRagdollDamageHeadshot", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vRagdollServerOrigin", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "MNetworkReplayCompatField", "type": "Unknown"}, {"name": "m_q<PERSON>eathEyeAngles", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_bLeftHanded", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fSwitchedHandednessTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flViewmodelOffsetX", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flViewmodelOffsetY", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flViewmodelOffsetZ", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flViewmodelFOV", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_vecPlayerPatchEconIndices", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_GunGameImmunityColor", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_bIsWalking", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_entitySpottedState", "type": "NetworkVarNames", "type_name": "EntitySpottedState_t"}, {"name": "m_bIsScoped", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bResumeZoom", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsDefusing", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsGrabbingHostage", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iBlockingUseActionInProgress", "type": "NetworkVarNames", "type_name": "CSPlayerBlockingUseAction_t"}, {"name": "m_flEmitSoundTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bInNoDefuseArea", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nWhichBombZone", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iShotsFired", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFlinchStack", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flVelocityModifier", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flHitHeading", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nHitBodyPart", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bWaitForNoAttack", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bKilledByHeadshot", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_ArmorValue", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_unCurrentEquipmentValue", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_unRoundStartEquipmentValue", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_unFreezetimeEndEquipmentValue", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_nLastKillerIndex", "type": "NetworkVarNames", "type_name": "CEntityIndex"}, {"name": "m_PredictedDamageTags", "type": "NetworkVarNames", "type_name": "PredictedDamageTag_t"}, {"name": "m_fImmuneToGunGameDamageTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bGunGameImmunity", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fMolotovDamageTime", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_CSPlayerPawnBase"}, "C_CSPlayerPawnBase": {"fields": {"m_angEyeAngles": 5792, "m_bCachedPlaneIsValid": 5661, "m_bClipHitStaticWorld": 5660, "m_bDeferStartMusicOnWarmup": 6012, "m_bFlashBuildUp": 5748, "m_bFlashDspHasBeenCleared": 5749, "m_bFlashScreenshotHasBeenGrabbed": 5750, "m_bGuardianShouldSprayCustomXMark": 6096, "m_bHasMovedSinceSpawn": 5681, "m_bIsRescuing": 5680, "m_bScreenTearFrameCaptured": 5732, "m_cycleLatch": 6016, "m_delayTargetIDTimer": 5944, "m_fMolotovUseTime": 5684, "m_fNextThinkPushAway": 5936, "m_fRenderingClipPlane": 5616, "m_flClientDeathTime": 5728, "m_flClientHealthFadeChangeTimestamp": 5760, "m_flCurrentMusicStartTime": 6004, "m_flDeathCCWeight": 5776, "m_flFlashBangTime": 5736, "m_flFlashDuration": 5756, "m_flFlashMaxAlpha": 5752, "m_flFlashOverlayAlpha": 5744, "m_flFlashScreenshotAlpha": 5740, "m_flLastSmokeAge": 6028, "m_flLastSmokeOverlayAlpha": 6024, "m_flLastSpawnTimeIndex": 5692, "m_flMusicRoundStartTime": 6008, "m_flNextMagDropTime": 6052, "m_flPrevMatchEndTime": 5784, "m_flPrevRoundEndTime": 5780, "m_flProgressBarStartTime": 5700, "m_hOriginalController": 6104, "m_holdTargetIDTimer": 5976, "m_iIDEntIndex": 5940, "m_iOldIDEntIndex": 5972, "m_iPlayerState": 5676, "m_iProgressBarDuration": 5696, "m_iTargetItemEntIdx": 5968, "m_iThrowGrenadeCounter": 5688, "m_nClientHealthFadeParityValue": 5764, "m_nLastClipPlaneSetupFrame": 5632, "m_nLastMagDropAttachmentIndex": 6056, "m_nPlayerInfernoBodyFx": 6044, "m_nPlayerInfernoFootFx": 6048, "m_pClippingWeapon": 5664, "m_pPingServices": 5608, "m_previousPlayerState": 5672, "m_serverIntendedCycle": 6020, "m_vLastSmokeOverlayColor": 6032, "m_vecIntroStartEyePosition": 5704, "m_vecIntroStartPlayerForward": 5716, "m_vecLastAliveLocalVelocity": 6060, "m_vecLastClipCameraForward": 5648, "m_vecLastClipCameraPos": 5636}, "metadata": [{"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "m_pPingServices", "type": "NetworkVarNames", "type_name": "CCSPlayer_PingServices*"}, {"name": "m_iPlayerState", "type": "NetworkVarNames", "type_name": "CSPlayerState"}, {"name": "m_bIsRescuing", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bHasMovedSinceSpawn", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fMolotovUseTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_iThrowGrenadeCounter", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iProgressBarDuration", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flProgressBarStartTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFlashMaxAlpha", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFlashDuration", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_cycleLatch", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_hOriginalController", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerController>"}], "parent": "C_BasePlayerPawn"}, "C_CSPlayerResource": {"fields": {"m_bEndMatchNextMapAllVoted": 1672, "m_bHostageAlive": 1528, "m_bombsiteCenterA": 1600, "m_bombsiteCenterB": 1612, "m_foundGoalPositions": 1673, "m_hostageRescueX": 1624, "m_hostageRescueY": 1640, "m_hostageRescueZ": 1656, "m_iHostageEntityIDs": 1552, "m_isHostageFollowingSomeone": 1540}, "metadata": [{"name": "m_bHostageAlive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_isHostageFollowingSomeone", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iHostageEntityIDs", "type": "NetworkVarNames", "type_name": "CEntityIndex"}, {"name": "m_bombsiteCenterA", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_bombsiteCenterB", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_hostageRescueX", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_hostageRescueY", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_hostageRescueZ", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bEndMatchNextMapAllVoted", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_CSTeam": {"fields": {"m_bSurrendered": 2228, "m_iClanID": 2376, "m_numMapVictories": 2224, "m_scoreFirstHalf": 2232, "m_scoreOvertime": 2240, "m_scoreSecondHalf": 2236, "m_szClanTeamname": 2244, "m_szTeamFlagImage": 2380, "m_szTeamLogoImage": 2388, "m_szTeamMatchStat": 1712}, "metadata": [{"name": "m_szTeamMatchStat", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_numMapVictories", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bSurrendered", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_scoreFirstHalf", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_scoreSecondHalf", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_scoreOvertime", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_szClanTeamname", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_iClanID", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_szTeamFlagImage", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_szTeamLogoImage", "type": "NetworkVarNames", "type_name": "char"}], "parent": "C_Team"}, "C_CSWeaponBase": {"fields": {"m_IronSightController": 7344, "m_OnPlayerPickup": 6624, "m_bBurstMode": 6708, "m_bClearWeaponIdentifyingUGC": 6912, "m_bDroppedNearBuyZone": 6752, "m_bFireOnEmpty": 6620, "m_bInReload": 6724, "m_bInspectPending": 6564, "m_bInspectShouldLoop": 6565, "m_bIsHauledBack": 6736, "m_bSilencerOn": 6737, "m_bUIWeapon": 6914, "m_bVisualsDataSet": 6913, "m_bWasActiveWeaponWhenDropped": 6940, "m_bWasOwnedByCT": 6980, "m_bWasOwnedByTerrorist": 6981, "m_donated": 6972, "m_fAccuracyPenalty": 6688, "m_fAccuracySmoothedForZoom": 6696, "m_fLastShotTime": 6976, "m_flCrosshairDistance": 6608, "m_flDisallowAttackAfterReloadStartUntilTime": 6728, "m_flDroppedAtTime": 6732, "m_flInspectCancelCompleteTime": 6560, "m_flLastAccuracyUpdateTime": 6692, "m_flLastBurstModeChangeTime": 6712, "m_flLastLOSTraceFailureTime": 7544, "m_flNextAttackRenderTimeOffset": 6756, "m_flNextClientFireBulletTime": 6984, "m_flNextClientFireBulletTime_Repredict": 6988, "m_flPostponeFireReadyFrac": 6720, "m_flRecoilIndex": 6704, "m_flTimeSilencerSwitchComplete": 6740, "m_flTurningInaccuracy": 6684, "m_flTurningInaccuracyDelta": 6668, "m_flWatTickOffset": 7640, "m_flWeaponGameplayAnimStateTimestamp": 6556, "m_hCurrentThirdPersonSequence": 6496, "m_hPrevOwner": 6932, "m_iAmmoLastCheck": 6612, "m_iIronSightMode": 7520, "m_iMostRecentTeamNumber": 6748, "m_iOriginalTeamNumber": 6744, "m_iRecoilIndex": 6700, "m_iWeaponGameplayAnimState": 6552, "m_nCustomEconReloadEventId": 6916, "m_nDropTick": 6936, "m_nLastEmptySoundCmdNum": 6616, "m_nPostponeFireReadyTicks": 6716, "m_nextPrevOwnerUseTime": 6928, "m_thirdPersonFireSequences": 6472, "m_thirdPersonSequences": 6500, "m_vecTurningInaccuracyEyeDirLast": 6672, "m_weaponMode": 6664}, "metadata": [{"name": "MNetworkOutOfPVSUpdates", "type": "Unknown"}, {"name": "m_iWeaponGameplayAnimState", "type": "NetworkVarNames", "type_name": "WeaponGameplayAnimState"}, {"name": "m_flWeaponGameplayAnimStateTimestamp", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flInspectCancelCompleteTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bInspectPending", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_weaponMode", "type": "NetworkVarNames", "type_name": "CSWeaponMode"}, {"name": "m_fAccuracyPenalty", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_iRecoilIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flRecoilIndex", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bBurstMode", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nPostponeFireReadyTicks", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "m_flPostponeFireReadyFrac", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bInReload", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flDisallowAttackAfterReloadStartUntilTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flDroppedAtTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bIsHauledBack", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bSilencerOn", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flTimeSilencerSwitchComplete", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_iOriginalTeamNumber", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iMostRecentTeamNumber", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bDroppedNearBuyZone", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nextPrevOwnerUseTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_hPrevOwner", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_nDropTick", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "m_bWasActiveWeaponWhenDropped", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fLastShotTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_iIronSightMode", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flWatTickOffset", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BasePlayerWeapon"}, "C_CSWeaponBaseGun": {"fields": {"m_bNeedsBoltAction": 7693, "m_iBurstShotsRemaining": 7668, "m_iSilencerBodygroup": 7672, "m_inPrecache": 7692, "m_nRevolverCylinderIdx": 7696, "m_silencedModelIndex": 7688, "m_zoomLevel": 7664}, "metadata": [{"name": "m_zoomLevel", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iBurstShotsRemaining", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bNeedsBoltAction", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nRevolverCylinderIdx", "type": "NetworkVarNames", "type_name": "int32"}], "parent": "C_CSWeaponBase"}, "C_Chicken": {"fields": {"m_AttributeManager": 5192, "m_bAttributesInitialized": 6432, "m_bIsPreviewModel": 6440, "m_hHolidayHatAddon": 5176, "m_hWaterWakeParticles": 6436, "m_jumpedThisFrame": 5180, "m_leader": 5184}, "metadata": [{"name": "m_jumpedThis<PERSON><PERSON>e", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_leader", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_AttributeManager", "type": "NetworkVarNames", "type_name": "CAttributeContainer"}], "parent": "C_DynamicProp"}, "C_ClientRagdoll": {"fields": {"m_bFadeOut": 4456, "m_bFadingOut": 4486, "m_bImportant": 4457, "m_bReleaseRagdoll": 4484, "m_flEffectTime": 4460, "m_flScaleEnd": 4488, "m_flScaleTimeEnd": 4568, "m_flScaleTimeStart": 4528, "m_gibDespawnTime": 4464, "m_iCurrentFriction": 4468, "m_iEyeAttachment": 4485, "m_iFrictionAnimState": 4480, "m_iMaxFriction": 4476, "m_iMinFriction": 4472}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_ColorCorrection": {"fields": {"m_MaxFalloff": 1544, "m_MinFalloff": 1540, "m_bClientSide": 2078, "m_bEnabled": 2076, "m_bEnabledOnClient": 2080, "m_bExclusive": 2079, "m_bFadingIn": 2088, "m_bMaster": 2077, "m_flCurWeight": 1560, "m_flCurWeightOnClient": 2084, "m_flFadeDuration": 2100, "m_flFadeInDuration": 1548, "m_flFadeOutDuration": 1552, "m_flFadeStartTime": 2096, "m_flFadeStartWeight": 2092, "m_flMaxWeight": 1556, "m_netlookupFilename": 1564, "m_vecOrigin": 1528}, "metadata": [{"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "m_<PERSON><PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_<PERSON><PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeInDuration", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeOutDuration", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flMaxWeight", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flCurWeight", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_netlookupFilename", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bMaster", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bClientSide", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bExclusive", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_ColorCorrectionVolume": {"fields": {"m_FadeDuration": 4120, "m_LastEnterTime": 4100, "m_LastEnterWeight": 4096, "m_LastExitTime": 4108, "m_LastExitWeight": 4104, "m_MaxWeight": 4116, "m_Weight": 4124, "m_bEnabled": 4112, "m_lookupFilename": 4128}, "metadata": [{"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_MaxWeight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_FadeDuration", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_Weight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_lookupFilename", "type": "NetworkVarNames", "type_name": "char"}], "parent": "C_BaseTrigger"}, "C_CsmFovOverride": {"fields": {"m_cameraName": 1528, "m_flCsmFovOverrideValue": 1536}, "metadata": [], "parent": "C_BaseEntity"}, "C_DEagle": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_DecoyGrenade": {"fields": {}, "metadata": [], "parent": "C_BaseCSGrenade"}, "C_DecoyProjectile": {"fields": {"m_flTimeParticleEffectSpawn": 5256, "m_nClientLastKnownDecoyShotTick": 5220, "m_nDecoyShotTick": 5216}, "metadata": [{"name": "m_nDecoyShotTick", "type": "NetworkVarNames", "type_name": "int"}], "parent": "C_BaseCSGrenadeProjectile"}, "C_DynamicLight": {"fields": {"m_Exponent": 3784, "m_Flags": 3776, "m_InnerAngle": 3788, "m_LightStyle": 3777, "m_OuterAngle": 3792, "m_Radius": 3780, "m_SpotRadius": 3796}, "metadata": [{"name": "m_Flags", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_LightStyle", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_<PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_Exponent", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_InnerAngle", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_OuterAngle", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_SpotRadius", "type": "NetworkVarNames", "type_name": "float32"}], "parent": "C_BaseModelEntity"}, "C_DynamicProp": {"fields": {"m_OnAnimReachedEnd": 5048, "m_OnAnimReachedStart": 5008, "m_bCreateNonSolid": 5104, "m_bFiredStartEndOutput": 5102, "m_bForceNpcExclude": 5103, "m_bIsOverrideProp": 5105, "m_bRandomizeCycle": 5100, "m_bStartDisabled": 5101, "m_bUseAnimGraph": 4881, "m_bUseHitboxesForRenderBox": 4880, "m_glowColor": 5120, "m_iCachedFrameCount": 5128, "m_iInitialGlowState": 5108, "m_iszIdleAnim": 5088, "m_nGlowRange": 5112, "m_nGlowRangeMin": 5116, "m_nGlowTeam": 5124, "m_nIdleAnimLoopMode": 5096, "m_pOutputAnimBegun": 4888, "m_pOutputAnimLoopCycleOver": 4968, "m_pOutputAnimOver": 4928, "m_vecCachedRenderMaxs": 5144, "m_vecCachedRenderMins": 5132}, "metadata": [{"name": "m_bUseHitboxesForRenderBox", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bUseAnimGraph", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BreakableProp"}, "C_DynamicPropAlias_cable_dynamic": {"fields": {}, "metadata": [], "parent": "C_DynamicProp"}, "C_DynamicPropAlias_dynamic_prop": {"fields": {}, "metadata": [], "parent": "C_DynamicProp"}, "C_DynamicPropAlias_prop_dynamic_override": {"fields": {}, "metadata": [], "parent": "C_DynamicProp"}, "C_EconEntity": {"fields": {"m_AttributeManager": 5024, "m_OriginalOwnerXuidHigh": 6268, "m_OriginalOwnerXuidLow": 6264, "m_bAttachmentDirty": 6328, "m_bAttributesInitialized": 5016, "m_bClientside": 6288, "m_bParticleSystemsCreated": 6289, "m_flFallbackWear": 6280, "m_flFlexDelayTime": 5000, "m_flFlexDelayedWeight": 5008, "m_hOldProvidee": 6352, "m_hViewmodelAttachment": 6320, "m_iNumOwnerValidationRetries": 6336, "m_iOldTeam": 6324, "m_nFallbackPaintKit": 6272, "m_nFallbackSeed": 6276, "m_nFallbackStatTrak": 6284, "m_nUnloadedModelIndex": 6332, "m_vecAttachedModels": 6360, "m_vecAttachedParticles": 6296}, "metadata": [{"name": "m_AttributeManager", "type": "NetworkVarNames", "type_name": "CAttributeContainer"}, {"name": "m_OriginalOwnerXuidLow", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_OriginalOwnerXuidHigh", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nFallbackPaintKit", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nFallbackSeed", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFallbackWear", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nFallbackStatTrak", "type": "NetworkVarNames", "type_name": "int"}], "parent": "C_BaseFlex"}, "C_EconEntity__AttachedModelData_t": {"fields": {"m_iModelDisplayFlags": 0}, "metadata": [], "parent": null}, "C_EconItemView": {"fields": {"m_AttributeList": 528, "m_NetworkedDynamicAttributes": 648, "m_bDisallowSOC": 489, "m_bInitialized": 488, "m_bInitializedTags": 1136, "m_bInventoryImageRgbaRequested": 96, "m_bInventoryImageTriedCache": 97, "m_bIsStoreItem": 490, "m_bIsTradeItem": 491, "m_bRestoreCustomMaterialAfterPrecache": 440, "m_iAccountID": 472, "m_iEntityLevel": 448, "m_iEntityQuality": 444, "m_iEntityQuantity": 492, "m_iInventoryPosition": 476, "m_iItemDefinitionIndex": 442, "m_iItemID": 456, "m_iItemIDHigh": 464, "m_iItemIDLow": 468, "m_iOriginOverride": 504, "m_iQualityOverride": 500, "m_iRarityOverride": 496, "m_nInventoryImageRgbaHeight": 132, "m_nInventoryImageRgbaWidth": 128, "m_szCurrentLoadCachedFileName": 136, "m_szCustomName": 768, "m_szCustomNameOverride": 929, "m_ubStyleOverride": 508, "m_unClientFlags": 509}, "metadata": [{"name": "m_iItemDefinitionIndex", "type": "NetworkVarNames", "type_name": "item_definition_index_t"}, {"name": "m_iEntityQuality", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iEntityLevel", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_iItemIDHigh", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_iItemIDLow", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_iAccountID", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_iInventoryPosition", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_bInitialized", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_AttributeList", "type": "NetworkVarNames", "type_name": "CAttributeList"}, {"name": "m_NetworkedDynamicAttributes", "type": "NetworkVarNames", "type_name": "CAttributeList"}, {"name": "m_szCustomName", "type": "NetworkVarNames", "type_name": "char"}], "parent": ""}, "C_EconWearable": {"fields": {"m_bAlwaysAllow": 6388, "m_nForceSkin": 6384}, "metadata": [], "parent": "C_EconEntity"}, "C_EntityDissolve": {"fields": {"m_bCoreExplode": 3836, "m_bLinkedToServerEnt": 3837, "m_flFadeInLength": 3792, "m_flFadeInStart": 3788, "m_flFadeOutLength": 3808, "m_flFadeOutModelLength": 3800, "m_flFadeOutModelStart": 3796, "m_flFadeOutStart": 3804, "m_flNextSparkTime": 3812, "m_flStartTime": 3784, "m_nDissolveType": 3816, "m_nMagnitude": 3832, "m_vDissolverOrigin": 3820}, "metadata": [{"name": "m_flStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flFadeInStart", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeInLength", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeOutModelStart", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeOutModelLength", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeOutStart", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeOutLength", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nDissolveType", "type": "NetworkVarNames", "type_name": "EntityDisolveType_t"}, {"name": "m_vDissolverOrigin", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nMagnitude", "type": "NetworkVarNames", "type_name": "uint32"}], "parent": "C_BaseModelEntity"}, "C_EntityFlame": {"fields": {"m_bCheapEffect": 1572, "m_hEntAttached": 1528, "m_hOldAttached": 1568}, "metadata": [{"name": "m_hEntAttached", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseEntity>"}, {"name": "m_bCheapEffect", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_EnvCombinedLightProbeVolume": {"fields": {"m_Entity_Color": 5744, "m_Entity_bCustomCubemapTexture": 5760, "m_Entity_bEnabled": 5945, "m_Entity_bMoveable": 5864, "m_Entity_bStartDisabled": 5880, "m_Entity_flBrightness": 5748, "m_Entity_flEdgeFadeDist": 5884, "m_Entity_hCubemapTexture": 5752, "m_Entity_hLightProbeDirectLightIndicesTexture": 5816, "m_Entity_hLightProbeDirectLightScalarsTexture": 5824, "m_Entity_hLightProbeDirectLightShadowsTexture": 5832, "m_Entity_hLightProbeTexture_AmbientCube": 5768, "m_Entity_hLightProbeTexture_SDF": 5776, "m_Entity_hLightProbeTexture_SH2_B": 5808, "m_Entity_hLightProbeTexture_SH2_DC": 5784, "m_Entity_hLightProbeTexture_SH2_G": 5800, "m_Entity_hLightProbeTexture_SH2_R": 5792, "m_Entity_nEnvCubeMapArrayIndex": 5872, "m_Entity_nHandshake": 5868, "m_Entity_nLightProbeAtlasX": 5912, "m_Entity_nLightProbeAtlasY": 5916, "m_Entity_nLightProbeAtlasZ": 5920, "m_Entity_nLightProbeSizeX": 5900, "m_Entity_nLightProbeSizeY": 5904, "m_Entity_nLightProbeSizeZ": 5908, "m_Entity_nPriority": 5876, "m_Entity_vBoxMaxs": 5852, "m_Entity_vBoxMins": 5840, "m_Entity_vEdgeFadeDists": 5888}, "metadata": [{"name": "m_Entity_Color", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_Entity_flBrightness", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_Entity_hCubemapTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_bCustomCubemapTexture", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_hLightProbeTexture_AmbientCube", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeTexture_SDF", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeTexture_SH2_DC", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeTexture_SH2_R", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeTexture_SH2_G", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeTexture_SH2_B", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeDirectLightIndicesTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeDirectLightScalarsTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeDirectLightShadowsTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_vBoxMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_Entity_vBoxMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_Entity_bMoveable", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_nHandshake", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nEnvCubeMapArrayIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nPriority", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_flEdgeFadeDist", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_Entity_vEdgeFadeDists", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_Entity_nLightProbeSizeX", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeSizeY", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeSizeZ", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeAtlasX", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeAtlasY", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeAtlasZ", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_EnvCombinedLightProbeVolumeAlias_func_combined_light_probe_volume": {"fields": {}, "metadata": [], "parent": "C_EnvCombinedLightProbeVolume"}, "C_EnvCubemap": {"fields": {"m_Entity_bCopyDiffuseFromDefaultCubemap": 1736, "m_Entity_bCustomCubemapTexture": 1664, "m_Entity_bDefaultEnvMap": 1733, "m_Entity_bDefaultSpecEnvMap": 1734, "m_Entity_bEnabled": 1752, "m_Entity_bIndoorCubeMap": 1735, "m_Entity_bMoveable": 1696, "m_Entity_bStartDisabled": 1732, "m_Entity_flDiffuseScale": 1728, "m_Entity_flEdgeFadeDist": 1712, "m_Entity_flInfluenceRadius": 1668, "m_Entity_hCubemapTexture": 1656, "m_Entity_nEnvCubeMapArrayIndex": 1704, "m_Entity_nHandshake": 1700, "m_Entity_nPriority": 1708, "m_Entity_vBoxProjectMaxs": 1684, "m_Entity_vBoxProjectMins": 1672, "m_Entity_vEdgeFadeDists": 1716}, "metadata": [{"name": "m_Entity_hCubemapTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_bCustomCubemapTexture", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_flInfluenceRadius", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_Entity_vBoxProjectMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_Entity_vBoxProjectMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_Entity_bMoveable", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_nHandshake", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nEnvCubeMapArrayIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nPriority", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_flEdgeFadeDist", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_Entity_vEdgeFadeDists", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_Entity_flDiffuseScale", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_Entity_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_bDefaultEnvMap", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_bDefaultSpecEnvMap", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_bIndoorCubeMap", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_bCopyDiffuseFromDefaultCubemap", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_EnvCubemapBox": {"fields": {}, "metadata": [], "parent": "C_EnvCubemap"}, "C_EnvCubemapFog": {"fields": {"m_bActive": 1564, "m_bFirstTime": 1601, "m_bHasHeightFogEnd": 1600, "m_bHeightFogEnabled": 1540, "m_bStartDisabled": 1565, "m_flEndDistance": 1528, "m_flFogFalloffExponent": 1536, "m_flFogHeightEnd": 1548, "m_flFogHeightExponent": 1556, "m_flFogHeightStart": 1552, "m_flFogHeightWidth": 1544, "m_flFogMaxOpacity": 1568, "m_flLODBias": 1560, "m_flStartDistance": 1532, "m_hFogCubemapTexture": 1592, "m_hSkyMaterial": 1576, "m_iszSkyEntity": 1584, "m_nCubemapSourceType": 1572}, "metadata": [{"name": "m_flEndDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flStartDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogFalloffExponent", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bHeightFogEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flFogHeightWidth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogHeightEnd", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogHeightStart", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogHeightExponent", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flLODBias", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flFogMaxOpacity", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nCubemapSourceType", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_hSkyMaterial", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_iszSkyEntity", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_hFogCubemapTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_bHasHeightFogEnd", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_EnvDecal": {"fields": {"m_bProjectOnCharacters": 3801, "m_bProjectOnWater": 3802, "m_bProjectOnWorld": 3800, "m_flDepth": 3792, "m_flDepthSortBias": 3804, "m_flHeight": 3788, "m_flWidth": 3784, "m_hDecalMaterial": 3776, "m_nRenderOrder": 3796}, "metadata": [{"name": "m_hDecalMaterial", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_flWidth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flHeight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDepth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nRenderOrder", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_bProjectOnWorld", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bProjectOnCharacters", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bProjectOnWater", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flDepthSortBias", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BaseModelEntity"}, "C_EnvDetailController": {"fields": {"m_flFadeEndDist": 1532, "m_flFadeStartDist": 1528}, "metadata": [{"name": "m_flFadeStartDist", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeEndDist", "type": "NetworkVarNames", "type_name": "float32"}], "parent": "C_BaseEntity"}, "C_EnvLightProbeVolume": {"fields": {"m_Entity_bEnabled": 5761, "m_Entity_bMoveable": 5712, "m_Entity_bStartDisabled": 5724, "m_Entity_hLightProbeDirectLightIndicesTexture": 5664, "m_Entity_hLightProbeDirectLightScalarsTexture": 5672, "m_Entity_hLightProbeDirectLightShadowsTexture": 5680, "m_Entity_hLightProbeTexture_AmbientCube": 5616, "m_Entity_hLightProbeTexture_SDF": 5624, "m_Entity_hLightProbeTexture_SH2_B": 5656, "m_Entity_hLightProbeTexture_SH2_DC": 5632, "m_Entity_hLightProbeTexture_SH2_G": 5648, "m_Entity_hLightProbeTexture_SH2_R": 5640, "m_Entity_nHandshake": 5716, "m_Entity_nLightProbeAtlasX": 5740, "m_Entity_nLightProbeAtlasY": 5744, "m_Entity_nLightProbeAtlasZ": 5748, "m_Entity_nLightProbeSizeX": 5728, "m_Entity_nLightProbeSizeY": 5732, "m_Entity_nLightProbeSizeZ": 5736, "m_Entity_nPriority": 5720, "m_Entity_vBoxMaxs": 5700, "m_Entity_vBoxMins": 5688}, "metadata": [{"name": "m_Entity_hLightProbeTexture_AmbientCube", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeTexture_SDF", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeTexture_SH2_DC", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeTexture_SH2_R", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeTexture_SH2_G", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeTexture_SH2_B", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeDirectLightIndicesTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeDirectLightScalarsTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeDirectLightShadowsTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_vBoxMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_Entity_vBoxMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_Entity_bMoveable", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_nHandshake", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nPriority", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_nLightProbeSizeX", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeSizeY", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeSizeZ", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeAtlasX", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeAtlasY", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeAtlasZ", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_EnvParticleGlow": {"fields": {"m_ColorTint": 5244, "m_flAlphaScale": 5232, "m_flRadiusScale": 5236, "m_flSelfIllumScale": 5240, "m_hTextureOverride": 5248}, "metadata": [{"name": "m_flAlphaScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flRadiusScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flSelfIllumScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_ColorTint", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_hTextureOverride", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}], "parent": "C_ParticleSystem"}, "C_EnvProjectedTexture": {"fields": {}, "metadata": [], "parent": "C_ModelPointEntity"}, "C_EnvSky": {"fields": {"m_bEnabled": 3828, "m_bStartDisabled": 3792, "m_flBrightnessScale": 3804, "m_flFogMaxEnd": 3824, "m_flFogMaxStart": 3820, "m_flFogMinEnd": 3816, "m_flFogMinStart": 3812, "m_hSkyMaterial": 3776, "m_hSkyMaterialLightingOnly": 3784, "m_nFogType": 3808, "m_vTintColor": 3793, "m_vTintColorLightingOnly": 3797}, "metadata": [{"name": "m_hSkyMaterial", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_hSkyMaterialLightingOnly", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vTintColor", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_vTintColorLightingOnly", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_flBrightnessScale", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nFogType", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFogMinStart", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogMinEnd", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogMaxStart", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogMaxEnd", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseModelEntity"}, "C_EnvVolumetricFogController": {"fields": {"m_TintColor": 1532, "m_bActive": 1604, "m_bEnableIndirect": 1645, "m_bFirstTime": 1696, "m_bIsMaster": 1646, "m_bStartDisabled": 1644, "m_fFirstVolumeSliceThickness": 1564, "m_fNoiseSpeed": 1660, "m_fNoiseStrength": 1664, "m_fWindSpeed": 1680, "m_flAnisotropy": 1536, "m_flDefaultAnisotropy": 1632, "m_flDefaultDrawDistance": 1640, "m_flDefaultScattering": 1636, "m_flDrawDistance": 1544, "m_flFadeInEnd": 1552, "m_flFadeInStart": 1548, "m_flFadeSpeed": 1540, "m_flIndirectStrength": 1556, "m_flScattering": 1528, "m_flStartAnisoTime": 1608, "m_flStartAnisotropy": 1620, "m_flStartDrawDistance": 1628, "m_flStartDrawDistanceTime": 1616, "m_flStartScatterTime": 1612, "m_flStartScattering": 1624, "m_hFogIndirectTexture": 1648, "m_nForceRefreshCount": 1656, "m_nIndirectTextureDimX": 1568, "m_nIndirectTextureDimY": 1572, "m_nIndirectTextureDimZ": 1576, "m_nVolumeDepth": 1560, "m_vBoxMaxs": 1592, "m_vBoxMins": 1580, "m_vNoiseScale": 1668, "m_vWindDirection": 1684}, "metadata": [{"name": "m_flScattering", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_TintColor", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_flAnisotropy", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFadeSpeed", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDrawDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFadeInStart", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFadeInEnd", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flIndirectStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nVolumeDepth", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_fFirstVolumeSliceThickness", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nIndirectTextureDimX", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nIndirectTextureDimY", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nIndirectTextureDimZ", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vBoxMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vBoxMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_bActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flStartAnisoTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flStartScatterTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flStartDrawDistanceTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flStartAnisotropy", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flStartScattering", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flStartDrawDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDefaultAnisotropy", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDefaultScattering", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDefaultDrawDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bEnableIndirect", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsMaster", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_hFogIndirectTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_nForceRefreshCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_fNoiseSpeed", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fNoiseStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_vNoiseScale", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_fWindSpeed", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_vWindDirection", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": "C_BaseEntity"}, "C_EnvVolumetricFogVolume": {"fields": {"m_TintColor": 1592, "m_bActive": 1528, "m_bIndirectUseLPVs": 1557, "m_bOverrideIndirectLightStrength": 1597, "m_bOverrideNoiseStrength": 1599, "m_bOverrideSunLightStrength": 1598, "m_bOverrideTintColor": 1596, "m_bStartDisabled": 1556, "m_fHeightFogEdgeWidth": 1576, "m_fIndirectLightStrength": 1580, "m_fNoiseStrength": 1588, "m_fSunLightStrength": 1584, "m_flFalloffExponent": 1568, "m_flHeightFogDepth": 1572, "m_flStrength": 1560, "m_nFalloffShape": 1564, "m_vBoxMaxs": 1544, "m_vBoxMins": 1532}, "metadata": [{"name": "m_bActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vBoxMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vBoxMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIndirectUseLPVs", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nFalloffShape", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFalloffExponent", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flHeightFogDepth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fHeightFogEdgeWidth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fIndirectLightStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fSunLightStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fNoiseStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_TintColor", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_bOverrideTintColor", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bOverrideIndirectLightStrength", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bOverrideSunLightStrength", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bOverrideNoiseStrength", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_EnvWind": {"fields": {"m_EnvWindShared": 1528}, "metadata": [{"name": "m_EnvWindShared", "type": "NetworkVarNames", "type_name": "CEnvWindShared"}], "parent": "C_BaseEntity"}, "C_EnvWindClientside": {"fields": {"m_EnvWindShared": 1528}, "metadata": [{"name": "m_EnvWindShared", "type": "NetworkVarNames", "type_name": "CEnvWindShared"}], "parent": "C_BaseEntity"}, "C_EnvWindController": {"fields": {"m_EnvWindShared": 1528, "m_bFirstTime": 1961, "m_bIsMaster": 1960, "m_fDirectionVariation": 1928, "m_fSpeedVariation": 1932, "m_fTurbulence": 1936, "m_fVolumeHalfExtentXY": 1940, "m_fVolumeHalfExtentZ": 1944, "m_nClipmapLevels": 1956, "m_nVolumeResolutionXY": 1948, "m_nVolumeResolutionZ": 1952}, "metadata": [{"name": "m_EnvWindShared", "type": "NetworkVarNames", "type_name": "CEnvWindShared"}, {"name": "m_fDirectionVariation", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fSpeedVariation", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fTurbulence", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fVolumeHalfExtentXY", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fVolumeHalfExtentZ", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nVolumeResolutionXY", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nVolumeResolutionZ", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nClipmapLevels", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bIsMaster", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_EnvWindShared": {"fields": {"m_flGustDuration": 36, "m_flInitialWindSpeed": 44, "m_flMaxGustDelay": 32, "m_flMinGustDelay": 28, "m_flStartTime": 8, "m_hEntOwner": 60, "m_iGustDirChange": 40, "m_iInitialWindDir": 42, "m_iMaxGust": 26, "m_iMaxWind": 18, "m_iMinGust": 24, "m_iMinWind": 16, "m_iWindSeed": 12, "m_location": 48, "m_windRadius": 20}, "metadata": [{"name": "m_flStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_iWindSeed", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_iMinWind", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_iMaxWind", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_windRadius", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_iMinGust", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_iMaxGust", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_flMinGustDelay", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flMaxGustDelay", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flGustDuration", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_iGustDirChange", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_iInitialWindDir", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_flInitialWindSpeed", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_location", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": ""}, "C_EnvWindVolume": {"fields": {"m_bActive": 1528, "m_bStartDisabled": 1556, "m_fWindDirectionVariationMultiplier": 1576, "m_fWindSpeedMultiplier": 1564, "m_fWindSpeedVariationMultiplier": 1572, "m_fWindTurbulenceMultiplier": 1568, "m_nShape": 1560, "m_vBoxMaxs": 1544, "m_vBoxMins": 1532}, "metadata": [{"name": "m_bActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vBoxMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vBoxMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nShape", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_fWindSpeedMultiplier", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fWindTurbulenceMultiplier", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fWindSpeedVariationMultiplier", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fWindDirectionVariationMultiplier", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BaseEntity"}, "C_FireCrackerBlast": {"fields": {}, "metadata": [], "parent": "C_Inferno"}, "C_Fish": {"fields": {"m_actualAngles": 4556, "m_actualPos": 4544, "m_angle": 4600, "m_angles": 4480, "m_averageError": 4692, "m_buoyancy": 4504, "m_deathAngle": 4500, "m_deathDepth": 4496, "m_errorHistory": 4604, "m_errorHistoryCount": 4688, "m_errorHistoryIndex": 4684, "m_gotUpdate": 4584, "m_localLifeState": 4492, "m_poolOrigin": 4568, "m_pos": 4456, "m_vel": 4468, "m_waterLevel": 4580, "m_wigglePhase": 4536, "m_wiggleRate": 4540, "m_wiggleTimer": 4512, "m_x": 4588, "m_y": 4592, "m_z": 4596}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_pool<PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_waterLevel", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_x", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_y", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_z", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_angle", "type": "NetworkVarNames", "type_name": "float32"}], "parent": "CBaseAnimGraph"}, "C_Flashbang": {"fields": {}, "metadata": [], "parent": "C_BaseCSGrenade"}, "C_FlashbangProjectile": {"fields": {}, "metadata": [], "parent": "C_BaseCSGrenadeProjectile"}, "C_FogController": {"fields": {"m_bUseAngles": 1632, "m_fog": 1528, "m_iChangedVariables": 1636}, "metadata": [{"name": "m_fog", "type": "NetworkVarNames", "type_name": "fogparams_t"}], "parent": "C_BaseEntity"}, "C_FootstepControl": {"fields": {"m_destination": 4104, "m_source": 4096}, "metadata": [{"name": "m_source", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_destination", "type": "NetworkVarNames", "type_name": "string_t"}], "parent": "C_BaseTrigger"}, "C_FuncBrush": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_FuncConveyor": {"fields": {"m_flCurrentConveyorOffset": 3840, "m_flCurrentConveyorSpeed": 3844, "m_flTargetSpeed": 3796, "m_flTransitionStartSpeed": 3808, "m_hConveyorModels": 3816, "m_nTransitionDurationTicks": 3804, "m_nTransitionStartTick": 3800, "m_vecMoveDirEntitySpace": 3784}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "m_vecMoveDirEntitySpace", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_flTargetSpeed", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nTransitionStartTick", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "m_nTransitionDurationTicks", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flTransitionStartSpeed", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_hConveyorModels", "type": "NetworkVarNames", "type_name": "EHANDLE"}], "parent": "C_BaseModelEntity"}, "C_FuncElectrifiedVolume": {"fields": {"m_EffectName": 3784, "m_bState": 3792, "m_nAmbientEffect": 3776}, "metadata": [{"name": "m_EffectName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_bState", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_FuncBrush"}, "C_FuncLadder": {"fields": {"m_Dismounts": 3792, "m_bDisabled": 3856, "m_bFakeLadder": 3857, "m_bHasSlack": 3858, "m_flAutoRideSpeed": 3852, "m_vecLadderDir": 3776, "m_vecLocalTop": 3816, "m_vecPlayerMountPositionBottom": 3840, "m_vecPlayerMountPositionTop": 3828}, "metadata": [{"name": "m_vecLadderDir", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vecPlayerMountPositionTop", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vecPlayerMountPositionBottom", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_flAutoRideSpeed", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_b<PERSON>ake<PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseModelEntity"}, "C_FuncMonitor": {"fields": {"m_bDraw3DSkybox": 3805, "m_bEnabled": 3804, "m_bRenderShadows": 3788, "m_bUseUniqueColorTarget": 3789, "m_brushModelName": 3792, "m_hTargetCamera": 3800, "m_nResolutionEnum": 3784, "m_targetCamera": 3776}, "metadata": [{"name": "m_targetCamera", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_nResolutionEnum", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bRenderShadows", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bUseUniqueColorTarget", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_brushModelName", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_hTargetCamera", "type": "NetworkVarNames", "type_name": "EHANDLE"}, {"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bDraw3DSkybox", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_FuncBrush"}, "C_FuncMoveLinear": {"fields": {}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}], "parent": "C_BaseToggle"}, "C_FuncMover": {"fields": {}, "metadata": [], "parent": "C_BaseToggle"}, "C_FuncRotating": {"fields": {}, "metadata": [{"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}], "parent": "C_BaseModelEntity"}, "C_FuncTrackTrain": {"fields": {"m_flLineLength": 3784, "m_flRadius": 3780, "m_nLongAxis": 3776}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_GameRules": {"fields": {"__m_pChainEntity": 8, "m_bGamePaused": 56, "m_nPauseStartTick": 52, "m_nTotalPausedTicks": 48}, "metadata": [{"name": "m_nTotalPausedTicks", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPauseStartTick", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bGamePaused", "type": "NetworkVarNames", "type_name": "bool"}], "parent": null}, "C_GameRulesProxy": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "C_GlobalLight": {"fields": {"m_WindClothForceHandle": 2752}, "metadata": [], "parent": "C_BaseEntity"}, "C_GradientFog": {"fields": {"m_bGradientFogNeedsTextures": 1586, "m_bHeightFogEnabled": 1544, "m_bIsEnabled": 1585, "m_bStartDisabled": 1584, "m_flFadeTime": 1580, "m_flFarZ": 1556, "m_flFogEndDistance": 1540, "m_flFogEndHeight": 1552, "m_flFogFalloffExponent": 1564, "m_flFogMaxOpacity": 1560, "m_flFogStartDistance": 1536, "m_flFogStartHeight": 1548, "m_flFogStrength": 1576, "m_flFogVerticalExponent": 1568, "m_fogColor": 1572, "m_hGradientFogTexture": 1528}, "metadata": [{"name": "m_hGradientFogTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_flFogStartDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogEndDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bHeightFogEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flFogStartHeight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogEndHeight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFarZ", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogMaxOpacity", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogFalloffExponent", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogVerticalExponent", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fogColor", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_flFogStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFadeTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsEnabled", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_HEGrenade": {"fields": {}, "metadata": [], "parent": "C_BaseCSGrenade"}, "C_HEGrenadeProjectile": {"fields": {}, "metadata": [], "parent": "C_BaseCSGrenadeProjectile"}, "C_HandleTest": {"fields": {"m_Handle": 1528, "m_bSendHandle": 1532}, "metadata": [{"name": "m_<PERSON>le", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_bSend<PERSON>andle", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_Hostage": {"fields": {"m_bHandsHaveBeenCut": 5196, "m_blinkTimer": 5240, "m_chestAttachment": 5306, "m_entitySpottedState": 5120, "m_eyeAttachment": 5305, "m_fLastGrabTime": 5204, "m_fNewestAlphaThinkTime": 5320, "m_flDeadOrRescuedTime": 5232, "m_flDropStartTime": 5228, "m_flGrabSuccessTime": 5224, "m_flRescueStartTime": 5220, "m_hHostageGrabber": 5200, "m_isInit": 5304, "m_isRescued": 5188, "m_jumpedThisFrame": 5189, "m_leader": 5144, "m_lookAroundTimer": 5280, "m_lookAt": 5264, "m_nHostageState": 5192, "m_pPredictionOwner": 5312, "m_reuseTimer": 5152, "m_vecGrabbedPos": 5208, "m_vel": 5176}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "m_entitySpottedState", "type": "NetworkVarNames", "type_name": "EntitySpottedState_t"}, {"name": "m_leader", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_reuseTimer", "type": "NetworkVarNames", "type_name": "CountdownTimer"}, {"name": "m_vel", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_isRescued", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_jumpedThis<PERSON><PERSON>e", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nHostageState", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bHandsHaveBeenCut", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_hHostageGrabber", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_flRescueStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flGrabSuccessTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flDropStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}], "parent": "C_BaseCombatCharacter"}, "C_HostageCarriableProp": {"fields": {}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_IncendiaryGrenade": {"fields": {}, "metadata": [], "parent": "C_MolotovGrenade"}, "C_Inferno": {"fields": {"m_BurnNormal": 5488, "m_bFireIsBurning": 5424, "m_bInPostEffectTime": 6268, "m_blosCheck": 33940, "m_drawableCount": 33936, "m_fireCount": 6256, "m_fireParentPositions": 4656, "m_firePositions": 3888, "m_flLastGrassBurnThink": 33980, "m_hInfernoClimbingOutlinePointsSnapshot": 3872, "m_hInfernoDecalsSnapshot": 3880, "m_hInfernoFillerPointsSnapshot": 3856, "m_hInfernoOutlinePointsSnapshot": 3864, "m_hInfernoPointsSnapshot": 3848, "m_lastFireCount": 6272, "m_maxBounds": 33968, "m_maxFireHalfWidth": 33948, "m_maxFireHeight": 33952, "m_minBounds": 33956, "m_nFireEffectTickBegin": 6276, "m_nFireLifetime": 6264, "m_nInfernoType": 6260, "m_nfxFireDamageEffect": 3840, "m_nlosperiod": 33944}, "metadata": [{"name": "m_firePositions", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_fireParentPositions", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_bFireIsBurning", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_BurnNormal", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_fireCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nInfernoType", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nFireLifetime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bInPostEffectTime", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nFireEffectTickBegin", "type": "NetworkVarNames", "type_name": "int"}], "parent": "C_BaseModelEntity"}, "C_InfoInstructorHintHostageRescueZone": {"fields": {}, "metadata": [], "parent": "C_PointEntity"}, "C_InfoLadderDismount": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "C_InfoVisibilityBox": {"fields": {"m_bEnabled": 1548, "m_nMode": 1532, "m_vBoxSize": 1536}, "metadata": [{"name": "m_nMode", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vBoxSize", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_Item": {"fields": {"m_pReticleHintTextName": 6384}, "metadata": [], "parent": "C_EconEntity"}, "C_ItemDogtags": {"fields": {"m_KillingPlayer": 6644, "m_OwningPlayer": 6640}, "metadata": [{"name": "m_OwningPlayer", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_KillingPlayer", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}], "parent": "C_Item"}, "C_Item_Healthshot": {"fields": {}, "metadata": [], "parent": "C_WeaponBaseItem"}, "C_KeychainModule": {"fields": {"m_nKeychainDefID": 4464, "m_nKeychainSeed": 4468}, "metadata": [], "parent": "C_CS2WeaponModuleBase"}, "C_Knife": {"fields": {"m_bFirstAttack": 7664}, "metadata": [{"name": "m_bFirstAttack", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_CSWeaponBase"}, "C_LateUpdatedAnimating": {"fields": {}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_LightDirectionalEntity": {"fields": {}, "metadata": [], "parent": "C_LightEntity"}, "C_LightEntity": {"fields": {"m_CLightComponent": 3776}, "metadata": [{"name": "m_CLightComponent", "type": "NetworkVarNames", "type_name": "CLightComponent::Storage_t"}], "parent": "C_BaseModelEntity"}, "C_LightEnvironmentEntity": {"fields": {}, "metadata": [], "parent": "C_LightDirectionalEntity"}, "C_LightOrthoEntity": {"fields": {}, "metadata": [], "parent": "C_LightEntity"}, "C_LightSpotEntity": {"fields": {}, "metadata": [], "parent": "C_LightEntity"}, "C_LocalTempEntity": {"fields": {"bounceFactor": 4480, "die": 4460, "fadeSpeed": 4476, "flags": 4456, "hitSound": 4484, "m_bParticleCollision": 4568, "m_flFrame": 4544, "m_flFrameMax": 4464, "m_flFrameRate": 4540, "m_flSpriteScale": 4532, "m_iLastCollisionFrame": 4572, "m_nFlickerFrame": 4536, "m_pszImpactEffect": 4552, "m_pszParticleEffect": 4560, "m_vLastCollisionOrigin": 4576, "m_vecNormal": 4520, "m_vecPrevAbsOrigin": 4600, "m_vecTempEntAcceleration": 4612, "m_vecTempEntAngVelocity": 4504, "m_vecTempEntVelocity": 4588, "priority": 4488, "tempent_renderamt": 4516, "tentOffset": 4492, "x": 4468, "y": 4472}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_MapPreviewParticleSystem": {"fields": {}, "metadata": [], "parent": "C_ParticleSystem"}, "C_MapVetoPickController": {"fields": {"m_bDisabledHud": 3900, "m_nAccountIDs": 1836, "m_nCurrentPhase": 3884, "m_nDraftType": 1544, "m_nMapId0": 2092, "m_nMapId1": 2348, "m_nMapId2": 2604, "m_nMapId3": 2860, "m_nMapId4": 3116, "m_nMapId5": 3372, "m_nPhaseDurationTicks": 3892, "m_nPhaseStartTick": 3888, "m_nPostDataUpdateTick": 3896, "m_nStartingSide0": 3628, "m_nTeamWinningCoinToss": 1548, "m_nTeamWithFirstChoice": 1552, "m_nVoteMapIdsList": 1808}, "metadata": [{"name": "m_nDraftType", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nTeamWinningCoinToss", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nTeamWithFirstChoice", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nVoteMapIdsList", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nAccountIDs", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nMapId0", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nMapId1", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nMapId2", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nMapId3", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nMapId4", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nMapId5", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nStartingSide0", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nCurrentPhase", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPhaseStartTick", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPhaseDurationTicks", "type": "NetworkVarNames", "type_name": "int"}], "parent": "C_BaseEntity"}, "C_ModelPointEntity": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_MolotovGrenade": {"fields": {}, "metadata": [], "parent": "C_BaseCSGrenade"}, "C_MolotovProjectile": {"fields": {"m_bIsIncGrenade": 5216}, "metadata": [{"name": "m_bIsIncGrenade", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseCSGrenadeProjectile"}, "C_Multimeter": {"fields": {"m_hTargetC4": 4464}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_MultiplayRules": {"fields": {}, "metadata": [], "parent": null}, "C_NametagModule": {"fields": {"m_strNametagString": 4464}, "metadata": [], "parent": "C_CS2WeaponModuleBase"}, "C_NetTestBaseCombatCharacter": {"fields": {}, "metadata": [], "parent": "C_BaseCombatCharacter"}, "C_OmniLight": {"fields": {"m_bShowLight": 4632, "m_flInnerAngle": 4624, "m_flOuterAngle": 4628}, "metadata": [{"name": "m_flInnerAngle", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flOuterAngle", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bShowLight", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BarnLight"}, "C_ParticleSystem": {"fields": {"m_bActive": 4288, "m_bAnimateDuringGameplayPause": 4300, "m_bFrozen": 4289, "m_bNoFreeze": 4629, "m_bNoRamp": 4630, "m_bNoSave": 4628, "m_bOldActive": 5208, "m_bOldFrozen": 5209, "m_bStartActive": 4631, "m_clrTint": 5172, "m_flFreezeTransitionDuration": 4292, "m_flPreSimTime": 4316, "m_flStartTime": 4312, "m_hControlPointEnts": 4372, "m_iEffectIndex": 4304, "m_iServerControlPointAssignments": 4368, "m_iszControlPointNames": 4640, "m_iszEffectName": 4632, "m_nDataCP": 5152, "m_nStopType": 4296, "m_nTintCP": 5168, "m_szSnapshotFileName": 3776, "m_vServerControlPoints": 4320, "m_vecDataCPValue": 5156}, "metadata": [{"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_szSnapshotFileName", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_bActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_b<PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flFreezeTransitionDuration", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nStopType", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bAnimateDuringGameplayPause", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iEffectIndex", "type": "NetworkVarNames", "type_name": "HParticleSystemDefinitionStrong"}, {"name": "m_flStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flPreSimTime", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_vServerControlPoints", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_iServerControlPointAssignments", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_hControlPointEnts", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_bNoSave", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bNoFreeze", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bNoRamp", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseModelEntity"}, "C_PathParticleRope": {"fields": {"m_ColorTint": 1588, "m_PathNodes_Color": 1680, "m_PathNodes_Name": 1552, "m_PathNodes_PinEnabled": 1704, "m_PathNodes_Position": 1608, "m_PathNodes_RadiusScale": 1728, "m_PathNodes_TangentIn": 1632, "m_PathNodes_TangentOut": 1656, "m_bStartActive": 1536, "m_flMaxSimulationTime": 1540, "m_flParticleSpacing": 1576, "m_flRadius": 1584, "m_flSlack": 1580, "m_iEffectIndex": 1600, "m_iszEffectName": 1544, "m_nEffectState": 1592}, "metadata": [{"name": "m_flParticleSpacing", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flSlack", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flRadius", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_ColorTint", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_nEffectState", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iEffectIndex", "type": "NetworkVarNames", "type_name": "HParticleSystemDefinitionStrong"}, {"name": "m_PathNodes_Position", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_PathNodes_TangentIn", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_PathNodes_TangentOut", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_PathNodes_Color", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_PathNodes_PinEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_PathNodes_RadiusScale", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BaseEntity"}, "C_PathParticleRopeAlias_path_particle_rope_clientside": {"fields": {}, "metadata": [], "parent": "C_PathParticleRope"}, "C_PhysBox": {"fields": {}, "metadata": [], "parent": "C_Breakable"}, "C_PhysMagnet": {"fields": {"m_aAttachedObjects": 4480, "m_aAttachedObjectsFromServer": 4456}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_PhysPropClientside": {"fields": {"m_fDeathTime": 4884, "m_flTouchDelta": 4880, "m_nDamageType": 4912, "m_vecDamageDirection": 4900, "m_vecDamagePosition": 4888}, "metadata": [], "parent": "C_BreakableProp"}, "C_PhysicsProp": {"fields": {"m_bAwake": 4880}, "metadata": [{"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_bAwake", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BreakableProp"}, "C_PhysicsPropMultiplayer": {"fields": {}, "metadata": [], "parent": "C_PhysicsProp"}, "C_PlantedC4": {"fields": {"m_AttributeManager": 4560, "m_bBeingDefused": 4524, "m_bBombDefused": 4548, "m_bBombTicking": 4464, "m_bC4Activated": 4536, "m_bCannotBeDefused": 4516, "m_bExplodeWarning": 4532, "m_bHasExploded": 4517, "m_bRadarFlash": 5808, "m_bTenSecWarning": 4537, "m_bTriggerWarning": 4528, "m_entitySpottedState": 4480, "m_fLastDefuseTime": 5816, "m_flC4Blow": 4512, "m_flC4ExplodeSpectateDuration": 5856, "m_flDefuseCountDown": 4544, "m_flDefuseLength": 4540, "m_flNextBeep": 4508, "m_flNextGlow": 4504, "m_flNextRadarFlashTime": 5804, "m_flTimerLength": 4520, "m_hBombDefuser": 4552, "m_hDefuserMultimeter": 5800, "m_nBombSite": 4468, "m_nSourceSoundscapeHash": 4472, "m_pBombDefuser": 5812, "m_pPredictionOwner": 5824, "m_vecC4ExplodeSpectateAng": 5844, "m_vecC4ExplodeSpectatePos": 5832}, "metadata": [{"name": "m_bBombTicking", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nBombSite", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nSourceSoundscapeHash", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_entitySpottedState", "type": "NetworkVarNames", "type_name": "EntitySpottedState_t"}, {"name": "m_flC4Blow", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bCannotBeDefused", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bHasExploded", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flTimer<PERSON><PERSON>th", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bBeingDefused", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flDefuseLength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDefuseCountDown", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bBombDefused", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_hBombDefuser", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_AttributeManager", "type": "NetworkVarNames", "type_name": "CAttributeContainer"}], "parent": "CBaseAnimGraph"}, "C_PlayerPing": {"fields": {"m_bUrgent": 1588, "m_hPingedEntity": 1580, "m_hPlayer": 1576, "m_iType": 1584, "m_szPlaceName": 1589}, "metadata": [{"name": "m_hPlayer", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_hPingedEntity", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_iType", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_b<PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_szPlaceName", "type": "NetworkVarNames", "type_name": "char"}], "parent": "C_BaseEntity"}, "C_PlayerSprayDecal": {"fields": {"m_SprayRenderHelper": 4000, "m_flCreationTime": 3852, "m_nEntity": 3844, "m_nHitbox": 3848, "m_nPlayer": 3840, "m_nTintID": 3856, "m_nUniqueID": 3776, "m_nVersion": 3860, "m_rtGcTime": 3788, "m_ubSignature": 3861, "m_unAccountID": 3780, "m_unTraceID": 3784, "m_vecEndPos": 3792, "m_vecLeft": 3816, "m_vecNormal": 3828, "m_vecStart": 3804}, "metadata": [{"name": "m_nUniqueID", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_unAccountID", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_unTraceID", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_rtGcTime", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_vecEndPos", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vecStart", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vecLeft", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vecNormal", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nPlayer", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nEntity", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nHitbox", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flCreationTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nTintID", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nVersion", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_ubSignature", "type": "NetworkVarNames", "type_name": "uint8"}], "parent": "C_ModelPointEntity"}, "C_PlayerVisibility": {"fields": {"m_bIsEnabled": 1545, "m_bStartDisabled": 1544, "m_flFadeTime": 1540, "m_flFogDistanceMultiplier": 1532, "m_flFogMaxDensityMultiplier": 1536, "m_flVisibilityStrength": 1528}, "metadata": [{"name": "m_flVisibilityStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogDistanceMultiplier", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogMaxDensityMultiplier", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFadeTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsEnabled", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_PointCamera": {"fields": {"m_DegreesPerSecond": 1608, "m_FOV": 1528, "m_FogColor": 1537, "m_Resolution": 1532, "m_TargetFOV": 1604, "m_bActive": 1556, "m_bAlignWithParent": 1581, "m_bCanHLTVUse": 1580, "m_bDofEnabled": 1582, "m_bFogEnable": 1536, "m_bIsOn": 1612, "m_bNoSky": 1564, "m_bUseScreenAspectRatio": 1557, "m_fBrightness": 1568, "m_flAspectRatio": 1560, "m_flDofFarBlurry": 1596, "m_flDofFarCrisp": 1592, "m_flDofNearBlurry": 1584, "m_flDofNearCrisp": 1588, "m_flDofTiltToGround": 1600, "m_flFogEnd": 1548, "m_flFogMaxDensity": 1552, "m_flFogStart": 1544, "m_flZFar": 1572, "m_flZNear": 1576, "m_pNext": 1616}, "metadata": [{"name": "m_FOV", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_Resolution", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bFogEnable", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_FogColor", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_flFogStart", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogEnd", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogMaxDensity", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bUseScreenAspectRatio", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flAspectRatio", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bNoSky", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fBrightness", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flZFar", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flZNear", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bCanHLTVUse", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bAlignWithParent", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bDofEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flDofNearBlurry", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDofNearCrisp", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDofFarCrisp", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDofFarBlurry", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDofTiltToGround", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BaseEntity"}, "C_PointCameraVFOV": {"fields": {"m_flVerticalFOV": 1624}, "metadata": [], "parent": "C_PointCamera"}, "C_PointClientUIDialog": {"fields": {"m_bStartEnabled": 3828, "m_hActivator": 3824}, "metadata": [{"name": "m_hActivator", "type": "NetworkVarNames", "type_name": "EHANDLE"}], "parent": "C_BaseClientUIEntity"}, "C_PointClientUIHUD": {"fields": {"m_bAllowInteractionFromAllSceneWorlds": 4256, "m_bCheckCSSClasses": 3832, "m_bIgnoreInput": 4216, "m_flDPI": 4228, "m_flDepthOffset": 4236, "m_flHeight": 4224, "m_flInteractDistance": 4232, "m_flWidth": 4220, "m_unHorizontalAlign": 4244, "m_unOrientation": 4252, "m_unOwnerContext": 4240, "m_unVerticalAlign": 4248, "m_vecCSSClasses": 4264}, "metadata": [{"name": "m_bIgnoreInput", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flWidth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flHeight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDPI", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flInteractDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDepthOffset", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_unOwnerContext", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_unHorizontalAlign", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_unVerticalAlign", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_unOrientation", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_bAllowInteractionFromAllSceneWorlds", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vecCSSClasses", "type": "NetworkVarNames", "type_name": "string_t"}], "parent": "C_BaseClientUIEntity"}, "C_PointClientUIWorldPanel": {"fields": {"m_anchorDeltaTransform": 3840, "m_bAllowInteractionFromAllSceneWorlds": 4328, "m_bCheckCSSClasses": 3834, "m_bDisableMipGen": 4367, "m_bExcludeFromSaveGames": 4364, "m_bFollowPlayerAcrossTeleport": 4290, "m_bForceRecreateNextUpdate": 3832, "m_bGrabbable": 4365, "m_bIgnoreInput": 4288, "m_bLit": 4289, "m_bMoveViewToPlayerNextThink": 3833, "m_bNoDepth": 4361, "m_bOnlyRenderToTexture": 4366, "m_bOpaque": 4360, "m_bRenderBackface": 4362, "m_bUseOffScreenIndicator": 4363, "m_flDPI": 4300, "m_flDepthOffset": 4308, "m_flHeight": 4296, "m_flInteractDistance": 4304, "m_flWidth": 4292, "m_nExplicitImageLayout": 4368, "m_pOffScreenIndicator": 4248, "m_unHorizontalAlign": 4316, "m_unOrientation": 4324, "m_unOwnerContext": 4312, "m_unVerticalAlign": 4320, "m_vecCSSClasses": 4336}, "metadata": [{"name": "m_bIgnoreInput", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bLit", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bFollowPlayerAcrossTeleport", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flWidth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flHeight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDPI", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flInteractDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDepthOffset", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_unOwnerContext", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_unHorizontalAlign", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_unVerticalAlign", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_unOrientation", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_bAllowInteractionFromAllSceneWorlds", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vecCSSClasses", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_bOpaque", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bNo<PERSON><PERSON>h", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bRenderBackface", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bUseOffScreenIndicator", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bExcludeFromSaveGames", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bGrabbable", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bOnlyRenderToTexture", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bDisableMipGen", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nExplicitImageLayout", "type": "NetworkVarNames", "type_name": "int32"}], "parent": "C_BaseClientUIEntity"}, "C_PointClientUIWorldTextPanel": {"fields": {"m_messageText": 4384}, "metadata": [{"name": "m_messageText", "type": "NetworkVarNames", "type_name": "char"}], "parent": "C_PointClientUIWorldPanel"}, "C_PointCommentaryNode": {"fields": {"m_bActive": 4480, "m_bListenedTo": 4528, "m_bRestartAfterRestore": 4548, "m_bWasActive": 4481, "m_flEndTime": 4484, "m_flStartTime": 4488, "m_flStartTimeInCommentary": 4492, "m_hViewPosition": 4544, "m_iNodeNumber": 4520, "m_iNodeNumberMax": 4524, "m_iszCommentaryFile": 4496, "m_iszSpeakers": 4512, "m_iszTitle": 4504}, "metadata": [{"name": "m_bActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flStartTimeInCommentary", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_iszCommentaryFile", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iszTitle", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iszSpeakers", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iNodeNumber", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iNodeNumberMax", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bListenedTo", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_hViewPosition", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseEntity>"}], "parent": "CBaseAnimGraph"}, "C_PointEntity": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "C_PointValueRemapper": {"fields": {"m_bDisabled": 1528, "m_bDisabledOld": 1529, "m_bEngaged": 1624, "m_bFirstUpdate": 1625, "m_bRequiresUseKey": 1556, "m_bUpdateOnClient": 1530, "m_flCurrentMomentum": 1608, "m_flDisengageDistance": 1548, "m_flEngageDistance": 1552, "m_flInputOffset": 1620, "m_flMaximumChangePerSecond": 1544, "m_flMomentumModifier": 1600, "m_flPreviousUpdateTickTime": 1632, "m_flPreviousValue": 1628, "m_flRatchetOffset": 1616, "m_flSnapValue": 1604, "m_hOutputEntities": 1568, "m_hRemapLineEnd": 1540, "m_hRemapLineStart": 1536, "m_nHapticsType": 1592, "m_nInputType": 1532, "m_nMomentumType": 1596, "m_nOutputType": 1560, "m_nRatchetType": 1612, "m_vecPreviousTestPoint": 1636}, "metadata": [{"name": "m_bDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bUpdateOnClient", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nInputType", "type": "NetworkVarNames", "type_name": "ValueRemapperInputType_t"}, {"name": "m_hRemapLineStart", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_hRemapLineEnd", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_flMaximumChangePerSecond", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDisengageDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flEngageDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bRequiresUseKey", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nOutputType", "type": "NetworkVarNames", "type_name": "ValueRemapperOutputType_t"}, {"name": "m_hOutputEntities", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseEntity>"}, {"name": "m_nHapticsType", "type": "NetworkVarNames", "type_name": "ValueRemapperHapticsType_t"}, {"name": "m_nMomentumType", "type": "NetworkVarNames", "type_name": "ValueRemapperMomentumType_t"}, {"name": "m_flMomentumModifier", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flSnapValue", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nRatchetType", "type": "NetworkVarNames", "type_name": "ValueRemapperRatchetType_t"}, {"name": "m_flInputOffset", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BaseEntity"}, "C_PointWorldText": {"fields": {"m_BackgroundMaterialName": 4384, "m_Color": 4480, "m_FontName": 4320, "m_bDrawBackground": 4464, "m_bEnabled": 4448, "m_bForceRecreateNextUpdate": 3784, "m_bFullbright": 4449, "m_flBackgroundBorderHeight": 4472, "m_flBackgroundBorderWidth": 4468, "m_flBackgroundWorldToUV": 4476, "m_flDepthOffset": 4460, "m_flFontSize": 4456, "m_flWorldUnitsPerPx": 4452, "m_messageText": 3808, "m_nJustifyHorizontal": 4484, "m_nJustifyVertical": 4488, "m_nReorientMode": 4492}, "metadata": [{"name": "m_messageText", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_FontName", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_BackgroundMaterialName", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_b<PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flWorldUnitsPerPx", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFontSize", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDepthOffset", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bDrawBackground", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flBackgroundBorderWidth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flBackgroundBorderHeight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flBackgroundWorldToUV", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_Color", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_nJustifyHorizontal", "type": "NetworkVarNames", "type_name": "PointWorldTextJustifyHorizontal_t"}, {"name": "m_nJustifyVertical", "type": "NetworkVarNames", "type_name": "PointWorldTextJustifyVertical_t"}, {"name": "m_nReorientMode", "type": "NetworkVarNames", "type_name": "PointWorldTextReorientMode_t"}], "parent": "C_ModelPointEntity"}, "C_PortraitWorldCallbackHandler": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "C_PostProcessingVolume": {"fields": {"m_bExposureControl": 4157, "m_bMaster": 4156, "m_flExposureCompensation": 4140, "m_flExposureFadeSpeedDown": 4148, "m_flExposureFadeSpeedUp": 4144, "m_flFadeDuration": 4120, "m_flMaxExposure": 4136, "m_flMaxLogExposure": 4128, "m_flMinExposure": 4132, "m_flMinLogExposure": 4124, "m_flTonemapEVSmoothingRange": 4152, "m_hPostSettings": 4112}, "metadata": [{"name": "m_hPostSettings", "type": "NetworkVarNames", "type_name": "HPostProcessingStrong"}, {"name": "m_flFadeDuration", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flMinLogExposure", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flMaxLogExposure", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flMinExposure", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flMaxExposure", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flExposureCompensation", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flExposureFadeSpeedUp", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flExposureFadeSpeedDown", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flTonemapEVSmoothingRange", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bMaster", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bExposureControl", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseTrigger"}, "C_Precipitation": {"fields": {"m_bActiveParticlePrecipEmitter": 4168, "m_bHasSimulatedSinceLastSceneObjectUpdate": 4170, "m_bParticlePrecipInitialized": 4169, "m_flDensity": 4096, "m_flParticleInnerDist": 4112, "m_nAvailableSheetSequencesMaxIndex": 4172, "m_pParticleDef": 4120, "m_tParticlePrecipTraceTimer": 4160}, "metadata": [], "parent": "C_BaseTrigger"}, "C_PrecipitationBlocker": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_PropDoorRotating": {"fields": {}, "metadata": [], "parent": "C_BasePropDoor"}, "C_RagdollProp": {"fields": {"m_flBlendWeight": 4536, "m_flBlendWeightCurrent": 4548, "m_hRagdollSource": 4540, "m_iEyeAttachment": 4544, "m_parentPhysicsBoneIndices": 4552, "m_ragAngles": 4512, "m_ragEnabled": 4464, "m_ragPos": 4488, "m_worldSpaceBoneComputationOrder": 4576}, "metadata": [{"name": "m_ragEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_ragPos", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_ragAngles", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_flBlendWeight", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_hRagdollSource", "type": "NetworkVarNames", "type_name": "EHANDLE"}], "parent": "CBaseAnimGraph"}, "C_RagdollPropAttached": {"fields": {"m_attachmentPointBoneSpace": 4608, "m_attachmentPointRagdollSpace": 4620, "m_bHasParent": 4648, "m_boneIndexAttached": 4600, "m_parentTime": 4644, "m_ragdollAttachedObjectIndex": 4604, "m_vecOffset": 4632}, "metadata": [{"name": "m_boneIndexAttached", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_ragdollAttachedObjectIndex", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_attachmentPointBoneSpace", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_attachmentPointRagdollSpace", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": "C_RagdollProp"}, "C_RectLight": {"fields": {"m_bShowLight": 4624}, "metadata": [{"name": "m_bShowLight", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BarnLight"}, "C_RetakeGameRules": {"fields": {"m_bBlockersPresent": 252, "m_bRoundInProgress": 253, "m_iBombSite": 260, "m_iFirstSecondHalfRound": 256, "m_nMatchSeed": 248}, "metadata": [{"name": "m_nMatchSeed", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bBlockersPresent", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bRoundInProgress", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iFirstSecondHalfRound", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iBombSite", "type": "NetworkVarNames", "type_name": "int"}], "parent": null}, "C_RopeKeyframe": {"fields": {"m_LinksTouchingSomething": 3784, "m_PhysicsDelegate": 4512, "m_RopeFlags": 3840, "m_RopeLength": 4496, "m_Slack": 4498, "m_Subdiv": 4494, "m_TextureHeight": 4536, "m_TextureScale": 4500, "m_Width": 4508, "m_bApplyWind": 3792, "m_bConstrainBetweenEndpoints": 4648, "m_bEndPointAttachmentAnglesDirty": 0, "m_bEndPointAttachmentPositionsDirty": 0, "m_bNewDataThisFrame": 0, "m_bPhysicsInitted": 0, "m_bPrevEndPointPos": 3804, "m_fLockedPoints": 4504, "m_fPrevLockedPoints": 3796, "m_flCurScroll": 3832, "m_flCurrentGustLifetime": 4568, "m_flCurrentGustTimer": 4564, "m_flScrollSpeed": 3836, "m_flTimeToNextGust": 4572, "m_hEndPoint": 4488, "m_hMaterial": 4528, "m_hStartPoint": 4484, "m_iEndAttachment": 4493, "m_iForcePointMoveCounter": 3800, "m_iRopeMaterialModelIndex": 3848, "m_iStartAttachment": 4492, "m_nChangeCount": 4505, "m_nLinksTouchingSomething": 3788, "m_nSegments": 4480, "m_vCachedEndPointAttachmentAngle": 4624, "m_vCachedEndPointAttachmentPos": 4600, "m_vColorMod": 4588, "m_vPrevEndPointPos": 3808, "m_vWindDir": 4576, "m_vecImpulse": 4540, "m_vecPreviousImpulse": 4552}, "metadata": [{"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_flScrollSpeed", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_RopeFlags", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_iRopeMaterialModelIndex", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_nSegments", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_hStartPoint", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseEntity>"}, {"name": "m_hEndPoint", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseEntity>"}, {"name": "m_iStartAttachment", "type": "NetworkVarNames", "type_name": "AttachmentHandle_t"}, {"name": "m_iEndAttachment", "type": "NetworkVarNames", "type_name": "AttachmentHandle_t"}, {"name": "m_Subdiv", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_RopeLength", "type": "NetworkVarNames", "type_name": "int16"}, {"name": "m_<PERSON>lack", "type": "NetworkVarNames", "type_name": "int16"}, {"name": "m_TextureScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fLockedPoints", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nChangeCount", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_Width", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_bConstrainBetweenEndpoints", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseModelEntity"}, "C_RopeKeyframe__CPhysicsDelegate": {"fields": {"m_pKeyframe": 8}, "metadata": [], "parent": null}, "C_SceneEntity": {"fields": {"m_QueuedEvents": 1592, "m_bAutogenerated": 1539, "m_bClientOnly": 1546, "m_bIsPlayingBack": 1536, "m_bMultiplayer": 1538, "m_bPaused": 1537, "m_bWasPlaying": 1576, "m_flCurrentTime": 1616, "m_flForceClientTime": 1540, "m_hActorList": 1552, "m_hOwner": 1548, "m_nSceneStringIndex": 1544}, "metadata": [{"name": "m_bIsPlayingBack", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bPaused", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bMultiplayer", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bAutogenerated", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flForceClientTime", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nSceneStringIndex", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_hActorList", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseFlex>"}], "parent": "C_PointEntity"}, "C_SceneEntity__QueuedEvents_t": {"fields": {"starttime": 0}, "metadata": [], "parent": null}, "C_ShatterGlassShardPhysics": {"fields": {"m_ShardDesc": 4904}, "metadata": [{"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "m_ShardDesc", "type": "NetworkVarNames", "type_name": "shard_model_desc_t"}], "parent": "C_PhysicsProp"}, "C_SingleplayRules": {"fields": {}, "metadata": [], "parent": null}, "C_SkyCamera": {"fields": {"m_bUseAngles": 1676, "m_pNext": 1680, "m_skyboxData": 1528, "m_skyboxSlotToken": 1672}, "metadata": [{"name": "m_skyboxData", "type": "NetworkVarNames", "type_name": "sky3dparams_t"}, {"name": "m_skyboxSlotToken", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}], "parent": "C_BaseEntity"}, "C_SmokeGrenade": {"fields": {}, "metadata": [], "parent": "C_BaseCSGrenade"}, "C_SmokeGrenadeProjectile": {"fields": {"m_VoxelFrameData": 5280, "m_bDidSmokeEffect": 5244, "m_bSmokeEffectSpawned": 5313, "m_bSmokeVolumeDataReceived": 5312, "m_nRandomSeed": 5248, "m_nSmokeEffectTickBegin": 5240, "m_nVoxelFrameDataSize": 5304, "m_nVoxelUpdate": 5308, "m_vSmokeColor": 5252, "m_vSmokeDetonationPos": 5264}, "metadata": [{"name": "m_nSmokeEffectTickBegin", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bDidSmokeEffect", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nRandomSeed", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vSmokeColor", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vSmokeDetonationPos", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_VoxelFrameData", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nVoxelFrameDataSize", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nVoxelUpdate", "type": "NetworkVarNames", "type_name": "int"}], "parent": "C_BaseCSGrenadeProjectile"}, "C_SoundAreaEntityBase": {"fields": {"m_bDisabled": 1528, "m_bWasEnabled": 1536, "m_iszSoundAreaType": 1544, "m_vPos": 1552}, "metadata": [{"name": "m_bDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iszSoundAreaType", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_vPos", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": "C_BaseEntity"}, "C_SoundAreaEntityOrientedBox": {"fields": {"m_vMax": 1580, "m_vMin": 1568}, "metadata": [{"name": "m_vMin", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vMax", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": "C_SoundAreaEntityBase"}, "C_SoundAreaEntitySphere": {"fields": {"m_flRadius": 1568}, "metadata": [{"name": "m_flRadius", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_SoundAreaEntityBase"}, "C_SoundEventAABBEntity": {"fields": {"m_vMaxs": 1740, "m_vMins": 1728}, "metadata": [{"name": "m_vMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vMaxs", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": "C_SoundEventEntity"}, "C_SoundEventEntity": {"fields": {"m_bClientSideOnly": 0, "m_bSaveRestore": 1531, "m_bSavedIsPlaying": 1532, "m_bStartOnSpawn": 1528, "m_bStopOnNew": 1530, "m_bToLocalPlayer": 1529, "m_flClientCullRadius": 1640, "m_flSavedElapsedTime": 1536, "m_hSource": 1716, "m_iszAttachmentName": 1552, "m_iszSoundName": 1688, "m_iszSourceEntityName": 1544, "m_nEntityIndexSelection": 1720, "m_onGUIDChanged": 1560, "m_onSoundFinished": 1600}, "metadata": [], "parent": "C_BaseEntity"}, "C_SoundEventEntityAlias_snd_event_point": {"fields": {}, "metadata": [], "parent": "C_SoundEventEntity"}, "C_SoundEventOBBEntity": {"fields": {"m_vMaxs": 1740, "m_vMins": 1728}, "metadata": [{"name": "m_vMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vMaxs", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": "C_SoundEventEntity"}, "C_SoundEventPathCornerEntity": {"fields": {"m_vecCornerPairsNetworked": 1728}, "metadata": [{"name": "m_vecCornerPairsNetworked", "type": "NetworkVarNames", "type_name": "SoundeventPathCornerPairNetworked_t"}], "parent": "C_SoundEventEntity"}, "C_SoundEventSphereEntity": {"fields": {"m_flRadius": 1728}, "metadata": [{"name": "m_flRadius", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_SoundEventEntity"}, "C_SoundOpvarSetAABBEntity": {"fields": {}, "metadata": [], "parent": "C_SoundOpvarSetPointEntity"}, "C_SoundOpvarSetAutoRoomEntity": {"fields": {}, "metadata": [], "parent": "C_SoundOpvarSetPointEntity"}, "C_SoundOpvarSetOBBEntity": {"fields": {}, "metadata": [], "parent": "C_SoundOpvarSetAABBEntity"}, "C_SoundOpvarSetOBBWindEntity": {"fields": {}, "metadata": [], "parent": "C_SoundOpvarSetPointBase"}, "C_SoundOpvarSetPathCornerEntity": {"fields": {}, "metadata": [], "parent": "C_SoundOpvarSetPointEntity"}, "C_SoundOpvarSetPointBase": {"fields": {"m_bUseAutoCompare": 1556, "m_iOpvarIndex": 1552, "m_iszOperatorName": 1536, "m_iszOpvarName": 1544, "m_iszStackName": 1528}, "metadata": [{"name": "m_iszStackName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iszOperatorName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iszOpvarName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iOpvarIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bUseAutoCompare", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_SoundOpvarSetPointEntity": {"fields": {}, "metadata": [], "parent": "C_SoundOpvarSetPointBase"}, "C_SpotlightEnd": {"fields": {"m_Radius": 3780, "m_flLightScale": 3776}, "metadata": [{"name": "m_flLightScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_<PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "float32"}], "parent": "C_BaseModelEntity"}, "C_Sprite": {"fields": {"m_bWorldSpaceScale": 3832, "m_flBrightnessDuration": 3820, "m_flBrightnessTimeStart": 3872, "m_flDestScale": 3856, "m_flDieTime": 3800, "m_flFrame": 3796, "m_flGlowProxySize": 3836, "m_flHDRColorScale": 3840, "m_flLastTime": 3844, "m_flMaxFrame": 3848, "m_flScaleDuration": 3828, "m_flScaleTimeStart": 3860, "m_flSpriteFramerate": 3792, "m_flSpriteScale": 3824, "m_flStartScale": 3852, "m_hAttachedToEntity": 3784, "m_hSpriteMaterial": 3776, "m_nAttachment": 3788, "m_nBrightness": 3816, "m_nDestBrightness": 3868, "m_nSpriteHeight": 3892, "m_nSpriteWidth": 3888, "m_nStartBrightness": 3864}, "metadata": [{"name": "m_hSpriteMaterial", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_hAttachedToEntity", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_nAttachment", "type": "NetworkVarNames", "type_name": "AttachmentHandle_t"}, {"name": "m_flSpriteFramerate", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFrame", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nBrightness", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_flBrightnessDuration", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flSpriteScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flScaleDuration", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_bWorldSpaceScale", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flGlowProxySize", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flHDRColorScale", "type": "NetworkVarNames", "type_name": "float32"}], "parent": "C_BaseModelEntity"}, "C_StattrakModule": {"fields": {"m_bKnife": 4464}, "metadata": [], "parent": "C_CS2WeaponModuleBase"}, "C_Team": {"fields": {"m_aPlayerControllers": 1528, "m_aPlayers": 1552, "m_iScore": 1576, "m_szTeamname": 1580}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_aPlayerControllers", "type": "NetworkVarNames", "type_name": "CHandle<CBasePlayerController>"}, {"name": "m_aPlayers", "type": "NetworkVarNames", "type_name": "CHandle<C_BasePlayerPawn>"}, {"name": "m_iScore", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_szTeamname", "type": "NetworkVarNames", "type_name": "char"}], "parent": "C_BaseEntity"}, "C_TeamRoundTimer": {"fields": {"m_bAutoCountdown": 1556, "m_bFire10SecRemain": 1584, "m_bFire1MinRemain": 1582, "m_bFire1SecRemain": 1589, "m_bFire2MinRemain": 1581, "m_bFire2SecRemain": 1588, "m_bFire30SecRemain": 1583, "m_bFire3MinRemain": 1580, "m_bFire3SecRemain": 1587, "m_bFire4MinRemain": 1579, "m_bFire4SecRemain": 1586, "m_bFire5MinRemain": 1578, "m_bFire5SecRemain": 1585, "m_bFireFinished": 1577, "m_bInCaptureWatchState": 1569, "m_bIsDisabled": 1540, "m_bShowInHUD": 1541, "m_bStartPaused": 1568, "m_bStopWatchTimer": 1576, "m_bTimerPaused": 1528, "m_flTimeRemaining": 1532, "m_flTimerEndTime": 1536, "m_flTotalTime": 1572, "m_nOldTimerLength": 1592, "m_nOldTimerState": 1596, "m_nSetupTimeLength": 1560, "m_nState": 1564, "m_nTimerInitialLength": 1548, "m_nTimerLength": 1544, "m_nTimerMaxLength": 1552}, "metadata": [{"name": "m_bTimerPaused", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flTimeRemaining", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flTimerEndTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bIsDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bShowInHUD", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nTimer<PERSON>ength", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nTimerInitialLength", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nTimerMaxLength", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bAutoCountdown", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nSetupTimeLength", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nState", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bStartPaused", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bInCaptureWatchState", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flTotalTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bStopWatchTimer", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_TeamplayRules": {"fields": {}, "metadata": [], "parent": null}, "C_TextureBasedAnimatable": {"fields": {"m_bLoop": 3776, "m_flFPS": 3780, "m_flStartFrame": 3828, "m_flStartTime": 3824, "m_hPositionKeys": 3784, "m_hRotationKeys": 3792, "m_vAnimationBoundsMax": 3812, "m_vAnimationBoundsMin": 3800}, "metadata": [{"name": "m_bLoop", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flFPS", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_hPositionKeys", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_hRotationKeys", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_vAnimationBoundsMin", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vAnimationBoundsMax", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_flStartTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flStartFrame", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BaseModelEntity"}, "C_TintController": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "C_TonemapController2": {"fields": {"m_flAutoExposureMax": 1532, "m_flAutoExposureMin": 1528, "m_flExposureAdaptationSpeedDown": 1540, "m_flExposureAdaptationSpeedUp": 1536, "m_flTonemapEVSmoothingRange": 1544}, "metadata": [{"name": "m_flAutoExposureMin", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flAutoExposureMax", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flExposureAdaptationSpeedUp", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flExposureAdaptationSpeedDown", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flTonemapEVSmoothingRange", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BaseEntity"}, "C_TonemapController2Alias_env_tonemap_controller2": {"fields": {}, "metadata": [], "parent": "C_TonemapController2"}, "C_TriggerBuoyancy": {"fields": {"m_BuoyancyHelper": 4096, "m_flFluidDensity": 4376}, "metadata": [{"name": "m_flFluidDensity", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BaseTrigger"}, "C_TriggerLerpObject": {"fields": {}, "metadata": [], "parent": "C_BaseTrigger"}, "C_TriggerMultiple": {"fields": {}, "metadata": [], "parent": "C_BaseTrigger"}, "C_TriggerPhysics": {"fields": {"m_angularDamping": 4112, "m_angularLimit": 4108, "m_bCollapseToForcePoint": 4140, "m_bConvertToDebrisWhenPossible": 4168, "m_flDampingRatio": 4124, "m_flFrequency": 4120, "m_gravityScale": 4096, "m_linearDamping": 4104, "m_linearForce": 4116, "m_linearLimit": 4100, "m_vecLinearForceDirection": 4156, "m_vecLinearForcePointAt": 4128, "m_vecLinearForcePointAtWorld": 4144}, "metadata": [{"name": "m_gravityScale", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_linearLimit", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_linearDamping", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_angularLimit", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_angularDamping", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_linearForce", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFrequency", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDampingRatio", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_vecLinearForcePointAt", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_bCollapseToForcePoint", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vecLinearForcePointAtWorld", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vecLinearForceDirection", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_bConvertToDebrisWhenPossible", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseTrigger"}, "C_TriggerVolume": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_VoteController": {"fields": {"m_bIsYesNoVote": 1578, "m_bTypeDirty": 1577, "m_bVotesDirty": 1576, "m_iActiveIssueIndex": 1544, "m_iOnlyTeamToVote": 1548, "m_nPotentialVotes": 1572, "m_nVoteOptionCount": 1552}, "metadata": [{"name": "m_iActiveIssueIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iOnlyTeamToVote", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nVoteOptionCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPotentialVotes", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bIsYesNoVote", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_WaterBullet": {"fields": {}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_WeaponAWP": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponAug": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponBaseItem": {"fields": {"m_SequenceCompleteTimer": 7664, "m_bRedraw": 7688}, "metadata": [{"name": "m_SequenceCompleteTimer", "type": "NetworkVarNames", "type_name": "CountdownTimer"}, {"name": "m_bRedraw", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_CSWeaponBase"}, "C_WeaponBizon": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponCZ75a": {"fields": {"m_bMagazineRemoved": 7712}, "metadata": [{"name": "m_bMagazineRemoved", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_CSWeaponBaseGun"}, "C_WeaponElite": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponFamas": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponFiveSeven": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponG3SG1": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponGalilAR": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponGlock": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponHKP2000": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponM249": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponM4A1": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponM4A1Silencer": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponMAC10": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponMP5SD": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponMP7": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponMP9": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponMag7": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponNOVA": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBase"}, "C_WeaponNegev": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponP250": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponP90": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponRevolver": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponSCAR20": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponSG556": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponSSG08": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponSawedoff": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBase"}, "C_WeaponTaser": {"fields": {"m_fFireTime": 7712, "m_nLastAttackTick": 7716}, "metadata": [{"name": "m_fFireTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}], "parent": "C_CSWeaponBaseGun"}, "C_WeaponTec9": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponUMP45": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponUSPSilencer": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponXM1014": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBase"}, "C_World": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_WorldModelGloves": {"fields": {}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_fogplayerparams_t": {"fields": {"m_NewColor": 40, "m_OldColor": 16, "m_flNewEnd": 48, "m_flNewFarZ": 60, "m_flNewHDRColorScale": 56, "m_flNewMaxDensity": 52, "m_flNewStart": 44, "m_flOldEnd": 24, "m_flOldFarZ": 36, "m_flOldHDRColorScale": 32, "m_flOldMaxDensity": 28, "m_flOldStart": 20, "m_flTransitionTime": 12, "m_hCtrl": 8}, "metadata": [{"name": "m_hCtrl", "type": "NetworkVarNames", "type_name": "CHandle<CFogController>"}], "parent": ""}, "CountdownTimer": {"fields": {"m_duration": 8, "m_nWorldGroupId": 20, "m_timescale": 16, "m_timestamp": 12}, "metadata": [{"name": "m_duration", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_timestamp", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_timescale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nWorldGroupId", "type": "NetworkVarNames", "type_name": "WorldGroupId_t"}], "parent": ""}, "EngineCountdownTimer": {"fields": {"m_duration": 8, "m_timescale": 16, "m_timestamp": 12}, "metadata": [{"name": "m_duration", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_timestamp", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_timescale", "type": "NetworkVarNames", "type_name": "float32"}], "parent": ""}, "EntityRenderAttribute_t": {"fields": {"m_ID": 48, "m_Values": 52}, "metadata": [{"name": "m_ID", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}, {"name": "m_Values", "type": "NetworkVarNames", "type_name": "Vector4D"}], "parent": ""}, "EntitySpottedState_t": {"fields": {"m_bSpotted": 8, "m_bSpottedByMask": 12}, "metadata": [{"name": "m_bSpotted", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bSpottedByMask", "type": "NetworkVarNames", "type_name": "uint32"}], "parent": null}, "FilterDamageType": {"fields": {"m_iDamageType": 1616}, "metadata": [], "parent": "CBaseFilter"}, "FilterHealth": {"fields": {"m_bAdrenalineActive": 1616, "m_iHealthMax": 1624, "m_iHealthMin": 1620}, "metadata": [], "parent": "CBaseFilter"}, "IntervalTimer": {"fields": {"m_nWorldGroupId": 12, "m_timestamp": 8}, "metadata": [{"name": "m_timestamp", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_nWorldGroupId", "type": "NetworkVarNames", "type_name": "WorldGroupId_t"}], "parent": ""}, "OutflowWithRequirements_t": {"fields": {"m_Connection": 0, "m_DestinationFlowNodeID": 72, "m_RequirementNodeIDs": 80, "m_nCursorStateBlockIndex": 104}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "PhysicsRagdollPose_t": {"fields": {"m_Transforms": 8, "m_bSetFromDebugHistory": 36, "m_hOwner": 32}, "metadata": [{"name": "m_Transforms", "type": "NetworkVarNames", "type_name": "CTransform"}, {"name": "m_hOwner", "type": "NetworkVarNames", "type_name": "EHANDLE"}], "parent": ""}, "PredictedDamageTag_t": {"fields": {"flFlinchModLarge": 56, "flFlinchModSmall": 52, "flFriendlyFireDamageReductionRatio": 60, "nTagTick": 48}, "metadata": [{"name": "nTagTick", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "flFlinchModSmall", "type": "NetworkVarNames", "type_name": "float"}, {"name": "flFlinchModLarge", "type": "NetworkVarNames", "type_name": "float"}, {"name": "flFriendlyFireDamageReductionRatio", "type": "NetworkVarNames", "type_name": "float"}], "parent": null}, "PulseNodeDynamicOutflows_t": {"fields": {"m_Outflows": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "PulseNodeDynamicOutflows_t__DynamicOutflow_t": {"fields": {"m_Connection": 8, "m_OutflowID": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "PulseObservableBoolExpression_t": {"fields": {"m_DependentObservableBlackboardReferences": 96, "m_DependentObservableVars": 72, "m_EvaluateConnection": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "PulseSelectorOutflowList_t": {"fields": {"m_Outflows": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "SellbackPurchaseEntry_t": {"fields": {"m_bPrevHelmet": 60, "m_hItem": 64, "m_nCost": 52, "m_nPrevArmor": 56, "m_unDefIdx": 48}, "metadata": [{"name": "m_unDefIdx", "type": "NetworkVarNames", "type_name": "item_definition_index_t"}, {"name": "m_nCost", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPrevArmor", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bPrevHelmet", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_hItem", "type": "NetworkVarNames", "type_name": "CEntityHandle"}], "parent": ""}, "SequenceHistory_t": {"fields": {"m_flCyclesPerSecond": 20, "m_flPlaybackRate": 16, "m_flSeqFixedCycle": 8, "m_flSeqStartTime": 4, "m_hSequence": 0, "m_nSeqLoopMode": 12}, "metadata": [], "parent": ""}, "SignatureOutflow_Continue": {"fields": {}, "metadata": [], "parent": null}, "SignatureOutflow_Resume": {"fields": {}, "metadata": [], "parent": null}, "VPhysicsCollisionAttribute_t": {"fields": {"m_nCollisionFunctionMask": 43, "m_nCollisionGroup": 42, "m_nEntityId": 32, "m_nHierarchyId": 40, "m_nInteractsAs": 8, "m_nInteractsExclude": 24, "m_nInteractsWith": 16, "m_nOwnerId": 36}, "metadata": [{"name": "m_nInteractsAs", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_nInteractsWith", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_nInteractsExclude", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_nEntityId", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nOwnerId", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nHierarchyId", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_nCollisionGroup", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nCollisionFunctionMask", "type": "NetworkVarNames", "type_name": "uint8"}], "parent": ""}, "ViewAngleServerChange_t": {"fields": {"nIndex": 64, "nType": 48, "qAngle": 52}, "metadata": [{"name": "nType", "type": "NetworkVarNames", "type_name": "FixAngleSet_t"}, {"name": "qAngle", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "nIndex", "type": "NetworkVarNames", "type_name": "uint32"}], "parent": null}, "WeaponPurchaseCount_t": {"fields": {"m_nCount": 50, "m_nItemDefIndex": 48}, "metadata": [{"name": "m_nItemDefIndex", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_nCount", "type": "NetworkVarNames", "type_name": "uint16"}], "parent": ""}, "WeaponPurchaseTracker_t": {"fields": {"m_weaponPurchases": 8}, "metadata": [{"name": "m_weaponPurchases", "type": "NetworkVarNames", "type_name": "WeaponPurchaseCount_t"}], "parent": ""}, "audioparams_t": {"fields": {"localBits": 108, "localSound": 8, "soundEventHash": 116, "soundscapeEntityListIndex": 112, "soundscapeIndex": 104}, "metadata": [{"name": "localSound", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "soundscapeIndex", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "localBits", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "soundscapeEntityListIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "soundEventHash", "type": "NetworkVarNames", "type_name": "uint32"}], "parent": ""}, "fogparams_t": {"fields": {"HDRColorScale": 56, "blend": 101, "blendtobackground": 88, "colorPrimary": 20, "colorPrimaryLerpTo": 28, "colorSecondary": 24, "colorSecondaryLerpTo": 32, "dirPrimary": 8, "duration": 84, "enable": 100, "end": 40, "endLerpTo": 72, "exponent": 52, "farz": 44, "lerptime": 80, "locallightscale": 96, "m_bPadding": 103, "m_bPadding2": 102, "maxdensity": 48, "maxdensityLerpTo": 76, "scattering": 92, "skyboxFogFactor": 60, "skyboxFogFactorLerpTo": 64, "start": 36, "startLerpTo": 68}, "metadata": [{"name": "dirPrimary", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "colorPrimary", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "colorSecondary", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "colorPrimaryLerpTo", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "colorSecondaryLerpTo", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "start", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "end", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "farz", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "maxdensity", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "exponent", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "HDRColorScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "skyboxFogFactor", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "skyboxFogFactorLerpTo", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "startLerpTo", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "endLerpTo", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "maxdensityLerpTo", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "lerptime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "duration", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "blendtobackground", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "scattering", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "locallightscale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "enable", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "blend", "type": "NetworkVarNames", "type_name": "bool"}], "parent": ""}, "shard_model_desc_t": {"fields": {"m_SurfacePropStringToken": 120, "m_bHasParent": 116, "m_bParentFrozen": 117, "m_flGlassHalfThickness": 112, "m_hMaterialBase": 16, "m_hMaterialDamageOverlay": 24, "m_nModelID": 8, "m_solid": 32, "m_vInitialPanelVertices": 88, "m_vecPanelSize": 36, "m_vecPanelVertices": 64, "m_vecStressPositionA": 44, "m_vecStressPositionB": 52}, "metadata": [{"name": "m_nModelID", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_hMaterialBase", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_hMaterialDamageOverlay", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_solid", "type": "NetworkVarNames", "type_name": "ShardSolid_t"}, {"name": "m_vecPanelSize", "type": "NetworkVarNames", "type_name": "Vector2D"}, {"name": "m_vecStressPositionA", "type": "NetworkVarNames", "type_name": "Vector2D"}, {"name": "m_vecStressPositionB", "type": "NetworkVarNames", "type_name": "Vector2D"}, {"name": "m_vecPanelVertices", "type": "NetworkVarNames", "type_name": "Vector2D"}, {"name": "m_vInitialPanelVertices", "type": "NetworkVarNames", "type_name": "Vector4D"}, {"name": "m_flGlassHalfThickness", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bHasParent", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bParentFrozen", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_SurfacePropStringToken", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}], "parent": ""}, "sky3dparams_t": {"fields": {"bClip3DSkyBoxNearToWorldFar": 24, "flClip3DSkyBoxNearToWorldFarOffset": 28, "fog": 32, "m_nWorldGroupID": 136, "origin": 12, "scale": 8}, "metadata": [{"name": "scale", "type": "NetworkVarNames", "type_name": "int16"}, {"name": "origin", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "bClip3DSkyBoxNearToWorldFar", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "flClip3DSkyBoxNearToWorldFarOffset", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "fog", "type": "NetworkVarNames", "type_name": "fogparams_t"}, {"name": "m_nWorldGroupID", "type": "NetworkVarNames", "type_name": "WorldGroupId_t"}], "parent": ""}}, "enums": {"CompMatPropertyMutatorConditionType_t": {"alignment": 4, "members": {"COMP_MAT_MUTATOR_CONDITION_INPUT_CONTAINER_EXISTS": 0, "COMP_MAT_MUTATOR_CONDITION_INPUT_CONTAINER_VALUE_EQUALS": 2, "COMP_MAT_MUTATOR_CONDITION_INPUT_CONTAINER_VALUE_EXISTS": 1}, "type": "uint32"}, "CompMatPropertyMutatorType_t": {"alignment": 4, "members": {"COMP_MAT_PROPERTY_MUTATOR_CONDITIONAL_MUTATORS": 6, "COMP_MAT_PROPERTY_MUTATOR_COPY_KEYS_WITH_SUFFIX": 2, "COMP_MAT_PROPERTY_MUTATOR_COPY_MATCHING_KEYS": 1, "COMP_MAT_PROPERTY_MUTATOR_COPY_PROPERTY": 3, "COMP_MAT_PROPERTY_MUTATOR_DRAW_TEXT": 8, "COMP_MAT_PROPERTY_MUTATOR_GENERATE_TEXTURE": 5, "COMP_MAT_PROPERTY_MUTATOR_INIT": 0, "COMP_MAT_PROPERTY_MUTATOR_POP_INPUT_QUEUE": 7, "COMP_MAT_PROPERTY_MUTATOR_RANDOM_ROLL_INPUT_VARIABLES": 9, "COMP_MAT_PROPERTY_MUTATOR_SET_VALUE": 4}, "type": "uint32"}, "CompositeMaterialInputContainerSourceType_t": {"alignment": 4, "members": {"CONTAINER_SOURCE_TYPE_LOOSE_VARIABLES": 3, "CONTAINER_SOURCE_TYPE_MATERIAL_FROM_TARGET_ATTR": 1, "CONTAINER_SOURCE_TYPE_SPECIFIC_MATERIAL": 2, "CONTAINER_SOURCE_TYPE_TARGET_INSTANCE_MATERIAL": 5, "CONTAINER_SOURCE_TYPE_TARGET_MATERIAL": 0, "CONTAINER_SOURCE_TYPE_VARIABLE_FROM_TARGET_ATTR": 4}, "type": "uint32"}, "CompositeMaterialInputLooseVariableType_t": {"alignment": 4, "members": {"LOOSE_VARIABLE_TYPE_BOOLEAN": 0, "LOOSE_VARIABLE_TYPE_COLOR4": 9, "LOOSE_VARIABLE_TYPE_FLOAT1": 5, "LOOSE_VARIABLE_TYPE_FLOAT2": 6, "LOOSE_VARIABLE_TYPE_FLOAT3": 7, "LOOSE_VARIABLE_TYPE_FLOAT4": 8, "LOOSE_VARIABLE_TYPE_INTEGER1": 1, "LOOSE_VARIABLE_TYPE_INTEGER2": 2, "LOOSE_VARIABLE_TYPE_INTEGER3": 3, "LOOSE_VARIABLE_TYPE_INTEGER4": 4, "LOOSE_VARIABLE_TYPE_PANORAMA_RENDER": 14, "LOOSE_VARIABLE_TYPE_RESOURCE_MATERIAL": 12, "LOOSE_VARIABLE_TYPE_RESOURCE_TEXTURE": 13, "LOOSE_VARIABLE_TYPE_STRING": 10, "LOOSE_VARIABLE_TYPE_SYSTEMVAR": 11}, "type": "uint32"}, "CompositeMaterialInputTextureType_t": {"alignment": 4, "members": {"INPUT_TEXTURE_TYPE_AO": 6, "INPUT_TEXTURE_TYPE_COLOR": 2, "INPUT_TEXTURE_TYPE_DEFAULT": 0, "INPUT_TEXTURE_TYPE_MASKS": 3, "INPUT_TEXTURE_TYPE_NORMALMAP": 1, "INPUT_TEXTURE_TYPE_PEARLESCENCE_MASK": 5, "INPUT_TEXTURE_TYPE_ROUGHNESS": 4}, "type": "uint32"}, "CompositeMaterialMatchFilterType_t": {"alignment": 4, "members": {"MATCH_FILTER_MATERIAL_ATTRIBUTE_EQUALS": 3, "MATCH_FILTER_MATERIAL_ATTRIBUTE_EXISTS": 0, "MATCH_FILTER_MATERIAL_NAME_SUBSTR": 2, "MATCH_FILTER_MATERIAL_PROPERTY_EQUALS": 5, "MATCH_FILTER_MATERIAL_PROPERTY_EXISTS": 4, "MATCH_FILTER_MATERIAL_SHADER": 1}, "type": "uint32"}, "CompositeMaterialVarSystemVar_t": {"alignment": 4, "members": {"COMPMATSYSVAR_COMPOSITETIME": 0, "COMPMATSYSVAR_EMPTY_RESOURCE_SPACER": 1}, "type": "uint32"}, "InventoryNodeType_t": {"alignment": 4, "members": {"CONCRETE_NODE_SCHEMA_ITEMDEF": 6, "CONCRETE_NODE_SCHEMA_KEYCHAIN": 8, "CONCRETE_NODE_SCHEMA_PREFAB": 5, "CONCRETE_NODE_SCHEMA_STICKER": 7, "NODE_TYPE_INVALID": 0, "VIRTUAL_NODE_SCHEMA_ITEMDEF": 2, "VIRTUAL_NODE_SCHEMA_KEYCHAIN": 4, "VIRTUAL_NODE_SCHEMA_PREFAB": 1, "VIRTUAL_NODE_SCHEMA_STICKER": 3}, "type": "uint32"}}}}