import psutil
import win32con
import win32gui
import win32process

from offsets import *
from random import uniform
import pymem
import time
import keyboard
from pynput.mouse import <PERSON><PERSON>, Controller
import pymem, pymem.process, keyboard, time, os

with open("offsets.json", "r") as f:
    offsets_client_dll = json.load(f)["client.dll"]

# 读结构体成员偏移（假设是 hex 字符串）
with open("client_dll.json", "r") as f:
    client_dll = json.load(f)

# 一次性计算并转换为 hex
dwLocalPlayerController = offsets_client_dll["dwLocalPlayerController"]
dwEntityList           = offsets_client_dll["dwEntityList"]
dwLocalPlayerPawn      = offsets_client_dll["dwLocalPlayerPawn"]
m_iIDEntIndex          = client_dll["client.dll"]["classes"]["C_CSPlayerPawnBase"]["fields"]["m_iIDEntIndex"]
m_iTeamNum             = client_dll["client.dll"]["classes"]["C_BaseEntity"]["fields"]["m_iTeamNum"]
m_iHealth              = client_dll["client.dll"]["classes"]["C_BaseEntity"]["fields"]["m_iHealth"]


def get_cs2_pid():
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            if proc.info['name'].lower() == 'cs2.exe':
                return proc.info['pid']
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    raise RuntimeError("未找到 cs2.exe 进程")


def bring_cs2_to_foreground(pid):
    def enum_window_callback(hwnd, result):
        _, found_pid = win32process.GetWindowThreadProcessId(hwnd)
        if found_pid == pid and win32gui.IsWindowVisible(hwnd):
            result.append(hwnd)

    hwnds = []
    win32gui.EnumWindows(enum_window_callback, hwnds)
    if not hwnds:
        raise RuntimeError("未找到 cs2.exe 窗口")

    hwnd = hwnds[0]
    win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)  # 还原最小化窗口（可选）
    win32gui.SetForegroundWindow(hwnd)  # 设为前台窗口
    time.sleep(0.1)
    return hwnd
# 开枪杀人，传参是杀人数
def circle_shoot(target_kill_count):
    pid = get_cs2_pid()
    hwnd = bring_cs2_to_foreground(pid)
    # 打开进程并获取 client.dll 基址
    pm = pymem.Pymem()
    pm.open_process_from_id(pid)
    client_dll = pymem.process.module_from_name(
        pm.process_handle, "client.dll"
    ).lpBaseOfDll

    mouse = Controller()
    kill_count = 0
    turning_left = True  # 初始左转

    # 按下 Q 键开始转圈
    keyboard.press('q')

    # 上次发现敌人的时间，初始化为现在
    last_detect_time = time.time()

    while kill_count < target_kill_count:

        # 如果 6 秒内都没检测到敌人，停止转圈
        if time.time() - last_detect_time >= 6.0:
            print("6 秒内未发现敌人，停止转圈")
            break

        # 读取本地玩家指针
        player = pm.read_longlong(client_dll + dwLocalPlayerPawn)
        if not player:
            time.sleep(0.01)
            continue

        # 获取玩家实体索引
        entityId = pm.read_int(player + m_iIDEntIndex)
        if entityId <= 0:
            time.sleep(0.01)
            continue

        # 通过 EntityList 找到对应实体指针
        entList = pm.read_longlong(client_dll + dwEntityList)
        entEntry = pm.read_longlong(
            entList + 0x8 * (entityId >> 9) + 0x10
        )
        entity = pm.read_longlong(
            entEntry + 120 * (entityId & 0x1FF)
        )
        if not entity:
            time.sleep(0.01)
            continue

        # 队伍判断，跳过队友
        entityTeam = pm.read_int(entity + m_iTeamNum)
        playerTeam = pm.read_int(player + m_iTeamNum)
        if entityTeam == playerTeam:
            time.sleep(0.01)
            continue

        # 初次检测到一个活敌人，重置计时
        initialHp = pm.read_int(entity + m_iHealth)
        if initialHp <= 0:
            time.sleep(0.03)
            continue
        last_detect_time = time.time()

        # 停止当前转向，准备射击
        if turning_left:
            keyboard.release('q')
        else:
            keyboard.release('e')

        prev_hp = initialHp
        unchanged_count = 0
        hp100_count = 0
        success = False  # 是否成功击杀

        while True:
            entityHp = pm.read_int(entity + m_iHealth)

            if entityHp == prev_hp:
                unchanged_count += 1
            else:
                prev_hp = entityHp
                unchanged_count = 0

            if unchanged_count >= 15:
                break

            if entityHp == 100:
                hp100_count += 1
                if hp100_count >= 3:
                    break
            else:
                hp100_count = 0

            if entityHp <= 0:
                kill_count += 1
                print(f"击杀 +1，当前击杀：{kill_count}/{target_kill_count}")
                success = True
                turning_left = False
                break

            mouse.press(Button.left)
            time.sleep(uniform(0.01, 0.03))
            mouse.release(Button.left)
            time.sleep(uniform(0.01, 0.05))

        # 准备继续转圈
        if kill_count < target_kill_count:
            if success:
                # 击杀成功 → 重置为默认左转
                if not turning_left:
                    keyboard.release('e')
                    keyboard.press('q')
                    turning_left = True
            else:
                # 没打死 → 反方向扫回来
                if turning_left:
                    keyboard.release('q')
                    keyboard.press('e')
                    turning_left = False
                else:
                    keyboard.release('e')
                    keyboard.press('q')
                    turning_left = True
            time.sleep(0.03)

    # 最后一定松开按键
    keyboard.release('q')
    keyboard.release('e')
    print(f"共击杀 {kill_count} 人")


if __name__ == '__main__':
    circle_shoot(4)