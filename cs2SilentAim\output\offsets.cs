// Generated using https://github.com/a2x/cs2-dumper
// 2025-05-16 14:03:14.936842900 UTC

namespace CS2Dumper.Offsets {
    // Module: client.dll
    public static class ClientDll {
        public const nint dwCSGOInput = 0x1A6AF00;
        public const nint dwEntityList = 0x19F7F00;
        public const nint dwGameEntitySystem = 0x1B1B738;
        public const nint dwGameEntitySystem_highestEntityIndex = 0x20F0;
        public const nint dwGameRules = 0x1A5C7E8;
        public const nint dwGlobalVars = 0x1840148;
        public const nint dwGlowManager = 0x1A5BF30;
        public const nint dwLocalPlayerController = 0x1A469E0;
        public const nint dwLocalPlayerPawn = 0x184C0D0;
        public const nint dwPlantedC4 = 0x1A670C8;
        public const nint dwPrediction = 0x184BF50;
        public const nint dwSensitivity = 0x1A5D508;
        public const nint dwSensitivity_sensitivity = 0x40;
        public const nint dwViewAngles = 0x1A6B2D0;
        public const nint dwViewMatrix = 0x1A60EE0;
        public const nint dwViewRender = 0x1A61998;
        public const nint dwWeaponC4 = 0x19FA260;
    }
    // Module: engine2.dll
    public static class Engine2Dll {
        public const nint dwBuildNumber = 0x541BD4;
        public const nint dwNetworkGameClient = 0x540CE0;
        public const nint dwNetworkGameClient_clientTickCount = 0x368;
        public const nint dwNetworkGameClient_deltaTick = 0x27C;
        public const nint dwNetworkGameClient_isBackgroundMap = 0x281447;
        public const nint dwNetworkGameClient_localPlayer = 0xF0;
        public const nint dwNetworkGameClient_maxClients = 0x238;
        public const nint dwNetworkGameClient_serverTickCount = 0x36C;
        public const nint dwNetworkGameClient_signOnState = 0x228;
        public const nint dwWindowHeight = 0x62454C;
        public const nint dwWindowWidth = 0x624548;
    }
    // Module: inputsystem.dll
    public static class InputsystemDll {
        public const nint dwInputSystem = 0x387E0;
    }
    // Module: matchmaking.dll
    public static class MatchmakingDll {
        public const nint dwGameTypes = 0x1A3190;
        public const nint dwGameTypes_mapName = 0x120;
    }
    // Module: soundsystem.dll
    public static class SoundsystemDll {
        public const nint dwSoundSystem = 0x3A15F0;
        public const nint dwSoundSystem_engineViewData = 0x7C;
    }
}
