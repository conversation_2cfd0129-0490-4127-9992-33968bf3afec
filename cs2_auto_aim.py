#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动瞄准
"""

import time
import pymem
from cs2_common_lib import (
    Vector3, QAngle, MathUtils,
    CS2ConfigManager, CS2GameConnector, CS2MemoryReader
)
from offset_manager import get_entity_offset


class CS2Helper:

    def __init__(self):
        # 使用公共配置管理器加载偏移量
        self.offsets = CS2ConfigManager.load_offsets()
        self.client_dll_offsets = CS2ConfigManager.load_client_dll_offsets()

        # 使用公共游戏连接器
        self.game_connector = CS2GameConnector()
        self.memory_reader = None

        # 游戏连接相关（保持兼容性）
        self.pm = None
        self.client_dll = None
        self.entity_list = None
        self.process_handle = None
    
    def connect_to_game(self):
        """连接到CS2游戏"""
        try:
            # 使用公共游戏连接器
            if not self.game_connector.connect_to_game():
                return False

            # 设置兼容性属性
            self.pm = self.game_connector.pm
            self.client_dll = self.game_connector.client_dll
            self.process_handle = self.game_connector.process_handle

            # 初始化内存读取器
            self.memory_reader = CS2MemoryReader(self.game_connector)

            return True

        except Exception as e:
            print(f"连接游戏失败: {e}")
            return False
    

    
    def get_local_player_pawn(self):
        """获取本地玩家Pawn指针"""
        if self.memory_reader:
            return self.memory_reader.get_local_player_pawn()
        try:
            return self.pm.read_longlong(self.client_dll + self.offsets["dwLocalPlayerPawn"])
        except:
            return 0

    def get_view_angle(self):
        """获取当前视角"""
        if self.memory_reader:
            return self.memory_reader.get_view_angle()
        try:
            pitch = pymem.memory.read_float(self.process_handle, self.client_dll + self.offsets["dwViewAngles"])
            yaw = pymem.memory.read_float(self.process_handle, self.client_dll + self.offsets["dwViewAngles"] + 0x4)
            return QAngle(pitch, yaw, 0.0)
        except:
            return QAngle()

    def write_view_angle(self, angle):
        """写入视角"""
        if self.memory_reader:
            return self.memory_reader.write_view_angle(angle)
        try:
            pymem.memory.write_float(self.process_handle, self.client_dll + self.offsets["dwViewAngles"], angle.pitch)
            pymem.memory.write_float(self.process_handle, self.client_dll + self.offsets["dwViewAngles"] + 0x4, angle.yaw)
            return True
        except:
            return False

    def get_player_data(self, index):
        """获取玩家数据"""
        try:
            # 获取实体列表
            entity_list = self.pm.read_longlong(self.client_dll + self.offsets["dwEntityList"])

            # 获取玩家索引
            player_index = index + 1

            # 获取实体指针
            list_entry = self.pm.read_longlong(entity_list + 0x8 * (player_index >> 9) + 16)
            if not list_entry:
                return None

            entity_ptr = self.pm.read_longlong(list_entry + 120 * (player_index & 0x1FF))
            if not entity_ptr:
                return None

            # 获取队伍
            team = self.pm.read_int(entity_ptr + get_entity_offset("m_iTeamNum"))
            if team not in [2, 3]:  # 只处理有效队伍
                return None

            # 获取玩家Pawn
            player_pawn_handle = self.pm.read_int(entity_ptr + get_entity_offset("m_hPlayerPawn"))
            list_entry2 = self.pm.read_longlong(entity_list + 0x8 * ((player_pawn_handle & 0x7FFF) >> 9) + 16)
            if not list_entry2:
                return None

            pawn_ptr = self.pm.read_longlong(list_entry2 + 120 * (player_pawn_handle & 0x1FF))
            if not pawn_ptr:
                return None

            # 获取血量
            health = self.pm.read_int(pawn_ptr + get_entity_offset("m_iHealth"))
            if health <= 0 or health > 100:
                return None

            # 获取坐标
            origin_x = self.pm.read_float(pawn_ptr + get_entity_offset("m_vOldOrigin"))
            origin_y = self.pm.read_float(pawn_ptr + get_entity_offset("m_vOldOrigin") + 4)
            origin_z = self.pm.read_float(pawn_ptr + get_entity_offset("m_vOldOrigin") + 8)

            # 验证坐标有效性
            if origin_x == 0 and origin_y == 0:
                return None

            return {
                'entity_ptr': entity_ptr,
                'pawn_ptr': pawn_ptr,
                'team': team,
                'health': health,
                'origin': (origin_x, origin_y, origin_z)
            }

        except:
            return None

    def get_team_num(self, player_ptr):
        """获取玩家队伍编号"""
        if self.memory_reader:
            return self.memory_reader.get_team_num(player_ptr)
        try:
            return self.pm.read_int(player_ptr + get_entity_offset("m_iTeamNum"))
        except:
            return 0

    def get_life_state(self, player_ptr):
        """获取玩家生命状态（0=活着，1=死亡）"""
        if self.memory_reader:
            return self.memory_reader.get_life_state(player_ptr)
        try:
            return self.pm.read_int(player_ptr + get_entity_offset("m_lifeState"))
        except:
            return 1

    def get_health(self, player_ptr):
        """获取玩家血量"""
        if self.memory_reader:
            return self.memory_reader.get_health(player_ptr)
        try:
            return self.pm.read_int(player_ptr + get_entity_offset("m_iHealth"))
        except:
            return 0

    def is_player_alive(self, player_ptr):
        """检查玩家是否存活"""
        try:
            # 获取血量
            health = self.get_health(player_ptr)

            # 简单有效的存活检测：血量在1-100之间
            is_alive = (health > 0 and health <= 100)

            return is_alive, health

        except:
            return False, 0

    def get_old_origin(self, pawn_ptr):
        """获取玩家位置"""
        if self.memory_reader:
            return self.memory_reader.get_old_origin(pawn_ptr)
        try:
            x = self.pm.read_float(pawn_ptr + get_entity_offset("m_vOldOrigin"))
            y = self.pm.read_float(pawn_ptr + get_entity_offset("m_vOldOrigin") + 4)
            z = self.pm.read_float(pawn_ptr + get_entity_offset("m_vOldOrigin") + 8)
            return Vector3(x, y, z)
        except:
            return Vector3()

    def get_view_offset(self, pawn_ptr):
        """获取视角偏移"""
        if self.memory_reader:
            return self.memory_reader.get_view_offset(pawn_ptr)
        try:
            x = self.pm.read_float(pawn_ptr + get_entity_offset("m_vecViewOffset"))
            y = self.pm.read_float(pawn_ptr + get_entity_offset("m_vecViewOffset") + 4)
            z = self.pm.read_float(pawn_ptr + get_entity_offset("m_vecViewOffset") + 8)
            return Vector3(x, y, z)
        except:
            return Vector3()

    def get_aim_punch_angle(self, pawn_ptr):
        """获取后坐力角度"""
        if self.memory_reader:
            return self.memory_reader.get_aim_punch_angle(pawn_ptr)
        try:
            x = self.pm.read_float(pawn_ptr + get_entity_offset("m_aimPunchAngle"))
            y = self.pm.read_float(pawn_ptr + get_entity_offset("m_aimPunchAngle") + 4)
            z = self.pm.read_float(pawn_ptr + get_entity_offset("m_aimPunchAngle") + 8)
            return QAngle(x, y, z)
        except:
            return QAngle()


class AimBot:
    """自动瞄准类"""

    @staticmethod
    def calculate_view_angle(from_pos, to_pos):
        """计算视角"""
        # 使用公共方法库的数学工具
        return MathUtils.calculate_view_angle(from_pos, to_pos)
    
    @staticmethod
    def get_target_angle(target_data, local_pawn_ptr, helper, debug=False):
        """获取目标角度"""
        try:
            # 获取目标玩家的眼部位置（使用正确的视角偏移）
            target_pawn_ptr = target_data['pawn_ptr']
            target_origin = helper.get_old_origin(target_pawn_ptr)
            target_view_offset = helper.get_view_offset(target_pawn_ptr)
            target_eye = target_origin + target_view_offset

            # 获取本地玩家眼部位置
            local_origin = helper.get_old_origin(local_pawn_ptr)
            local_view_offset = helper.get_view_offset(local_pawn_ptr)
            local_eye = local_origin + local_view_offset

            if debug:
                print(f" 本地原始位置: ({local_origin.x:.1f}, {local_origin.y:.1f}, {local_origin.z:.1f})")
                print(f" 本地视角偏移: ({local_view_offset.x:.1f}, {local_view_offset.y:.1f}, {local_view_offset.z:.1f})")
                print(f" 本地眼部位置: ({local_eye.x:.1f}, {local_eye.y:.1f}, {local_eye.z:.1f})")
                print(f" 目标原始位置: ({target_origin.x:.1f}, {target_origin.y:.1f}, {target_origin.z:.1f})")
                print(f" 目标视角偏移: ({target_view_offset.x:.1f}, {target_view_offset.y:.1f}, {target_view_offset.z:.1f})")
                print(f" 目标眼部位置: ({target_eye.x:.1f}, {target_eye.y:.1f}, {target_eye.z:.1f})")

            # 计算角度
            angle = AimBot.calculate_view_angle(local_eye, target_eye)

            if debug:
                print(f" 原始角度: pitch={angle.pitch:.2f}°, yaw={angle.yaw:.2f}°")

            angle.normalize()

            if debug:
                print(f" 归一化后: pitch={angle.pitch:.2f}°, yaw={angle.yaw:.2f}°")

            return angle

        except Exception as e:
            print(f"计算目标角度失败: {e}")
            return None


class CS2AutoAim:
    """CS2自动瞄准主类"""

    def __init__(self):
        self.helper = CS2Helper()
        self.enabled = False
        self.debug_mode = False  # 调试模式开关
        self.alive_enemies_count = 0  # 存活敌人数量
        self.last_status_time = 0  # 上次状态报告时间
        self.update_interval = 0.01  # 更新间隔（秒）
        self.status_report_interval = 3.0  # 状态报告间隔（秒）
    
    def connect(self):
        """连接到游戏"""
        return self.helper.connect_to_game()
    
    def toggle_aim(self):
        """切换自动瞄准状态"""
        self.enabled = not self.enabled
        print(f"自动瞄准: {'开启' if self.enabled else '关闭'}")

    def toggle_debug(self):
        """切换调试模式"""
        self.debug_mode = not self.debug_mode
        print(f"调试模式: {'开启' if self.debug_mode else '关闭'}")

    def should_report_status(self):
        """检查是否应该报告状态"""
        current_time = time.time()
        if current_time - self.last_status_time >= self.status_report_interval:
            self.last_status_time = current_time
            return True
        return False
    
    def scan_enemies(self):
        """扫描敌人"""
        try:
            # 获取本地玩家
            local_ptr = self.helper.get_local_player_pawn()
            if not local_ptr:
                if self.debug_mode:
                    print("无法获取本地玩家")
                return

            # 获取本地玩家队伍
            local_team = self.helper.get_team_num(local_ptr)
            if not local_team:
                if self.debug_mode:
                    print("无法获取本地玩家队伍")
                return

            # 扫描敌人并计数
            alive_enemies = []
            self.alive_enemies_count = 0

            # 智能扫描：使用修正后的玩家获取方法
            for i in range(32):
                player_data = self.helper.get_player_data(i)
                if not player_data:
                    continue

                # 跳过队友
                if player_data['team'] == local_team:
                    continue

                if self.debug_mode:
                    print(f"玩家 {i}: 队伍={player_data['team']}, 血量={player_data['health']}, 坐标={player_data['origin']}")

                # 玩家数据已经过验证（血量、坐标等），直接计入存活敌人
                self.alive_enemies_count += 1
                alive_enemies.append((i, player_data))

            # 状态报告
            if self.should_report_status():
                print(f"存活敌人数量: {self.alive_enemies_count}")

            # 返回扫描结果
            return {
                'local_ptr': local_ptr,
                'alive_enemies': alive_enemies,
                'enemy_count': self.alive_enemies_count
            }

        except Exception as e:
            print(f"敌人扫描失败: {e}")
            return None

    def apply_aim(self, scan_result):
        """应用自动瞄准"""
        if not scan_result or scan_result['enemy_count'] == 0:
            return

        try:
            local_ptr = scan_result['local_ptr']
            alive_enemies = scan_result['alive_enemies']

            # 获取当前视角
            current_angle = self.helper.get_view_angle()

            # 寻找最近的敌人
            closest_distance = float('inf')
            target_angle = None
            best_target_info = None

            for i, player_data in alive_enemies:
                # 计算目标角度
                angle = AimBot.get_target_angle(player_data, local_ptr, self.helper, debug=self.debug_mode)
                if not angle:
                    continue

                # 计算角度差距
                diff = (angle - current_angle).length_2d()

                if self.debug_mode:
                    print(f"玩家 {i} 角度差距: {diff:.2f}°")

                if diff < closest_distance:
                    closest_distance = diff
                    target_angle = angle
                    best_target_info = (i, player_data['health'])

                    if self.debug_mode:
                        print(f"找到更近目标 {i}，角度: pitch={angle.pitch:.2f}°, yaw={angle.yaw:.2f}°")

            # 如果找到目标，应用瞄准
            if target_angle and best_target_info:
                target_id, target_health = best_target_info

                if self.debug_mode:
                    print(f"瞄准玩家 {target_id} (血量: {target_health}): pitch={target_angle.pitch:.2f}°, yaw={target_angle.yaw:.2f}°")

                success = self.helper.write_view_angle(target_angle)
                if not success and self.debug_mode:
                    print("写入视角失败")

        except Exception as e:
            print(f"自动瞄准应用失败: {e}")

    def update(self):
        """更新主方法（分离扫描和瞄准功能）"""
        # 始终执行敌人扫描
        scan_result = self.scan_enemies()

        # 只有在启用自瞄时才应用瞄准
        if self.enabled and scan_result:
            if scan_result['enemy_count'] == 0:
                if self.debug_mode:
                    print("没有存活敌人，暂停自瞄...")
            else:
                self.apply_aim(scan_result)
