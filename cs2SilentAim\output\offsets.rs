// Generated using https://github.com/a2x/cs2-dumper
// 2025-05-16 14:03:14.936842900 UTC

#![allow(non_upper_case_globals, unused)]

pub mod cs2_dumper {
    pub mod offsets {
        // Module: client.dll
        pub mod client_dll {
            pub const dwCSGOInput: usize = 0x1A6AF00;
            pub const dwEntityList: usize = 0x19F7F00;
            pub const dwGameEntitySystem: usize = 0x1B1B738;
            pub const dwGameEntitySystem_highestEntityIndex: usize = 0x20F0;
            pub const dwGameRules: usize = 0x1A5C7E8;
            pub const dwGlobalVars: usize = 0x1840148;
            pub const dwGlowManager: usize = 0x1A5BF30;
            pub const dwLocalPlayerController: usize = 0x1A469E0;
            pub const dwLocalPlayerPawn: usize = 0x184C0D0;
            pub const dwPlantedC4: usize = 0x1A670C8;
            pub const dwPrediction: usize = 0x184BF50;
            pub const dwSensitivity: usize = 0x1A5D508;
            pub const dwSensitivity_sensitivity: usize = 0x40;
            pub const dwViewAngles: usize = 0x1A6B2D0;
            pub const dwViewMatrix: usize = 0x1A60EE0;
            pub const dwViewRender: usize = 0x1A61998;
            pub const dwWeaponC4: usize = 0x19FA260;
        }
        // Module: engine2.dll
        pub mod engine2_dll {
            pub const dwBuildNumber: usize = 0x541BD4;
            pub const dwNetworkGameClient: usize = 0x540CE0;
            pub const dwNetworkGameClient_clientTickCount: usize = 0x368;
            pub const dwNetworkGameClient_deltaTick: usize = 0x27C;
            pub const dwNetworkGameClient_isBackgroundMap: usize = 0x281447;
            pub const dwNetworkGameClient_localPlayer: usize = 0xF0;
            pub const dwNetworkGameClient_maxClients: usize = 0x238;
            pub const dwNetworkGameClient_serverTickCount: usize = 0x36C;
            pub const dwNetworkGameClient_signOnState: usize = 0x228;
            pub const dwWindowHeight: usize = 0x62454C;
            pub const dwWindowWidth: usize = 0x624548;
        }
        // Module: inputsystem.dll
        pub mod inputsystem_dll {
            pub const dwInputSystem: usize = 0x387E0;
        }
        // Module: matchmaking.dll
        pub mod matchmaking_dll {
            pub const dwGameTypes: usize = 0x1A3190;
            pub const dwGameTypes_mapName: usize = 0x120;
        }
        // Module: soundsystem.dll
        pub mod soundsystem_dll {
            pub const dwSoundSystem: usize = 0x3A15F0;
            pub const dwSoundSystem_engineViewData: usize = 0x7C;
        }
    }
}
