{"particles.dll": {"classes": {"CBaseRendererSource2": {"fields": {"m_bAnimateInFPS": 4008, "m_bBlendFramesSeq0": 10332, "m_bDisableZBuffering": 8912, "m_bGammaCorrectVertexColors": 5788, "m_bMaxLuminanceBlendingSequence0": 10333, "m_bOnlyRenderInEffecsGameOverlay": 8651, "m_bOnlyRenderInEffectsBloomPass": 8648, "m_bOnlyRenderInEffectsWaterPass": 8649, "m_bRefract": 8280, "m_bRefractSolid": 8281, "m_bReverseZBuffering": 8911, "m_bSaturateColorPreAlphaBlend": 5789, "m_bStencilTestExclude": 8780, "m_bTintByFOW": 7208, "m_bTintByGlobalLight": 7209, "m_bUseMixedResolutionRendering": 8650, "m_bWriteStencilOnDepthFail": 8910, "m_bWriteStencilOnDepthPass": 8909, "m_flAddSelfAmount": 5792, "m_flAlphaReferenceSoftness": 7224, "m_flAlphaScale": 880, "m_flAnimationRate": 4000, "m_flBumpStrength": 3968, "m_flCenterXOffset": 3264, "m_flCenterYOffset": 3616, "m_flDepthBias": 9976, "m_flDesaturation": 6144, "m_flDiffuseAmount": 5072, "m_flDiffuseClamp": 5424, "m_flFeatheringFilter": 9624, "m_flFeatheringMaxDist": 9272, "m_flFeatheringMinDist": 8920, "m_flFogAmount": 6856, "m_flMotionVectorScaleU": 4016, "m_flMotionVectorScaleV": 4368, "m_flOverbrightFactor": 6496, "m_flRadiusScale": 528, "m_flRefractAmount": 8288, "m_flRollScale": 1232, "m_flSelfIllumAmount": 4720, "m_flSourceAlphaValueToMapToOne": 7928, "m_flSourceAlphaValueToMapToZero": 7576, "m_nAlpha2Field": 1584, "m_nAlphaReferenceType": 7220, "m_nAnimationType": 4004, "m_nColorBlendType": 3248, "m_nCropTextureOverride": 3972, "m_nFeatheringMode": 8916, "m_nFogType": 6852, "m_nHSVShiftControlPoint": 6848, "m_nLightingControlPoint": 5776, "m_nOutputBlendMode": 5784, "m_nPerParticleAlphaRefWindow": 7216, "m_nPerParticleAlphaReference": 7212, "m_nRefractBlurRadius": 8640, "m_nRefractBlurType": 8644, "m_nSelfIllumPerParticle": 5780, "m_nShaderType": 3252, "m_nSortMethod": 10328, "m_stencilTestID": 8652, "m_stencilWriteID": 8781, "m_strShaderOverride": 3256, "m_vecColorScale": 1592, "m_vecTexturesInput": 3976}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "CBaseTrailRenderer": {"fields": {"m_bClampV": 11640, "m_flEndFadeSize": 11288, "m_flMaxSize": 10932, "m_flMinSize": 10928, "m_flStartFadeSize": 10936, "m_nOrientationControlPoint": 10924, "m_nOrientationType": 10920}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBaseRendererSource2"}, "CGeneralRandomRotation": {"fields": {"m_bRandomlyFlipDirection": 476, "m_flDegrees": 460, "m_flDegreesMax": 468, "m_flDegreesMin": 464, "m_flRotationRandExponent": 472, "m_nFieldOutput": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "CGeneralSpin": {"fields": {"m_fSpinRateStopTime": 460, "m_nSpinRateDegrees": 448, "m_nSpinRateMinDegrees": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "CNewParticleEffect": {"fields": {"m_LastMax": 140, "m_LastMin": 128, "m_RefCount": 192, "m_bAllocated": 0, "m_bAutoUpdateBBox": 0, "m_bCanFreeze": 126, "m_bDisableAggregation": 0, "m_bDontRemove": 0, "m_bForceNoDraw": 0, "m_bFreezeTargetState": 125, "m_bFreezeTransitionActive": 124, "m_bIsFirstFrame": 0, "m_bNeedsBBoxUpdate": 0, "m_bRemove": 0, "m_bShouldCheckFoW": 0, "m_bShouldPerformCullCheck": 0, "m_bShouldSave": 0, "m_bShouldSimulateDuringGamePaused": 0, "m_bSimulate": 0, "m_flFreezeTransitionDuration": 116, "m_flFreezeTransitionOverride": 120, "m_flFreezeTransitionStart": 112, "m_flScale": 76, "m_hOwner": 80, "m_nSplitScreenUser": 152, "m_pDebugName": 40, "m_pNext": 16, "m_pOwningParticleProperty": 88, "m_pParticles": 32, "m_pPrev": 24, "m_vSortOrigin": 64, "m_vecAggregationCenter": 156}, "metadata": [], "parent": "IParticleEffect"}, "CParticleCollectionBindingInstance": {"fields": {}, "metadata": [{"name": "MPulseInstanceDomainInfo", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}, {"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseDomainOptInFeatureTag", "type": "Unknown"}, {"name": "MPulseDomainOptInFeatureTag", "type": "Unknown"}], "parent": "CBasePulseGraphInstance"}, "CParticleCollectionFloatInput": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}], "parent": "CParticleFloatInput"}, "CParticleCollectionRendererFloatInput": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}], "parent": "CParticleCollectionFloatInput"}, "CParticleCollectionRendererVecInput": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}], "parent": "CParticleCollectionVecInput"}, "CParticleCollectionVecInput": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}], "parent": "CParticleVecInput"}, "CParticleFloatInput": {"fields": {"m_Curve": 288, "m_NamedValue": 32, "m_bHasRandomSignFlip": 120, "m_bNoiseImgPreviewLive": 212, "m_bUseBoundsCenter": 228, "m_flBiasParameter": 280, "m_flInput0": 240, "m_flInput1": 244, "m_flLOD0": 136, "m_flLOD1": 140, "m_flLOD2": 144, "m_flLOD3": 148, "m_flLiteralValue": 24, "m_flMultFactor": 236, "m_flNoCameraFallback": 224, "m_flNoiseImgPreviewScale": 208, "m_flNoiseOffset": 180, "m_flNoiseOutputMax": 160, "m_flNoiseOutputMin": 156, "m_flNoiseScale": 164, "m_flNoiseTurbulenceMix": 204, "m_flNoiseTurbulenceScale": 200, "m_flNotchedOutputInside": 268, "m_flNotchedOutputOutside": 264, "m_flNotchedRangeMax": 260, "m_flNotchedRangeMin": 256, "m_flOutput0": 248, "m_flOutput1": 252, "m_flRandomMax": 116, "m_flRandomMin": 112, "m_nBiasType": 276, "m_nControlPoint": 96, "m_nInputMode": 232, "m_nMapType": 20, "m_nNoiseInputVectorAttribute": 152, "m_nNoiseModifier": 196, "m_nNoiseOctaves": 184, "m_nNoiseTurbulence": 188, "m_nNoiseType": 192, "m_nRandomMode": 128, "m_nRandomSeed": 124, "m_nRoundType": 272, "m_nScalarAttribute": 100, "m_nType": 16, "m_nVectorAttribute": 104, "m_nVectorComponent": 108, "m_vecNoiseOffsetRate": 168}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MParticleCustomFieldDefaultValue", "type": "Unknown"}], "parent": "CParticleInput"}, "CParticleFunction": {"fields": {"m_Notes": 416, "m_bDisableOperator": 414, "m_bNormalizeToStopTime": 384, "m_flOpEndFadeInTime": 368, "m_flOpEndFadeOutTime": 376, "m_flOpFadeOscillatePeriod": 380, "m_flOpStartFadeInTime": 364, "m_flOpStartFadeOutTime": 372, "m_flOpStrength": 8, "m_flOpTimeOffsetMax": 392, "m_flOpTimeOffsetMin": 388, "m_flOpTimeScaleMax": 408, "m_flOpTimeScaleMin": 404, "m_nOpEndCapState": 360, "m_nOpTimeOffsetSeed": 396, "m_nOpTimeScaleSeed": 400}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CParticleFunctionConstraint": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction"}, "CParticleFunctionEmitter": {"fields": {"m_nEmitterIndex": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction"}, "CParticleFunctionForce": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction"}, "CParticleFunctionInitializer": {"fields": {"m_nAssociatedEmitterIndex": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction"}, "CParticleFunctionOperator": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction"}, "CParticleFunctionPreEmission": {"fields": {"m_bRunOnce": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "CParticleFunctionRenderer": {"fields": {"VisibilityInputs": 448, "m_bCannotBeRefracted": 520, "m_bSkipRenderingOnMobile": 521}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction"}, "CParticleInput": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CParticleMassCalculationParameters": {"fields": {"m_flNominalRadius": 360, "m_flRadius": 8, "m_flScale": 712, "m_nMassMode": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CParticleModelInput": {"fields": {"m_NamedValue": 24, "m_nControlPoint": 88, "m_nType": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}, {"name": "MParticleCustomFieldDefaultValue", "type": "Unknown"}], "parent": "CParticleInput"}, "CParticleProperty": {"fields": {}, "metadata": [], "parent": null}, "CParticleRemapFloatInput": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}], "parent": "CParticleFloatInput"}, "CParticleSystemDefinition": {"fields": {"m_BoundingBoxMax": 552, "m_BoundingBoxMin": 540, "m_Children": 184, "m_ConstantColor": 608, "m_ConstantNormal": 612, "m_Constraints": 136, "m_Emitters": 40, "m_ForceGenerators": 112, "m_Initializers": 64, "m_NamedValueDomain": 576, "m_NamedValueLocals": 584, "m_Operators": 88, "m_PreEmissionOperators": 16, "m_Renderers": 160, "m_bEnableNamedValues": 573, "m_bInfiniteBounds": 572, "m_bScreenSpaceEffect": 788, "m_bShouldBatch": 780, "m_bShouldHitboxesFallbackToCollisionHulls": 783, "m_bShouldHitboxesFallbackToRenderBounds": 781, "m_bShouldHitboxesFallbackToSnapshot": 782, "m_bShouldSort": 808, "m_controlPointConfigurations": 880, "m_flAggregateRadius": 776, "m_flConstantLifespan": 636, "m_flConstantRadius": 624, "m_flConstantRotation": 628, "m_flConstantRotationSpeed": 632, "m_flCullFillCost": 676, "m_flCullRadius": 672, "m_flDepthSortBias": 564, "m_flMaxCreationDistance": 768, "m_flMaxDrawDistance": 760, "m_flMaximumSimTime": 732, "m_flMaximumTimeStep": 728, "m_flMinimumSimTime": 736, "m_flMinimumTimeStep": 740, "m_flNoDrawTimeToGoToSleep": 756, "m_flPreSimulationTime": 720, "m_flStartFadeDistance": 764, "m_flStopSimulationAfterTime": 724, "m_hFallback": 688, "m_hLowViolenceDef": 704, "m_hReferenceReplacement": 712, "m_hSnapshot": 656, "m_nAggregationMinAvailableParticles": 772, "m_nAllowRenderControlPoint": 804, "m_nBehaviorVersion": 8, "m_nConstantSequenceNumber": 640, "m_nConstantSequenceNumber1": 644, "m_nCullControlPoint": 680, "m_nFallbackMaxCount": 696, "m_nFirstMultipleOverride_BackwardCompat": 376, "m_nGroupID": 536, "m_nInitialParticles": 528, "m_nMaxParticles": 532, "m_nMinCPULevel": 748, "m_nMinGPULevel": 752, "m_nMinimumFrames": 744, "m_nSkipRenderControlPoint": 800, "m_nSnapshotControlPoint": 648, "m_nSortOverridePositionCP": 568, "m_nViewModelEffect": 784, "m_pszCullReplacementName": 664, "m_pszTargetLayerID": 792}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "IParticleSystemDefinition"}, "CParticleTransformInput": {"fields": {"m_NamedValue": 24, "m_bFollowNamedValue": 88, "m_bSupportsDisabled": 89, "m_bUseOrientation": 90, "m_flEndCPGrowthTime": 100, "m_nControlPoint": 92, "m_nControlPointRangeMax": 96, "m_nType": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}, {"name": "MParticleCustomFieldDefaultValue", "type": "Unknown"}], "parent": "CParticleInput"}, "CParticleVariableRef": {"fields": {"m_variableName": 0, "m_variableType": 56}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}], "parent": null}, "CParticleVecInput": {"fields": {"m_FloatComponentX": 168, "m_FloatComponentY": 520, "m_FloatComponentZ": 872, "m_FloatInterp": 1224, "m_Gradient": 1608, "m_LiteralColor": 32, "m_NamedValue": 40, "m_bFollowNamedValue": 104, "m_flInterpInput0": 1576, "m_flInterpInput1": 1580, "m_nControlPoint": 124, "m_nDeltaControlPoint": 128, "m_nType": 16, "m_nVectorAttribute": 108, "m_vCPRelativeDir": 156, "m_vCPRelativePosition": 144, "m_vCPValueScale": 132, "m_vInterpOutput0": 1584, "m_vInterpOutput1": 1596, "m_vLiteralValue": 20, "m_vRandomMax": 1644, "m_vRandomMin": 1632, "m_vVectorAttributeScale": 112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MParticleCustomFieldDefaultValue", "type": "Unknown"}], "parent": "CParticleInput"}, "CParticleVisibilityInputs": {"fields": {"m_bDotCPAngles": 44, "m_bDotCameraAngles": 45, "m_bRightEye": 68, "m_flAlphaScaleMax": 52, "m_flAlphaScaleMin": 48, "m_flCameraBias": 0, "m_flDistanceInputMax": 32, "m_flDistanceInputMin": 28, "m_flDotInputMax": 40, "m_flDotInputMin": 36, "m_flInputMax": 16, "m_flInputMin": 12, "m_flInputPixelVisFade": 20, "m_flNoPixelVisibilityFallback": 24, "m_flProxyRadius": 8, "m_flRadiusScaleFOVBase": 64, "m_flRadiusScaleMax": 60, "m_flRadiusScaleMin": 56, "m_nCPin": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPathParameters": {"fields": {"m_flBulge": 12, "m_flMidPoint": 16, "m_nBulgeControl": 8, "m_nEndControlPointNumber": 4, "m_nStartControlPointNumber": 0, "m_vEndOffset": 44, "m_vMidPointOffset": 32, "m_vStartPointOffset": 20}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CPerParticleFloatInput": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}], "parent": "CParticleFloatInput"}, "CPerParticleVecInput": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertyCustomEditor", "type": "Unknown"}], "parent": "CParticleVecInput"}, "CRandomNumberGeneratorParameters": {"fields": {"m_bDistributeEvenly": 0, "m_nSeed": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CReplicationParameters": {"fields": {"m_bScaleChildParticleRadii": 4, "m_flMaxRandomRadiusScale": 360, "m_flMinRandomRadiusScale": 8, "m_flModellingScale": 4024, "m_nReplicationMode": 0, "m_vMaxRandomDisplacement": 2368, "m_vMinRandomDisplacement": 712}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CSpinUpdateBase": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_INIT_AddVectorToVector": {"fields": {"m_nFieldInput": 472, "m_nFieldOutput": 468, "m_randomnessParameters": 500, "m_vOffsetMax": 488, "m_vOffsetMin": 476, "m_vecScale": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_AgeNoise": {"fields": {"m_bAbsVal": 456, "m_bAbsValInv": 457, "m_flAgeMax": 468, "m_flAgeMin": 464, "m_flNoiseScale": 472, "m_flNoiseScaleLoc": 476, "m_flOffset": 460, "m_vecOffsetLoc": 480}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_ChaoticAttractor": {"fields": {"m_bUniformSpeed": 488, "m_flAParm": 456, "m_flBParm": 460, "m_flCParm": 464, "m_flDParm": 468, "m_flScale": 472, "m_flSpeedMax": 480, "m_flSpeedMin": 476, "m_nBaseCP": 484}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_ColorLitPerParticle": {"fields": {"m_ColorMax": 484, "m_ColorMin": 480, "m_TintMax": 492, "m_TintMin": 488, "m_flLightAmplification": 504, "m_flTintPerc": 496, "m_nTintBlendMode": 500}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateAlongPath": {"fields": {"m_PathParams": 464, "m_bSaveOffset": 544, "m_bUseRandomCPs": 528, "m_fMaxDistance": 456, "m_vEndOffset": 532}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateFromCPs": {"fields": {"m_nDynamicCPCount": 472, "m_nIncrement": 456, "m_nMaxCP": 464, "m_nMinCP": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateFromParentParticles": {"fields": {"m_bRandomDistribution": 464, "m_bSubFrame": 472, "m_flIncrement": 460, "m_flVelocityScale": 456, "m_nRandomSeed": 468}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateFromPlaneCache": {"fields": {"m_bUseNormal": 481, "m_vecOffsetMax": 468, "m_vecOffsetMin": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateInEpitrochoid": {"fields": {"m_TransformInput": 464, "m_bOffsetExistingPos": 1978, "m_bUseCount": 1976, "m_bUseLocalCoords": 1977, "m_flOffset": 920, "m_flParticleDensity": 568, "m_flRadius1": 1272, "m_flRadius2": 1624, "m_nComponent1": 456, "m_nComponent2": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateOnGrid": {"fields": {"m_bCenter": 2573, "m_bHollow": 2574, "m_bLocalSpace": 2572, "m_nControlPointNumber": 2568, "m_nXCount": 456, "m_nXSpacing": 1512, "m_nYCount": 808, "m_nYSpacing": 1864, "m_nZCount": 1160, "m_nZSpacing": 2216}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateOnModel": {"fields": {"m_HitboxSetName": 4344, "m_bEvenDistribution": 661, "m_bLocalCoords": 4472, "m_bScaleToVolume": 660, "m_bUseBones": 4473, "m_bUseMesh": 4474, "m_flBoneVelocity": 2680, "m_flMaxBoneVelocity": 2684, "m_flShellSize": 4480, "m_modelInput": 456, "m_nDesiredHitbox": 664, "m_nForceInModel": 656, "m_nHitboxValueFromControlPointIndex": 1016, "m_transformInput": 552, "m_vecDirectionBias": 2688, "m_vecHitBoxScale": 1024}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateOnModelAtHeight": {"fields": {"m_HitboxSetName": 4142, "m_bForceZ": 457, "m_bLocalCoords": 4140, "m_bPreferMovingBoxes": 4141, "m_bUseBones": 456, "m_bUseWaterHeight": 468, "m_flDesiredHeight": 472, "m_flHitboxVelocityScale": 4272, "m_flMaxBoneVelocity": 4624, "m_nBiasType": 4136, "m_nControlPointNumber": 460, "m_nHeightCP": 464, "m_vecDirectionBias": 2480, "m_vecHitBoxScale": 824}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateParticleImpulse": {"fields": {"m_InputFalloffExp": 1168, "m_InputMagnitude": 808, "m_InputRadius": 456, "m_nFalloffFunction": 1160, "m_nImpulseType": 1520}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreatePhyllotaxis": {"fields": {"m_bUseLocalCoords": 500, "m_bUseOrigRadius": 502, "m_bUseWithContEmit": 501, "m_fDistBias": 496, "m_fMinRad": 492, "m_fRadBias": 488, "m_fRadCentCore": 468, "m_fRadPerPoint": 472, "m_fRadPerPointTo": 476, "m_fpointAngle": 480, "m_fsizeOverall": 484, "m_nComponent": 464, "m_nControlPointNumber": 456, "m_nScaleCP": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateSequentialPath": {"fields": {"m_PathParams": 480, "m_bCPPairs": 465, "m_bLoop": 464, "m_bSaveOffset": 466, "m_fMaxDistance": 456, "m_flNumToAssign": 460}, "metadata": [{"name": "MParticleMaxVersion", "type": "Unknown"}, {"name": "MParticleReplacementOp", "type": "Unknown"}, {"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateSequentialPathV2": {"fields": {"m_PathParams": 1168, "m_bCPPairs": 1161, "m_bLoop": 1160, "m_bSaveOffset": 1162, "m_fMaxDistance": 456, "m_flNumToAssign": 808}, "metadata": [{"name": "MParticleMinVersion", "type": "Unknown"}, {"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateSpiralSphere": {"fields": {"m_bUseParticleCount": 480, "m_flInitialRadius": 468, "m_flInitialSpeedMax": 476, "m_flInitialSpeedMin": 472, "m_nControlPointNumber": 456, "m_nDensity": 464, "m_nOverrideCP": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateWithinBox": {"fields": {"m_bLocalSpace": 3772, "m_nControlPointNumber": 3768, "m_randomnessParameters": 3776, "m_vecMax": 2112, "m_vecMin": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreateWithinSphereTransform": {"fields": {"m_LocalCoordinateSystemSpeedMax": 5312, "m_LocalCoordinateSystemSpeedMin": 3656, "m_TransformInput": 2832, "m_bLocalCoords": 3644, "m_fRadiusMax": 808, "m_fRadiusMin": 456, "m_fSpeedMax": 3288, "m_fSpeedMin": 2936, "m_fSpeedRandExp": 3640, "m_flEndCPGrowthTime": 3648, "m_nFieldOutput": 6968, "m_nFieldVelocity": 6972, "m_vecDistanceBias": 1160, "m_vecDistanceBiasAbs": 2816}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_CreationNoise": {"fields": {"m_bAbsVal": 460, "m_bAbsValInv": 461, "m_flNoiseScale": 476, "m_flNoiseScaleLoc": 480, "m_flOffset": 464, "m_flOutputMax": 472, "m_flOutputMin": 468, "m_flWorldTimeScale": 496, "m_nFieldOutput": 456, "m_vecOffsetLoc": 484}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_DistanceCull": {"fields": {"m_bCullInside": 816, "m_flDistance": 464, "m_nControlPoint": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_DistanceToCPInit": {"fields": {"m_CollisionGroupName": 1877, "m_bActiveRange": 2376, "m_bLOS": 1876, "m_flInputMax": 816, "m_flInputMin": 464, "m_flLOSScale": 2368, "m_flMaxTraceLength": 2016, "m_flOutputMax": 1520, "m_flOutputMin": 1168, "m_flRemapBias": 2392, "m_nFieldOutput": 456, "m_nSetMethod": 2372, "m_nStartCP": 1872, "m_nTraceSet": 2008, "m_vecDistanceScale": 2380}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_DistanceToNeighborCull": {"fields": {"m_bIncludeRadii": 808, "m_flDistance": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_GlobalScale": {"fields": {"m_bScalePosition": 469, "m_bScaleRadius": 468, "m_bScaleVelocity": 470, "m_flScale": 456, "m_nControlPointNumber": 464, "m_nScaleControlPointNumber": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InheritFromParentParticles": {"fields": {"m_bRandomDistribution": 468, "m_flScale": 456, "m_nFieldOutput": 460, "m_nIncrement": 464, "m_nRandomSeed": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InheritVelocity": {"fields": {"m_flVelocityScale": 460, "m_nControlPointNumber": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitFloat": {"fields": {"m_InputStrength": 816, "m_InputValue": 456, "m_nOutputField": 808, "m_nSetMethod": 812}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitFloatCollection": {"fields": {"m_InputValue": 456, "m_nOutputField": 808}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitFromCPSnapshot": {"fields": {"m_bLocalSpaceAngles": 1188, "m_bRandom": 472, "m_bReverse": 473, "m_nAttributeToRead": 460, "m_nAttributeToWrite": 464, "m_nControlPointNumber": 456, "m_nLocalSpaceCP": 468, "m_nManualSnapshotIndex": 832, "m_nRandomSeed": 1184, "m_nSnapShotIncrement": 480}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitFromParentKilled": {"fields": {"m_nAttributeToCopy": 456, "m_nEventType": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitFromVectorFieldSnapshot": {"fields": {"m_bUseVerticalVelocity": 468, "m_nControlPointNumber": 456, "m_nLocalSpaceCP": 460, "m_nWeightUpdateCP": 464, "m_vecScale": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitSkinnedPositionFromCPSnapshot": {"fields": {"m_bCopyAlpha": 861, "m_bCopyColor": 860, "m_bIgnoreDt": 474, "m_bRandom": 464, "m_bRigid": 472, "m_bSetNormal": 473, "m_bSetRadius": 862, "m_flBoneVelocity": 852, "m_flBoneVelocityMax": 856, "m_flIncrement": 840, "m_flMaxNormalVelocity": 480, "m_flMinNormalVelocity": 476, "m_flReadIndex": 488, "m_nControlPointNumber": 460, "m_nFullLoopIncrement": 844, "m_nIndexType": 484, "m_nRandomSeed": 468, "m_nSnapShotStartPoint": 848, "m_nSnapshotControlPointNumber": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitVec": {"fields": {"m_InputValue": 456, "m_bNormalizedOutput": 2120, "m_bWritePreviousPosition": 2121, "m_nOutputField": 2112, "m_nSetMethod": 2116}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitVecCollection": {"fields": {"m_InputValue": 456, "m_nOutputField": 2112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitialRepulsionVelocity": {"fields": {"m_CollisionGroupName": 456, "m_bInherit": 625, "m_bPerParticle": 616, "m_bPerParticleTR": 624, "m_bProportional": 618, "m_bTranslate": 617, "m_flTraceLength": 620, "m_nChildCP": 628, "m_nChildGroupID": 632, "m_nControlPointNumber": 612, "m_nTraceSet": 584, "m_vecOutputMax": 600, "m_vecOutputMin": 588}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitialSequenceFromModel": {"fields": {"m_flInputMax": 472, "m_flInputMin": 468, "m_flOutputMax": 480, "m_flOutputMin": 476, "m_nControlPointNumber": 456, "m_nFieldOutput": 460, "m_nFieldOutputAnim": 464, "m_nSetMethod": 484}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitialVelocityFromHitbox": {"fields": {"m_HitboxSetName": 468, "m_bUseBones": 596, "m_flVelocityMax": 460, "m_flVelocityMin": 456, "m_nControlPointNumber": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_InitialVelocityNoise": {"fields": {"m_TransformInput": 6504, "m_bIgnoreDt": 6608, "m_flNoiseScale": 5800, "m_flNoiseScaleLoc": 6152, "m_flOffset": 2136, "m_vecAbsVal": 456, "m_vecAbsValInv": 468, "m_vecOffsetLoc": 480, "m_vecOutputMax": 4144, "m_vecOutputMin": 2488}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_LifespanFromVelocity": {"fields": {"m_CollisionGroupName": 488, "m_bIncludeWater": 632, "m_flMaxTraceLength": 472, "m_flTraceOffset": 468, "m_flTraceTolerance": 476, "m_nMaxPlanes": 480, "m_nTraceSet": 616, "m_vecComponentScale": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_ModelCull": {"fields": {"m_HitboxSetName": 463, "m_bBoundBox": 460, "m_bCullOutside": 461, "m_bUseBones": 462, "m_nControlPointNumber": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_MoveBetweenPoints": {"fields": {"m_bTrailBias": 2220, "m_flEndOffset": 1864, "m_flEndSpread": 1160, "m_flSpeedMax": 808, "m_flSpeedMin": 456, "m_flStartOffset": 1512, "m_nEndControlPointNumber": 2216}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_NormalAlignToCP": {"fields": {"m_nControlPointAxis": 560, "m_transformInput": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_NormalOffset": {"fields": {"m_OffsetMax": 468, "m_OffsetMin": 456, "m_bLocalCoords": 484, "m_bNormalize": 485, "m_nControlPointNumber": 480}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_OffsetVectorToVector": {"fields": {"m_nFieldInput": 456, "m_nFieldOutput": 460, "m_randomnessParameters": 488, "m_vecOutputMax": 476, "m_vecOutputMin": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_Orient2DRelToCP": {"fields": {"m_flRotOffset": 464, "m_nCP": 456, "m_nFieldOutput": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_PlaneCull": {"fields": {"m_bCullInside": 816, "m_flDistance": 464, "m_nControlPoint": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_PointList": {"fields": {"m_bClosedLoop": 489, "m_bPlaceAlongPath": 488, "m_nFieldOutput": 456, "m_nNumPointsAlongPath": 492, "m_pointList": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_PositionOffset": {"fields": {"m_OffsetMax": 2112, "m_OffsetMin": 456, "m_TransformInput": 3768, "m_bLocalCoords": 3872, "m_bProportional": 3873, "m_randomnessParameters": 3876}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_PositionOffsetToCP": {"fields": {"m_bLocalCoords": 464, "m_nControlPointNumberEnd": 460, "m_nControlPointNumberStart": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_PositionPlaceOnGround": {"fields": {"m_CollisionGroupName": 1160, "m_bIncludeWater": 1308, "m_bOffsetonColOnly": 1312, "m_bSetNormal": 1309, "m_bSetPXYZOnly": 1310, "m_bTraceAlongNormal": 1311, "m_flMaxTraceLength": 808, "m_flOffset": 456, "m_flOffsetByRadiusFactor": 1316, "m_nIgnoreCP": 1324, "m_nPreserveOffsetCP": 1320, "m_nTraceMissBehavior": 1304, "m_nTraceSet": 1288}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_PositionWarp": {"fields": {"m_bInvertWarp": 3792, "m_bUseCount": 3793, "m_flPrevPosScale": 3788, "m_flWarpStartTime": 3784, "m_flWarpTime": 3780, "m_nControlPointNumber": 3772, "m_nRadiusComponent": 3776, "m_nScaleControlPointNumber": 3768, "m_vecWarpMax": 2112, "m_vecWarpMin": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_PositionWarpScalar": {"fields": {"m_InputValue": 480, "m_flPrevPosScale": 832, "m_nControlPointNumber": 840, "m_nScaleControlPointNumber": 836, "m_vecWarpMax": 468, "m_vecWarpMin": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_QuantizeFloat": {"fields": {"m_InputValue": 456, "m_nOutputField": 808}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RadiusFromCPObject": {"fields": {"m_nControlPoint": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomAlpha": {"fields": {"m_flAlphaRandExponent": 476, "m_nAlphaMax": 464, "m_nAlphaMin": 460, "m_nFieldOutput": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomAlphaWindowThreshold": {"fields": {"m_flExponent": 464, "m_flMax": 460, "m_flMin": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomColor": {"fields": {"m_ColorMax": 488, "m_ColorMin": 484, "m_TintMax": 496, "m_TintMin": 492, "m_flLightAmplification": 520, "m_flTintPerc": 500, "m_flUpdateThreshold": 504, "m_nFieldOutput": 512, "m_nTintBlendMode": 516, "m_nTintCP": 508}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomLifeTime": {"fields": {"m_fLifetimeMax": 460, "m_fLifetimeMin": 456, "m_fLifetimeRandExponent": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomModelSequence": {"fields": {"m_ActivityName": 456, "m_SequenceName": 712, "m_hModel": 968}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomNamedModelBodyPart": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RandomNamedModelElement"}, "C_INIT_RandomNamedModelElement": {"fields": {"m_bLinear": 489, "m_bModelFromRenderer": 490, "m_bShuffle": 488, "m_hModel": 456, "m_nFieldOutput": 492, "m_names": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomNamedModelMeshGroup": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RandomNamedModelElement"}, "C_INIT_RandomNamedModelSequence": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RandomNamedModelElement"}, "C_INIT_RandomRadius": {"fields": {"m_flRadiusMax": 460, "m_flRadiusMin": 456, "m_flRadiusRandExponent": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomRotation": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CGeneralRandomRotation"}, "C_INIT_RandomRotationSpeed": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CGeneralRandomRotation"}, "C_INIT_RandomScalar": {"fields": {"m_flExponent": 464, "m_flMax": 460, "m_flMin": 456, "m_nFieldOutput": 468}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomSecondSequence": {"fields": {"m_nSequenceMax": 460, "m_nSequenceMin": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomSequence": {"fields": {"m_WeightedList": 472, "m_bLinear": 465, "m_bShuffle": 464, "m_nSequenceMax": 460, "m_nSequenceMin": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomTrailLength": {"fields": {"m_flLengthRandExponent": 464, "m_flMaxLength": 460, "m_flMinLength": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomVector": {"fields": {"m_nFieldOutput": 480, "m_randomnessParameters": 484, "m_vecMax": 468, "m_vecMin": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomVectorComponent": {"fields": {"m_flMax": 460, "m_flMin": 456, "m_nComponent": 468, "m_nFieldOutput": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RandomYaw": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CGeneralRandomRotation"}, "C_INIT_RandomYawFlip": {"fields": {"m_flPercent": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapCPtoScalar": {"fields": {"m_flEndTime": 488, "m_flInputMax": 472, "m_flInputMin": 468, "m_flOutputMax": 480, "m_flOutputMin": 476, "m_flRemapBias": 496, "m_flStartTime": 484, "m_nCPInput": 456, "m_nField": 464, "m_nFieldOutput": 460, "m_nSetMethod": 492}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapInitialDirectionToTransformToVector": {"fields": {"m_TransformInput": 456, "m_bNormalize": 584, "m_flOffsetRot": 568, "m_flScale": 564, "m_nFieldOutput": 560, "m_vecOffsetAxis": 572}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapInitialTransformDirectionToRotation": {"fields": {"m_TransformInput": 456, "m_flOffsetRot": 564, "m_nComponent": 568, "m_nFieldOutput": 560}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapInitialVisibilityScalar": {"fields": {"m_flInputMax": 468, "m_flInputMin": 464, "m_flOutputMax": 476, "m_flOutputMin": 472, "m_nFieldOutput": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapNamedModelBodyPartToScalar": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RemapNamedModelElementToScalar"}, "C_INIT_RemapNamedModelElementToScalar": {"fields": {"m_bModelFromRenderer": 524, "m_hModel": 456, "m_nFieldInput": 512, "m_nFieldOutput": 516, "m_nSetMethod": 520, "m_names": 464, "m_values": 488}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapNamedModelMeshGroupToScalar": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RemapNamedModelElementToScalar"}, "C_INIT_RemapNamedModelSequenceToScalar": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RemapNamedModelElementToScalar"}, "C_INIT_RemapParticleCountToNamedModelBodyPartScalar": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RemapParticleCountToNamedModelElementScalar"}, "C_INIT_RemapParticleCountToNamedModelElementScalar": {"fields": {"m_bModelFromRenderer": 528, "m_hModel": 504, "m_outputMaxName": 520, "m_outputMinName": 512}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RemapParticleCountToScalar"}, "C_INIT_RemapParticleCountToNamedModelMeshGroupScalar": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RemapParticleCountToNamedModelElementScalar"}, "C_INIT_RemapParticleCountToNamedModelSequenceScalar": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_INIT_RemapParticleCountToNamedModelElementScalar"}, "C_INIT_RemapParticleCountToScalar": {"fields": {"m_bActiveRange": 488, "m_bInvert": 489, "m_bWrap": 490, "m_flOutputMax": 480, "m_flOutputMin": 476, "m_flRemapBias": 492, "m_nFieldOutput": 456, "m_nInputMax": 464, "m_nInputMin": 460, "m_nScaleControlPoint": 468, "m_nScaleControlPointField": 472, "m_nSetMethod": 484}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapQAnglesToRotation": {"fields": {"m_TransformInput": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapScalar": {"fields": {"m_bActiveRange": 492, "m_flEndTime": 484, "m_flInputMax": 468, "m_flInputMin": 464, "m_flOutputMax": 476, "m_flOutputMin": 472, "m_flRemapBias": 496, "m_flStartTime": 480, "m_nFieldInput": 456, "m_nFieldOutput": 460, "m_nSetMethod": 488}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapScalarToVector": {"fields": {"m_bLocalCoords": 512, "m_flEndTime": 500, "m_flInputMax": 468, "m_flInputMin": 464, "m_flRemapBias": 516, "m_flStartTime": 496, "m_nControlPointNumber": 508, "m_nFieldInput": 456, "m_nFieldOutput": 460, "m_nSetMethod": 504, "m_vecOutputMax": 484, "m_vecOutputMin": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapSpeedToScalar": {"fields": {"m_bPerParticle": 492, "m_flEndTime": 468, "m_flInputMax": 476, "m_flInputMin": 472, "m_flOutputMax": 484, "m_flOutputMin": 480, "m_flStartTime": 464, "m_nControlPointNumber": 460, "m_nFieldOutput": 456, "m_nSetMethod": 488}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapTransformOrientationToRotations": {"fields": {"m_TransformInput": 456, "m_bUseQuat": 572, "m_bWriteNormal": 573, "m_vecRotation": 560}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RemapTransformToVector": {"fields": {"m_LocalSpaceTransform": 616, "m_TransformInput": 512, "m_bAccelerate": 733, "m_bOffset": 732, "m_flEndTime": 724, "m_flRemapBias": 736, "m_flStartTime": 720, "m_nFieldOutput": 456, "m_nSetMethod": 728, "m_vInputMax": 472, "m_vInputMin": 460, "m_vOutputMax": 496, "m_vOutputMin": 484}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RingWave": {"fields": {"m_TransformInput": 456, "m_bEvenDistribution": 3376, "m_bXYVelocityOnly": 3377, "m_flInitialRadius": 912, "m_flInitialSpeedMax": 1968, "m_flInitialSpeedMin": 1616, "m_flParticlesPerOrbit": 560, "m_flPitch": 2672, "m_flRoll": 2320, "m_flThickness": 1264, "m_flYaw": 3024}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_RtEnvCull": {"fields": {"m_RtEnvName": 483, "m_bCullOnMiss": 481, "m_bLifeAdjust": 482, "m_bUseVelocity": 480, "m_nComponent": 616, "m_nRTEnvCP": 612, "m_vecTestDir": 456, "m_vecTestNormal": 468}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_ScaleVelocity": {"fields": {"m_vecScale": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_SequenceFromCP": {"fields": {"m_bKillUnused": 456, "m_bRadiusScale": 457, "m_nCP": 460, "m_vecOffset": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_SequenceLifeTime": {"fields": {"m_flFramerate": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_SetAttributeToScalarExpression": {"fields": {"m_flInput1": 464, "m_flInput2": 816, "m_flOutputRemap": 1168, "m_nExpression": 456, "m_nOutputField": 1520, "m_nSetMethod": 1524}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_SetHitboxToClosest": {"fields": {"m_HitboxSetName": 2120, "m_bUpdatePosition": 2608, "m_bUseBones": 2248, "m_bUseClosestPointOnHitbox": 2249, "m_flHybridRatio": 2256, "m_nControlPointNumber": 456, "m_nDesiredHitbox": 460, "m_nTestType": 2252, "m_vecHitBoxScale": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_SetHitboxToModel": {"fields": {"m_HitboxSetName": 2142, "m_bEvenDistribution": 464, "m_bMaintainHitbox": 2140, "m_bUseBones": 2141, "m_flShellSize": 2272, "m_nControlPointNumber": 456, "m_nDesiredHitbox": 468, "m_nForceInModel": 460, "m_vecDirectionBias": 2128, "m_vecHitBoxScale": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_SetRigidAttachment": {"fields": {"m_bLocalSpace": 468, "m_nControlPointNumber": 456, "m_nFieldInput": 460, "m_nFieldOutput": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_SetVectorAttributeToVectorExpression": {"fields": {"m_bNormalizedOutput": 3784, "m_nExpression": 456, "m_nOutputField": 3776, "m_nSetMethod": 3780, "m_vInput1": 464, "m_vInput2": 2120}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_StatusEffect": {"fields": {"m_flAmbientScale": 484, "m_flColorWarpIntensity": 472, "m_flDetail2BlendFactor": 468, "m_flDetail2Rotation": 460, "m_flDetail2Scale": 464, "m_flDiffuseWarpBlendToFull": 476, "m_flEnvMapIntensity": 480, "m_flMetalnessBlendToFull": 520, "m_flReflectionsTintByBaseBlendToNone": 516, "m_flRimLightScale": 512, "m_flSelfIllumBlendToFull": 524, "m_flSpecularBlendToFull": 504, "m_flSpecularExponent": 496, "m_flSpecularExponentBlendToFull": 500, "m_flSpecularScale": 492, "m_nDetail2Combo": 456, "m_rimLightColor": 508, "m_specularColor": 488}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_StatusEffectCitadel": {"fields": {"m_flSFXColorWarpAmount": 456, "m_flSFXMetalnessAmount": 464, "m_flSFXNormalAmount": 460, "m_flSFXRoughnessAmount": 468, "m_flSFXSDetailAmount": 508, "m_flSFXSDetailScale": 512, "m_flSFXSDetailScrollX": 516, "m_flSFXSDetailScrollY": 520, "m_flSFXSDetailScrollZ": 524, "m_flSFXSOffsetX": 492, "m_flSFXSOffsetY": 496, "m_flSFXSOffsetZ": 500, "m_flSFXSScale": 476, "m_flSFXSScrollX": 480, "m_flSFXSScrollY": 484, "m_flSFXSScrollZ": 488, "m_flSFXSUseModelUVs": 528, "m_flSFXSelfIllumAmount": 472, "m_nDetailCombo": 504}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_VelocityFromCP": {"fields": {"m_bDirectionOnly": 2220, "m_flVelocityScale": 2216, "m_transformInput": 2112, "m_velocityInput": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_VelocityFromNormal": {"fields": {"m_bIgnoreDt": 464, "m_fSpeedMax": 460, "m_fSpeedMin": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_VelocityRadialRandom": {"fields": {"m_bIgnoreDelta": 1181, "m_fSpeedMax": 816, "m_fSpeedMin": 464, "m_nControlPointNumber": 456, "m_vecLocalCoordinateSystemSpeedScale": 1168}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_INIT_VelocityRandom": {"fields": {"m_LocalCoordinateSystemSpeedMax": 2824, "m_LocalCoordinateSystemSpeedMin": 1168, "m_bIgnoreDT": 4480, "m_fSpeedMax": 816, "m_fSpeedMin": 464, "m_nControlPointNumber": 456, "m_randomnessParameters": 4484}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionInitializer"}, "C_OP_AlphaDecay": {"fields": {"m_flMinAlpha": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_AttractToControlPoint": {"fields": {"m_TransformInput": 840, "m_bApplyMinForce": 1296, "m_fFalloffPower": 832, "m_fForceAmount": 480, "m_fForceAmountMin": 944, "m_vecComponentScale": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_BasicMovement": {"fields": {"m_Gravity": 448, "m_bUseNewCode": 3524, "m_fDrag": 2104, "m_massControls": 2456, "m_nMaxConstraintPasses": 3520}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_BoxConstraint": {"fields": {"m_bAccountForRadius": 3765, "m_bLocalSpace": 3764, "m_nCP": 3760, "m_vecMax": 2104, "m_vecMin": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_CPOffsetToPercentageBetweenCPs": {"fields": {"m_bRadialCheck": 480, "m_bScaleOffset": 481, "m_flInputBias": 456, "m_flInputMax": 452, "m_flInputMin": 448, "m_nEndCP": 464, "m_nInputCP": 476, "m_nOffsetCP": 468, "m_nOuputCP": 472, "m_nStartCP": 460, "m_vecOffset": 484}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_CPVelocityForce": {"fields": {"m_flScale": 472, "m_nControlPointNumber": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_CalculateVectorAttribute": {"fields": {"m_flControlPointScale1": 496, "m_flControlPointScale2": 520, "m_flInputScale1": 464, "m_flInputScale2": 472, "m_nControlPointInput1": 476, "m_nControlPointInput2": 500, "m_nFieldInput1": 460, "m_nFieldInput2": 468, "m_nFieldOutput": 524, "m_vFinalOutputScale": 528, "m_vStartValue": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_Callback": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_ChladniWave": {"fields": {"m_b3D": 5184, "m_flInputMax": 808, "m_flInputMin": 456, "m_flOutputMax": 1512, "m_flOutputMin": 1160, "m_nFieldOutput": 448, "m_nLocalSpaceControlPoint": 5180, "m_nSetMethod": 5176, "m_vecHarmonics": 3520, "m_vecWaveLength": 1864}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ChooseRandomChildrenInGroup": {"fields": {"m_flNumberOfChildren": 464, "m_nChildGroupID": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_ClampScalar": {"fields": {"m_flOutputMax": 808, "m_flOutputMin": 456, "m_nFieldOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ClampVector": {"fields": {"m_nFieldOutput": 448, "m_vecOutputMax": 2112, "m_vecOutputMin": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ClientPhysics": {"fields": {"m_bDeleteSim": 1258, "m_bKillParticles": 1257, "m_bRespectExclusionVolumes": 1256, "m_bStartAsleep": 536, "m_bUseHighQualitySimulation": 1248, "m_flPlayerWakeRadius": 544, "m_flVehicleWakeRadius": 896, "m_nColorBlendType": 1264, "m_nControlPoint": 1260, "m_nMaxParticleCount": 1252, "m_strPhysicsType": 528}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_CollideWithParentParticles": {"fields": {"m_flParentRadiusScale": 448, "m_flRadiusScale": 800}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_CollideWithSelf": {"fields": {"m_flMinimumSpeed": 800, "m_flRadiusScale": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_ColorAdjustHSL": {"fields": {"m_flHueAdjust": 448, "m_flLightnessAdjust": 1152, "m_flSaturationAdjust": 800}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ColorInterpolate": {"fields": {"m_ColorFade": 448, "m_bEaseInOut": 476, "m_flFadeEndTime": 468, "m_flFadeStartTime": 464, "m_nFieldOutput": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ColorInterpolateRandom": {"fields": {"m_ColorFadeMax": 476, "m_ColorFadeMin": 448, "m_bEaseInOut": 504, "m_flFadeEndTime": 496, "m_flFadeStartTime": 492, "m_nFieldOutput": 500}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ConnectParentParticleToNearest": {"fields": {"m_bUseRadius": 456, "m_flParentRadiusScale": 816, "m_flRadiusScale": 464, "m_nFirstControlPoint": 448, "m_nSecondControlPoint": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ConstrainDistance": {"fields": {"m_CenterOffset": 1156, "m_bGlobalCenter": 1168, "m_fMaxDistance": 800, "m_fMinDistance": 448, "m_nControlPointNumber": 1152}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_ConstrainDistanceToPath": {"fields": {"m_PathParameters": 464, "m_fMinDistance": 448, "m_flMaxDistance0": 452, "m_flMaxDistance1": 460, "m_flMaxDistanceMid": 456, "m_flTravelTime": 528, "m_nFieldScale": 532, "m_nManualTField": 536}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_ConstrainDistanceToUserSpecifiedPath": {"fields": {"m_bLoopedPath": 460, "m_fMinDistance": 448, "m_flMaxDistance": 452, "m_flTimeScale": 456, "m_pointList": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_ConstrainLineLength": {"fields": {"m_flMaxDistance": 452, "m_flMinDistance": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_ContinuousEmitter": {"fields": {"m_bForceEmitOnFirstUpdate": 1536, "m_bForceEmitOnLastUpdate": 1537, "m_bInitFromKilledParentParticles": 1520, "m_flEmissionDuration": 456, "m_flEmissionScale": 1512, "m_flEmitRate": 1160, "m_flScalePerParentParticle": 1516, "m_flStartTime": 808, "m_nEventType": 1524, "m_nLimitPerUpdate": 1532, "m_nSnapshotControlPoint": 1528}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionEmitter"}, "C_OP_ControlPointToRadialScreenSpace": {"fields": {"m_nCPIn": 456, "m_nCPOut": 472, "m_nCPOutField": 476, "m_nCPSSPosOut": 480, "m_vecCP1Pos": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_ControlpointLight": {"fields": {"m_LightColor1": 1712, "m_LightColor2": 1716, "m_LightColor3": 1720, "m_LightColor4": 1724, "m_LightFiftyDist1": 1680, "m_LightFiftyDist2": 1688, "m_LightFiftyDist3": 1696, "m_LightFiftyDist4": 1704, "m_LightZeroDist1": 1684, "m_LightZeroDist2": 1692, "m_LightZeroDist3": 1700, "m_LightZeroDist4": 1708, "m_bClampLowerRange": 1742, "m_bClampUpperRange": 1743, "m_bLightDynamic1": 1732, "m_bLightDynamic2": 1733, "m_bLightDynamic3": 1734, "m_bLightDynamic4": 1735, "m_bLightType1": 1728, "m_bLightType2": 1729, "m_bLightType3": 1730, "m_bLightType4": 1731, "m_bUseHLambert": 1737, "m_bUseNormal": 1736, "m_flScale": 448, "m_nControlPoint1": 1616, "m_nControlPoint2": 1620, "m_nControlPoint3": 1624, "m_nControlPoint4": 1628, "m_vecCPOffset1": 1632, "m_vecCPOffset2": 1644, "m_vecCPOffset3": 1656, "m_vecCPOffset4": 1668}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_Cull": {"fields": {"m_flCullEnd": 456, "m_flCullExp": 460, "m_flCullPerc": 448, "m_flCullStart": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_CurlNoiseForce": {"fields": {"m_flWorleyJitter": 7448, "m_flWorleySeed": 7096, "m_nNoiseType": 464, "m_vecNoiseFreq": 472, "m_vecNoiseScale": 2128, "m_vecOffset": 3784, "m_vecOffsetRate": 5440}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_CycleScalar": {"fields": {"m_bDoNotRepeatCycle": 464, "m_bSynchronizeParticles": 465, "m_flCycleTime": 460, "m_flEndValue": 456, "m_flStartValue": 452, "m_nCPFieldMax": 476, "m_nCPFieldMin": 472, "m_nCPScale": 468, "m_nDestField": 448, "m_nSetMethod": 480}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_CylindricalDistanceToTransform": {"fields": {"m_TransformEnd": 1968, "m_TransformStart": 1864, "m_bActiveRange": 2076, "m_bAdditive": 2077, "m_bCapsule": 2078, "m_flInputMax": 808, "m_flInputMin": 456, "m_flOutputMax": 1512, "m_flOutputMin": 1160, "m_nFieldOutput": 448, "m_nSetMethod": 2072}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DampenToCP": {"fields": {"m_flRange": 452, "m_flScale": 456, "m_nControlPointNumber": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_Decay": {"fields": {"m_bForcePreserveParticleOrder": 449, "m_bRopeDecay": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DecayClampCount": {"fields": {"m_nCount": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DecayMaintainCount": {"fields": {"m_bKillNewest": 816, "m_bLifespanDecay": 460, "m_flDecayDelay": 452, "m_flScale": 464, "m_nParticlesToMaintain": 448, "m_nSnapshotControlPoint": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DecayOffscreen": {"fields": {"m_flOffscreenTime": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DensityForce": {"fields": {"m_flForceScale": 468, "m_flRadiusScale": 464, "m_flTargetDensity": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_DifferencePreviousParticle": {"fields": {"m_bActiveRange": 476, "m_bSetPreviousParticle": 477, "m_flInputMax": 460, "m_flInputMin": 456, "m_flOutputMax": 468, "m_flOutputMin": 464, "m_nFieldInput": 448, "m_nFieldOutput": 452, "m_nSetMethod": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_Diffusion": {"fields": {"m_flRadiusScale": 448, "m_nFieldOutput": 452, "m_nVoxelGridResolution": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DirectionBetweenVecsToVec": {"fields": {"m_nFieldOutput": 448, "m_vecPoint1": 456, "m_vecPoint2": 2112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DistanceBetweenCPsToCP": {"fields": {"m_CollisionGroupName": 501, "m_bLOS": 500, "m_bSetOnce": 472, "m_flInputMax": 480, "m_flInputMin": 476, "m_flLOSScale": 496, "m_flMaxTraceLength": 492, "m_flOutputMax": 488, "m_flOutputMin": 484, "m_nEndCP": 460, "m_nOutputCP": 464, "m_nOutputCPField": 468, "m_nSetParent": 636, "m_nStartCP": 456, "m_nTraceSet": 632}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_DistanceBetweenTransforms": {"fields": {"m_CollisionGroupName": 2080, "m_TransformEnd": 560, "m_TransformStart": 456, "m_bLOS": 2212, "m_flInputMax": 1016, "m_flInputMin": 664, "m_flLOSScale": 2076, "m_flMaxTraceLength": 2072, "m_flOutputMax": 1720, "m_flOutputMin": 1368, "m_nFieldOutput": 448, "m_nSetMethod": 2216, "m_nTraceSet": 2208}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DistanceBetweenVecs": {"fields": {"m_bDeltaTime": 5180, "m_flInputMax": 4120, "m_flInputMin": 3768, "m_flOutputMax": 4824, "m_flOutputMin": 4472, "m_nFieldOutput": 448, "m_nSetMethod": 5176, "m_vecPoint1": 456, "m_vecPoint2": 2112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DistanceCull": {"fields": {"m_bCullInside": 468, "m_flDistance": 464, "m_nControlPoint": 448, "m_vecPointOffset": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DistanceToTransform": {"fields": {"m_CollisionGroupName": 1969, "m_TransformStart": 1864, "m_bActiveRange": 2116, "m_bAdditive": 2117, "m_bLOS": 1968, "m_flInputMax": 808, "m_flInputMin": 456, "m_flLOSScale": 2108, "m_flMaxTraceLength": 2104, "m_flOutputMax": 1512, "m_flOutputMin": 1160, "m_nFieldOutput": 448, "m_nSetMethod": 2112, "m_nTraceSet": 2100, "m_vecComponentScale": 2120}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DragRelativeToPlane": {"fields": {"m_bDirectional": 1152, "m_flDragAtPlane": 448, "m_flFalloff": 800, "m_nControlPointNumber": 2816, "m_vecPlaneNormal": 1160}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_DriveCPFromGlobalSoundFloat": {"fields": {"m_FieldName": 496, "m_OperatorName": 488, "m_StackName": 480, "m_flInputMax": 468, "m_flInputMin": 464, "m_flOutputMax": 476, "m_flOutputMin": 472, "m_nOutputControlPoint": 456, "m_nOutputField": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_EnableChildrenFromParentParticleCount": {"fields": {"m_bDestroyImmediately": 818, "m_bDisableChildren": 816, "m_bPlayEndcapOnStop": 817, "m_nChildGroupID": 456, "m_nFirstChild": 460, "m_nNumChildrenToEnable": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_EndCapDecay": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_EndCapTimedDecay": {"fields": {"m_flDecayTime": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_EndCapTimedFreeze": {"fields": {"m_flFreezeTime": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ExternalGameImpulseForce": {"fields": {"m_bExplosions": 818, "m_bParticles": 819, "m_bRopes": 816, "m_bRopesZOnly": 817, "m_flForceScale": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_ExternalWindForce": {"fields": {"m_bDampenNearWaterPlane": 3778, "m_bSampleGravity": 3779, "m_bSampleWater": 3777, "m_bSampleWind": 3776, "m_bUseBasicMovementGravity": 5440, "m_flLocalBuoyancyScale": 5800, "m_flLocalGravityScale": 5448, "m_vecBuoyancyForce": 6152, "m_vecGravityForce": 3784, "m_vecSamplePosition": 464, "m_vecScale": 2120}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_FadeAndKill": {"fields": {"m_bForcePreserveParticleOrder": 472, "m_flEndAlpha": 468, "m_flEndFadeInTime": 452, "m_flEndFadeOutTime": 460, "m_flStartAlpha": 464, "m_flStartFadeInTime": 448, "m_flStartFadeOutTime": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_FadeAndKillForTracers": {"fields": {"m_flEndAlpha": 468, "m_flEndFadeInTime": 452, "m_flEndFadeOutTime": 460, "m_flStartAlpha": 464, "m_flStartFadeInTime": 448, "m_flStartFadeOutTime": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_FadeIn": {"fields": {"m_bProportional": 460, "m_flFadeInTimeExp": 456, "m_flFadeInTimeMax": 452, "m_flFadeInTimeMin": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_FadeInSimple": {"fields": {"m_flFadeInTime": 448, "m_nFieldOutput": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_FadeOut": {"fields": {"m_bEaseInAndOut": 513, "m_bProportional": 512, "m_flFadeBias": 460, "m_flFadeOutTimeExp": 456, "m_flFadeOutTimeMax": 452, "m_flFadeOutTimeMin": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_FadeOutSimple": {"fields": {"m_flFadeOutTime": 448, "m_nFieldOutput": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ForceBasedOnDistanceToPlane": {"fields": {"m_flExponent": 512, "m_flMaxDist": 480, "m_flMinDist": 464, "m_nControlPointNumber": 508, "m_vecForceAtMaxDist": 484, "m_vecForceAtMinDist": 468, "m_vecPlaneNormal": 496}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_ForceControlPointStub": {"fields": {"m_ControlPoint": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_GlobalLight": {"fields": {"m_bClampLowerRange": 452, "m_bClampUpperRange": 453, "m_flScale": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_HSVShiftToCP": {"fields": {"m_DefaultHSVColor": 468, "m_nColorCP": 456, "m_nColorGemEnableCP": 460, "m_nOutputCP": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_InheritFromParentParticles": {"fields": {"m_bRandomDistribution": 460, "m_flScale": 448, "m_nFieldOutput": 452, "m_nIncrement": 456}, "metadata": [{"name": "MParticleMaxVersion", "type": "Unknown"}, {"name": "MParticleReplacementOp", "type": "Unknown"}, {"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_InheritFromParentParticlesV2": {"fields": {"m_bRandomDistribution": 460, "m_flScale": 448, "m_nFieldOutput": 452, "m_nIncrement": 456, "m_nMissingParentBehavior": 464}, "metadata": [{"name": "MParticleMinVersion", "type": "Unknown"}, {"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_InheritFromPeerSystem": {"fields": {"m_nFieldInput": 452, "m_nFieldOutput": 448, "m_nGroupID": 460, "m_nIncrement": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_InstantaneousEmitter": {"fields": {"m_flInitFromKilledParentParticles": 1160, "m_flParentParticleScale": 1168, "m_flStartTime": 808, "m_nEventType": 1164, "m_nMaxEmittedPerFrame": 1520, "m_nParticlesToEmit": 456, "m_nSnapshotControlPoint": 1524}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionEmitter"}, "C_OP_InterpolateRadius": {"fields": {"m_bEaseInAndOut": 464, "m_flBias": 468, "m_flEndScale": 460, "m_flEndTime": 452, "m_flStartScale": 456, "m_flStartTime": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_IntraParticleForce": {"fields": {"m_bThreadIt": 489, "m_bUseAABB": 488, "m_flAttractionMaxDistance": 468, "m_flAttractionMaxStrength": 472, "m_flAttractionMinDistance": 464, "m_flRepulsionMaxDistance": 480, "m_flRepulsionMaxStrength": 484, "m_flRepulsionMinDistance": 476}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_LagCompensation": {"fields": {"m_nDesiredVelocityCP": 448, "m_nDesiredVelocityCPField": 460, "m_nLatencyCP": 452, "m_nLatencyCPField": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LerpEndCapScalar": {"fields": {"m_flLerpTime": 456, "m_flOutput": 452, "m_nFieldOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LerpEndCapVector": {"fields": {"m_flLerpTime": 464, "m_nFieldOutput": 448, "m_vecOutput": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LerpScalar": {"fields": {"m_flEndTime": 812, "m_flOutput": 456, "m_flStartTime": 808, "m_nFieldOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LerpToInitialPosition": {"fields": {"m_flInterpolation": 456, "m_flScale": 816, "m_nCacheField": 808, "m_nControlPointNumber": 448, "m_vecScale": 1168}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LerpToOtherAttribute": {"fields": {"m_flInterpolation": 448, "m_nFieldInput": 804, "m_nFieldInputFrom": 800, "m_nFieldOutput": 808}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LerpVector": {"fields": {"m_flEndTime": 468, "m_flStartTime": 464, "m_nFieldOutput": 448, "m_nSetMethod": 472, "m_vecOutput": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LightningSnapshotGenerator": {"fields": {"m_flBranchTwist": 2936, "m_flDedicatedPool": 4000, "m_flOffset": 824, "m_flOffsetDecay": 1176, "m_flRadiusEnd": 3648, "m_flRadiusStart": 3296, "m_flRecalcRate": 1528, "m_flSegments": 472, "m_flSplitRate": 2584, "m_flUVOffset": 2232, "m_flUVScale": 1880, "m_nBranchBehavior": 3288, "m_nCPEndPnt": 464, "m_nCPSnapshot": 456, "m_nCPStartPnt": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_LocalAccelerationForce": {"fields": {"m_nCP": 464, "m_nScaleCP": 468, "m_vecAccel": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_LockPoints": {"fields": {"m_flBlendValue": 468, "m_nControlPoint": 464, "m_nMaxCol": 452, "m_nMaxRow": 460, "m_nMinCol": 448, "m_nMinRow": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LockToBone": {"fields": {"m_HitboxSetName": 664, "m_bRigid": 792, "m_bRigidRotationLock": 808, "m_bUseBones": 793, "m_flJumpThreshold": 656, "m_flLifeTimeFadeEnd": 652, "m_flLifeTimeFadeStart": 648, "m_flPrevPosScale": 660, "m_flRotLerp": 2472, "m_modelInput": 448, "m_nFieldOutput": 796, "m_nFieldOutputPrev": 800, "m_nRotationSetType": 804, "m_transformInput": 544, "m_vecRotation": 816}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LockToPointList": {"fields": {"m_bClosedLoop": 481, "m_bPlaceAlongPath": 480, "m_nFieldOutput": 448, "m_nNumPointsAlongPath": 484, "m_pointList": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LockToSavedSequentialPath": {"fields": {"m_PathParams": 464, "m_bCPPairs": 460, "m_flFadeEnd": 456, "m_flFadeStart": 452}, "metadata": [{"name": "MParticleMaxVersion", "type": "Unknown"}, {"name": "MParticleReplacementOp", "type": "Unknown"}, {"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_LockToSavedSequentialPathV2": {"fields": {"m_PathParams": 464, "m_bCPPairs": 456, "m_flFadeEnd": 452, "m_flFadeStart": 448}, "metadata": [{"name": "MParticleMinVersion", "type": "Unknown"}, {"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MaintainEmitter": {"fields": {"m_bEmitInstantaneously": 1176, "m_bFinalEmitOnStop": 1177, "m_flEmissionDuration": 816, "m_flEmissionRate": 1168, "m_flScale": 1184, "m_flStartTime": 808, "m_nParticlesToMaintain": 456, "m_nSnapshotControlPoint": 1172}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionEmitter"}, "C_OP_MaintainSequentialPath": {"fields": {"m_PathParams": 480, "m_bLoop": 464, "m_bUseParticleCount": 465, "m_fMaxDistance": 448, "m_flCohesionStrength": 456, "m_flNumToAssign": 452, "m_flTolerance": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MaxVelocity": {"fields": {"m_flMaxVelocity": 448, "m_flMinVelocity": 452, "m_nOverrideCP": 456, "m_nOverrideCPField": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ModelCull": {"fields": {"m_HitboxSetName": 455, "m_bBoundBox": 452, "m_bCullOutside": 453, "m_bUseBones": 454, "m_nControlPointNumber": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ModelDampenMovement": {"fields": {"m_HitboxSetName": 455, "m_bBoundBox": 452, "m_bOutside": 453, "m_bUseBones": 454, "m_fDrag": 2240, "m_nControlPointNumber": 448, "m_vecPosOffset": 584}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MoveToHitbox": {"fields": {"m_HitboxSetName": 664, "m_bUseBones": 792, "m_flInterpolation": 800, "m_flLifeTimeLerpEnd": 656, "m_flLifeTimeLerpStart": 652, "m_flPrevPosScale": 660, "m_modelInput": 448, "m_nLerpType": 796, "m_transformInput": 544}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MovementLoopInsideSphere": {"fields": {"m_flDistance": 456, "m_nCP": 448, "m_nDistSqrAttr": 2464, "m_vecScale": 808}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MovementMaintainOffset": {"fields": {"m_bRadiusScale": 464, "m_nCP": 460, "m_vecOffset": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MovementMoveAlongSkinnedCPSnapshot": {"fields": {"m_bSetNormal": 456, "m_bSetRadius": 457, "m_flInterpolation": 464, "m_flTValue": 816, "m_nControlPointNumber": 448, "m_nSnapshotControlPointNumber": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MovementPlaceOnGround": {"fields": {"m_CollisionGroupName": 816, "m_bIncludeShotHull": 972, "m_bIncludeWater": 973, "m_bScaleOffset": 977, "m_bSetNormal": 976, "m_flLerpRate": 812, "m_flMaxTraceLength": 800, "m_flOffset": 448, "m_flTolerance": 804, "m_flTraceOffset": 808, "m_nIgnoreCP": 984, "m_nLerpCP": 956, "m_nPreserveOffsetCP": 980, "m_nRefCP1": 948, "m_nRefCP2": 952, "m_nTraceMissBehavior": 968, "m_nTraceSet": 944}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MovementRigidAttachToCP": {"fields": {"m_bOffsetLocal": 468, "m_nControlPointNumber": 448, "m_nFieldInput": 460, "m_nFieldOutput": 464, "m_nScaleCPField": 456, "m_nScaleControlPoint": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MovementRotateParticleAroundAxis": {"fields": {"m_TransformInput": 2456, "m_bLocalSpace": 2560, "m_flRotRate": 2104, "m_vecRotAxis": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_MovementSkinnedPositionFromCPSnapshot": {"fields": {"m_bRandom": 456, "m_bSetNormal": 464, "m_bSetRadius": 465, "m_flIncrement": 824, "m_flInterpolation": 1880, "m_flReadIndex": 472, "m_nControlPointNumber": 452, "m_nFullLoopIncrement": 1176, "m_nIndexType": 468, "m_nRandomSeed": 460, "m_nSnapShotStartPoint": 1528, "m_nSnapshotControlPointNumber": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_Noise": {"fields": {"m_bAdditive": 464, "m_fl4NoiseScale": 460, "m_flNoiseAnimationTimeScale": 468, "m_flOutputMax": 456, "m_flOutputMin": 452, "m_nFieldOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_NoiseEmitter": {"fields": {"m_bAbsVal": 480, "m_bAbsValInv": 481, "m_flEmissionDuration": 456, "m_flEmissionScale": 464, "m_flNoiseScale": 496, "m_flOffset": 484, "m_flOutputMax": 492, "m_flOutputMin": 488, "m_flStartTime": 460, "m_flWorldNoiseScale": 500, "m_flWorldTimeScale": 516, "m_nScaleControlPoint": 468, "m_nScaleControlPointField": 472, "m_nWorldNoisePoint": 476, "m_vecOffsetLoc": 504}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionEmitter"}, "C_OP_NormalLock": {"fields": {"m_nControlPointNumber": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_NormalizeVector": {"fields": {"m_flScale": 452, "m_nFieldOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_Orient2DRelToCP": {"fields": {"m_flRotOffset": 448, "m_flSpinStrength": 452, "m_nCP": 456, "m_nFieldOutput": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_OrientTo2dDirection": {"fields": {"m_flRotOffset": 448, "m_flSpinStrength": 452, "m_nFieldOutput": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_OscillateScalar": {"fields": {"m_FrequencyMax": 460, "m_FrequencyMin": 456, "m_RateMax": 452, "m_RateMin": 448, "m_bProportional": 468, "m_bProportionalOp": 469, "m_flEndTime_max": 484, "m_flEndTime_min": 480, "m_flOscAdd": 492, "m_flOscMult": 488, "m_flStartTime_max": 476, "m_flStartTime_min": 472, "m_nField": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_OscillateScalarSimple": {"fields": {"m_Frequency": 452, "m_Rate": 448, "m_flOscAdd": 464, "m_flOscMult": 460, "m_nField": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_OscillateVector": {"fields": {"m_FrequencyMax": 484, "m_FrequencyMin": 472, "m_RateMax": 460, "m_RateMin": 448, "m_bOffset": 502, "m_bProportional": 500, "m_bProportionalOp": 501, "m_flEndTime_max": 516, "m_flEndTime_min": 512, "m_flOscAdd": 872, "m_flOscMult": 520, "m_flRateScale": 1224, "m_flStartTime_max": 508, "m_flStartTime_min": 504, "m_nField": 496}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_OscillateVectorSimple": {"fields": {"m_Frequency": 460, "m_Rate": 448, "m_bOffset": 484, "m_flOscAdd": 480, "m_flOscMult": 476, "m_nField": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ParentVortices": {"fields": {"m_bFlipBasedOnYaw": 480, "m_flForceScale": 464, "m_vecTwistAxis": 468}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_PerParticleForce": {"fields": {"m_flForceScale": 464, "m_nCP": 2472, "m_vForce": 816}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_PercentageBetweenTransformLerpCPs": {"fields": {"m_TransformEnd": 568, "m_TransformStart": 464, "m_bActiveRange": 692, "m_bRadialCheck": 693, "m_flInputMax": 456, "m_flInputMin": 452, "m_nFieldOutput": 448, "m_nOutputEndCP": 680, "m_nOutputEndField": 684, "m_nOutputStartCP": 672, "m_nOutputStartField": 676, "m_nSetMethod": 688}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_PercentageBetweenTransforms": {"fields": {"m_TransformEnd": 576, "m_TransformStart": 472, "m_bActiveRange": 684, "m_bRadialCheck": 685, "m_flInputMax": 456, "m_flInputMin": 452, "m_flOutputMax": 464, "m_flOutputMin": 460, "m_nFieldOutput": 448, "m_nSetMethod": 680}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_PercentageBetweenTransformsVector": {"fields": {"m_TransformEnd": 592, "m_TransformStart": 488, "m_bActiveRange": 700, "m_bRadialCheck": 701, "m_flInputMax": 456, "m_flInputMin": 452, "m_nFieldOutput": 448, "m_nSetMethod": 696, "m_vecOutputMax": 472, "m_vecOutputMin": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_PinParticleToCP": {"fields": {"m_bOffsetLocal": 2112, "m_flAge": 3184, "m_flBreakDistance": 2480, "m_flBreakSpeed": 2832, "m_flBreakValue": 3544, "m_flInterpolation": 3896, "m_nBreakControlPointNumber": 3536, "m_nBreakControlPointNumber2": 3540, "m_nControlPointNumber": 448, "m_nParticleNumber": 2120, "m_nParticleSelection": 2116, "m_nPinBreakType": 2472, "m_vecOffset": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_PlanarConstraint": {"fields": {"m_PlaneNormal": 460, "m_PointOnPlane": 448, "m_bGlobalNormal": 477, "m_bGlobalOrigin": 476, "m_bUseOldCode": 1184, "m_flMaximumDistanceToCP": 832, "m_flRadiusScale": 480, "m_nControlPointNumber": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_PlaneCull": {"fields": {"m_bLocalSpace": 464, "m_flPlaneOffset": 468, "m_nPlaneControlPoint": 448, "m_vecPlaneDirection": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_PlayEndCapWhenFinished": {"fields": {"m_bFireOnEmissionEnd": 456, "m_bIncludeChildren": 457}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_PointVectorAtNextParticle": {"fields": {"m_flInterpolation": 456, "m_nFieldOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_PositionLock": {"fields": {"m_TransformInput": 448, "m_bLockRot": 944, "m_flEndTime_exp": 572, "m_flEndTime_max": 568, "m_flEndTime_min": 564, "m_flJumpThreshold": 936, "m_flPrevPosScale": 940, "m_flRange": 576, "m_flRangeBias": 584, "m_flStartTime_exp": 560, "m_flStartTime_max": 556, "m_flStartTime_min": 552, "m_nFieldOutput": 2608, "m_nFieldOutputPrev": 2612, "m_vecScale": 952}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_QuantizeCPComponent": {"fields": {"m_flInputValue": 456, "m_flQuantizeValue": 816, "m_nCPOutput": 808, "m_nOutVectorField": 812}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_QuantizeFloat": {"fields": {"m_InputValue": 448, "m_nOutputField": 800}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RadiusDecay": {"fields": {"m_flMinRadius": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RampCPLinearRandom": {"fields": {"m_nOutControlPointNumber": 456, "m_vecRateMax": 472, "m_vecRateMin": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RampScalarLinear": {"fields": {"m_RateMax": 452, "m_RateMin": 448, "m_bProportionalOp": 516, "m_flEndTime_max": 468, "m_flEndTime_min": 464, "m_flStartTime_max": 460, "m_flStartTime_min": 456, "m_nField": 512}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RampScalarLinearSimple": {"fields": {"m_Rate": 448, "m_flEndTime": 456, "m_flStartTime": 452, "m_nField": 496}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RampScalarSpline": {"fields": {"m_RateMax": 452, "m_RateMin": 448, "m_bEaseOut": 517, "m_bProportionalOp": 516, "m_flBias": 472, "m_flEndTime_max": 468, "m_flEndTime_min": 464, "m_flStartTime_max": 460, "m_flStartTime_min": 456, "m_nField": 512}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RampScalarSplineSimple": {"fields": {"m_Rate": 448, "m_bEaseOut": 500, "m_flEndTime": 456, "m_flStartTime": 452, "m_nField": 496}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RandomForce": {"fields": {"m_MaxForce": 476, "m_MinForce": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_ReadFromNeighboringParticle": {"fields": {"m_DistanceCheck": 464, "m_flInterpolation": 816, "m_nFieldInput": 448, "m_nFieldOutput": 452, "m_nIncrement": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ReinitializeScalarEndCap": {"fields": {"m_flOutputMax": 456, "m_flOutputMin": 452, "m_nFieldOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapAverageHitboxSpeedtoCP": {"fields": {"m_HitboxSetName": 3544, "m_flInputMax": 824, "m_flInputMin": 472, "m_flOutputMax": 1528, "m_flOutputMin": 1176, "m_nField": 464, "m_nHeightControlPointNumber": 1880, "m_nHitboxDataType": 468, "m_nInControlPointNumber": 456, "m_nOutControlPointNumber": 460, "m_vecComparisonVelocity": 1888}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RemapAverageScalarValuetoCP": {"fields": {"m_flInputMax": 472, "m_flInputMin": 468, "m_flOutputMax": 480, "m_flOutputMin": 476, "m_nField": 464, "m_nOutControlPointNumber": 456, "m_nOutVectorField": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RemapBoundingVolumetoCP": {"fields": {"m_flInputMax": 464, "m_flInputMin": 460, "m_flOutputMax": 472, "m_flOutputMin": 468, "m_nOutControlPointNumber": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RemapCPVelocityToVector": {"fields": {"m_bNormalize": 460, "m_flScale": 456, "m_nControlPoint": 448, "m_nFieldOutput": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapCPtoCP": {"fields": {"m_bDerivative": 488, "m_flInputMax": 476, "m_flInputMin": 472, "m_flInterpRate": 492, "m_flOutputMax": 484, "m_flOutputMin": 480, "m_nInputControlPoint": 456, "m_nInputField": 464, "m_nOutputControlPoint": 460, "m_nOutputField": 468}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RemapCPtoScalar": {"fields": {"m_flEndTime": 480, "m_flInputMax": 464, "m_flInputMin": 460, "m_flInterpRate": 484, "m_flOutputMax": 472, "m_flOutputMin": 468, "m_flStartTime": 476, "m_nCPInput": 448, "m_nField": 456, "m_nFieldOutput": 452, "m_nSetMethod": 488}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapCPtoVector": {"fields": {"m_bAccelerate": 525, "m_bOffset": 524, "m_flEndTime": 512, "m_flInterpRate": 516, "m_flStartTime": 508, "m_nCPInput": 448, "m_nFieldOutput": 452, "m_nLocalSpaceCP": 456, "m_nSetMethod": 520, "m_vInputMax": 472, "m_vInputMin": 460, "m_vOutputMax": 496, "m_vOutputMin": 484}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapControlPointDirectionToVector": {"fields": {"m_flScale": 452, "m_nControlPointNumber": 456, "m_nFieldOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapControlPointOrientationToRotation": {"fields": {"m_flOffsetRot": 456, "m_nCP": 448, "m_nComponent": 460, "m_nFieldOutput": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapCrossProductOfTwoVectorsToVector": {"fields": {"m_InputVec1": 448, "m_InputVec2": 2104, "m_bNormalize": 3764, "m_nFieldOutput": 3760}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapDensityGradientToVectorAttribute": {"fields": {"m_flRadiusScale": 448, "m_nFieldOutput": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapDensityToVector": {"fields": {"m_bUseParentDensity": 488, "m_flDensityMax": 460, "m_flDensityMin": 456, "m_flRadiusScale": 448, "m_nFieldOutput": 452, "m_nVoxelGridResolution": 492, "m_vecOutputMax": 476, "m_vecOutputMin": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapDirectionToCPToVector": {"fields": {"m_bNormalize": 476, "m_flOffsetRot": 460, "m_flScale": 456, "m_nCP": 448, "m_nFieldOutput": 452, "m_nFieldStrength": 480, "m_vecOffsetAxis": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapDistanceToLineSegmentBase": {"fields": {"m_bInfiniteLine": 464, "m_flMaxInputValue": 460, "m_flMinInputValue": 456, "m_nCP0": 448, "m_nCP1": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapDistanceToLineSegmentToScalar": {"fields": {"m_flMaxOutputValue": 480, "m_flMinOutputValue": 476, "m_nFieldOutput": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RemapDistanceToLineSegmentBase"}, "C_OP_RemapDistanceToLineSegmentToVector": {"fields": {"m_nFieldOutput": 472, "m_vMaxOutputValue": 488, "m_vMinOutputValue": 476}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RemapDistanceToLineSegmentBase"}, "C_OP_RemapDotProductToCP": {"fields": {"m_flInputMax": 824, "m_flInputMin": 472, "m_flOutputMax": 1528, "m_flOutputMin": 1176, "m_nInputCP1": 456, "m_nInputCP2": 460, "m_nOutVectorField": 468, "m_nOutputCP": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RemapDotProductToScalar": {"fields": {"m_bActiveRange": 484, "m_bUseParticleNormal": 485, "m_bUseParticleVelocity": 476, "m_flInputMax": 464, "m_flInputMin": 460, "m_flOutputMax": 472, "m_flOutputMin": 468, "m_nFieldOutput": 456, "m_nInputCP1": 448, "m_nInputCP2": 452, "m_nSetMethod": 480}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapExternalWindToCP": {"fields": {"m_bSetMagnitude": 2120, "m_nCP": 456, "m_nCPOutput": 460, "m_nOutVectorField": 2124, "m_vecScale": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RemapGravityToVector": {"fields": {"m_bNormalizedOutput": 2112, "m_nOutputField": 2104, "m_nSetMethod": 2108, "m_vInput1": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapModelVolumetoCP": {"fields": {"m_bBBoxOnly": 492, "m_bCubeRoot": 493, "m_flInputMax": 480, "m_flInputMin": 476, "m_flOutputMax": 488, "m_flOutputMin": 484, "m_nBBoxType": 456, "m_nField": 472, "m_nInControlPointNumber": 460, "m_nOutControlPointMaxNumber": 468, "m_nOutControlPointNumber": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RemapNamedModelBodyPartEndCap": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RemapNamedModelElementEndCap"}, "C_OP_RemapNamedModelBodyPartOnceTimed": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RemapNamedModelElementOnceTimed"}, "C_OP_RemapNamedModelElementEndCap": {"fields": {"m_bModelFromRenderer": 528, "m_fallbackNames": 504, "m_hModel": 448, "m_inNames": 456, "m_nFieldInput": 532, "m_nFieldOutput": 536, "m_outNames": 480}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapNamedModelElementOnceTimed": {"fields": {"m_bModelFromRenderer": 528, "m_bProportional": 529, "m_fallbackNames": 504, "m_flRemapTime": 540, "m_hModel": 448, "m_inNames": 456, "m_nFieldInput": 532, "m_nFieldOutput": 536, "m_outNames": 480}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapNamedModelMeshGroupEndCap": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RemapNamedModelElementEndCap"}, "C_OP_RemapNamedModelMeshGroupOnceTimed": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RemapNamedModelElementOnceTimed"}, "C_OP_RemapNamedModelSequenceEndCap": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RemapNamedModelElementEndCap"}, "C_OP_RemapNamedModelSequenceOnceTimed": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RemapNamedModelElementOnceTimed"}, "C_OP_RemapParticleCountOnScalarEndCap": {"fields": {"m_bBackwards": 468, "m_flOutputMax": 464, "m_flOutputMin": 460, "m_nFieldOutput": 448, "m_nInputMax": 456, "m_nInputMin": 452, "m_nSetMethod": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapParticleCountToScalar": {"fields": {"m_bActiveRange": 1864, "m_flOutputMax": 1512, "m_flOutputMin": 1160, "m_nFieldOutput": 448, "m_nInputMax": 808, "m_nInputMin": 456, "m_nSetMethod": 1868}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapSDFDistanceToScalarAttribute": {"fields": {"m_flMaxDistance": 808, "m_flMinDistance": 456, "m_flValueAboveMax": 2216, "m_flValueAtMax": 1864, "m_flValueAtMin": 1512, "m_flValueBelowMin": 1160, "m_nFieldOutput": 448, "m_nVectorFieldInput": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapSDFDistanceToVectorAttribute": {"fields": {"m_flMaxDistance": 808, "m_flMinDistance": 456, "m_nVectorFieldInput": 452, "m_nVectorFieldOutput": 448, "m_vValueAboveMax": 1196, "m_vValueAtMax": 1184, "m_vValueAtMin": 1172, "m_vValueBelowMin": 1160}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapSDFGradientToVectorAttribute": {"fields": {"m_nFieldOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapScalar": {"fields": {"m_bOldCode": 472, "m_flInputMax": 460, "m_flInputMin": 456, "m_flOutputMax": 468, "m_flOutputMin": 464, "m_nFieldInput": 448, "m_nFieldOutput": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapScalarEndCap": {"fields": {"m_flInputMax": 460, "m_flInputMin": 456, "m_flOutputMax": 468, "m_flOutputMin": 464, "m_nFieldInput": 448, "m_nFieldOutput": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapScalarOnceTimed": {"fields": {"m_bProportional": 448, "m_flInputMax": 464, "m_flInputMin": 460, "m_flOutputMax": 472, "m_flOutputMin": 468, "m_flRemapTime": 476, "m_nFieldInput": 452, "m_nFieldOutput": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapSpeed": {"fields": {"m_bIgnoreDelta": 472, "m_flInputMax": 456, "m_flInputMin": 452, "m_flOutputMax": 464, "m_flOutputMin": 460, "m_nFieldOutput": 448, "m_nSetMethod": 468}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapSpeedtoCP": {"fields": {"m_bUseDeltaV": 484, "m_flInputMax": 472, "m_flInputMin": 468, "m_flOutputMax": 480, "m_flOutputMin": 476, "m_nField": 464, "m_nInControlPointNumber": 456, "m_nOutControlPointNumber": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RemapTransformOrientationToRotations": {"fields": {"m_TransformInput": 448, "m_bUseQuat": 564, "m_bWriteNormal": 565, "m_vecRotation": 552}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapTransformOrientationToYaw": {"fields": {"m_TransformInput": 448, "m_flRotOffset": 556, "m_flSpinStrength": 560, "m_nFieldOutput": 552}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapTransformToVelocity": {"fields": {"m_TransformInput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapTransformVisibilityToScalar": {"fields": {"m_TransformInput": 456, "m_flInputMax": 568, "m_flInputMin": 564, "m_flOutputMax": 576, "m_flOutputMin": 572, "m_flRadius": 580, "m_nFieldOutput": 560, "m_nSetMethod": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapTransformVisibilityToVector": {"fields": {"m_TransformInput": 456, "m_flInputMax": 568, "m_flInputMin": 564, "m_flRadius": 596, "m_nFieldOutput": 560, "m_nSetMethod": 448, "m_vecOutputMax": 584, "m_vecOutputMin": 572}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapVectorComponentToScalar": {"fields": {"m_nComponent": 456, "m_nFieldInput": 448, "m_nFieldOutput": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapVectortoCP": {"fields": {"m_nFieldInput": 452, "m_nOutControlPointNumber": 448, "m_nParticleNumber": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapVelocityToVector": {"fields": {"m_bNormalize": 456, "m_flScale": 452, "m_nFieldOutput": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RemapVisibilityScalar": {"fields": {"m_flInputMax": 460, "m_flInputMin": 456, "m_flOutputMax": 468, "m_flOutputMin": 464, "m_flRadiusScale": 472, "m_nFieldInput": 448, "m_nFieldOutput": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RenderAsModels": {"fields": {"m_ModelList": 528, "m_bFitToModelSize": 560, "m_bNonUniformScaling": 561, "m_flModelScale": 556, "m_nSizeCullBloat": 576, "m_nXAxisScalingAttribute": 564, "m_nYAxisScalingAttribute": 568, "m_nZAxisScalingAttribute": 572}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderBlobs": {"fields": {"m_MaterialVars": 1600, "m_cubeWidth": 528, "m_cutoffRadius": 880, "m_hMaterial": 1648, "m_nIndexCountKb": 1588, "m_nScaleCP": 1592, "m_nVertexCountKb": 1584, "m_renderRadius": 1232}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderCables": {"fields": {"m_LightingTransform": 5056, "m_MaterialFloatVars": 5160, "m_MaterialVecVars": 5208, "m_bDrawCableCaps": 5024, "m_flAlphaScale": 880, "m_flCapOffsetAmount": 5032, "m_flCapRoundness": 5028, "m_flColorMapOffsetU": 3968, "m_flColorMapOffsetV": 3616, "m_flNormalMapOffsetU": 4672, "m_flNormalMapOffsetV": 4320, "m_flRadiusScale": 528, "m_flTessScale": 5036, "m_flTextureRepeatsCircumference": 3264, "m_flTextureRepeatsPerSegment": 2912, "m_hMaterial": 2896, "m_nColorBlendType": 2888, "m_nMaxTesselation": 5044, "m_nMinTesselation": 5040, "m_nRoundness": 5048, "m_nTextureRepetitionMode": 2904, "m_vecColorScale": 1232}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderClientPhysicsImpulse": {"fields": {"m_flMagnitude": 880, "m_flRadius": 528, "m_nSimIdFilter": 1232}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderClothForce": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderDeferredLight": {"fields": {"m_bUseAlphaTestWindow": 528, "m_bUseTexture": 529, "m_flAlphaScale": 536, "m_flDistanceFalloff": 2212, "m_flLightDistance": 2204, "m_flRadiusScale": 532, "m_flSpotFoV": 2216, "m_flStartFalloff": 2208, "m_hTexture": 2232, "m_nAlpha2Field": 540, "m_nAlphaTestPointField": 2220, "m_nAlphaTestRangeField": 2224, "m_nAlphaTestSharpnessField": 2228, "m_nColorBlendType": 2200, "m_nHSVShiftControlPoint": 2240, "m_vecColorScale": 544}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderFlattenGrass": {"fields": {"m_flFlattenStrength": 528, "m_flRadiusScale": 536, "m_nStrengthFieldOverride": 532}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderGpuImplicit": {"fields": {"m_bUsePerParticleRadius": 528, "m_fGridSize": 544, "m_fIsosurfaceThreshold": 1248, "m_fRadiusScale": 896, "m_hMaterial": 1608, "m_nIndexCountKb": 536, "m_nScaleCP": 1600, "m_nVertexCountKb": 532}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderLightBeam": {"fields": {"m_bCastShadows": 2544, "m_flBrightnessLumensPerMeter": 2192, "m_flRange": 2904, "m_flSkirt": 2552, "m_flThickness": 3256, "m_nColorBlendType": 2184, "m_vColorBlend": 528}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderLights": {"fields": {"m_bAnimateInFPS": 544, "m_flAnimationRate": 536, "m_flEndFadeSize": 560, "m_flMaxSize": 552, "m_flMinSize": 548, "m_flStartFadeSize": 556, "m_nAnimationType": 540}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "C_OP_RenderPoints"}, "C_OP_RenderMaterialProxy": {"fields": {"m_MaterialVars": 536, "m_flAlpha": 2576, "m_flMaterialOverrideEnabled": 568, "m_hOverrideMaterial": 560, "m_nColorBlendType": 2928, "m_nMaterialControlPoint": 528, "m_nProxyType": 532, "m_vecColorScale": 920}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderModels": {"fields": {"m_ActivityName": 5936, "m_ClothEffectName": 6449, "m_EconSlotName": 7364, "m_MaterialVars": 6888, "m_ModelList": 536, "m_SequenceName": 6192, "m_bAcceptsDecals": 7630, "m_bAllowApproximateTransforms": 7633, "m_bAnimated": 5560, "m_bCenterOffset": 570, "m_bDisableDepthPrepass": 7629, "m_bDisableShadows": 7628, "m_bDoNotDrawInParticlePass": 7632, "m_bEnableClothSimulation": 6448, "m_bForceDrawInterlevedWithSiblings": 7631, "m_bForceLoopingAnimation": 5921, "m_bIgnoreNormal": 568, "m_bIgnoreRadius": 3888, "m_bLocalScale": 5552, "m_bManualAnimFrame": 5923, "m_bOnlyRenderInEffecsGameOverlay": 531, "m_bOnlyRenderInEffectsBloomPass": 528, "m_bOnlyRenderInEffectsWaterPass": 529, "m_bOrientZ": 569, "m_bOriginalModel": 7620, "m_bOverrideTranslucentMaterials": 6528, "m_bResetAnimOnStop": 5922, "m_bScaleAnimationRate": 5920, "m_bSuppressTint": 7621, "m_bUseMixedResolutionRendering": 530, "m_flAlphaScale": 8248, "m_flAnimationRate": 5568, "m_flManualModelSelection": 6912, "m_flRadiusScale": 7896, "m_flRollScale": 8600, "m_hOverrideMaterial": 6520, "m_modelInput": 7264, "m_nAlpha2Field": 8952, "m_nAnimationField": 5928, "m_nAnimationScaleField": 5924, "m_nBodyGroupField": 560, "m_nColorBlendType": 10616, "m_nLOD": 7360, "m_nManualFrameField": 5932, "m_nModelScaleCP": 3892, "m_nSizeCullBloat": 5556, "m_nSkin": 6536, "m_nSubModelField": 564, "m_nSubModelFieldType": 7624, "m_szRenderAttribute": 7634, "m_vecColorScale": 8960, "m_vecComponentScale": 3896, "m_vecLocalOffset": 576, "m_vecLocalRotation": 2232}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderOmni2Light": {"fields": {"m_bCastShadows": 2904, "m_bFog": 2905, "m_bSphericalCookie": 5032, "m_flBrightnessCandelas": 2552, "m_flBrightnessLumens": 2200, "m_flFogScale": 2912, "m_flInnerConeAngle": 4320, "m_flLuminaireRadius": 3264, "m_flOuterConeAngle": 4672, "m_flRange": 3968, "m_flSkirt": 3616, "m_hLightCookie": 5024, "m_nBrightnessUnit": 2196, "m_nColorBlendType": 2192, "m_nLightType": 528, "m_vColorBlend": 536}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderPoints": {"fields": {"m_hMaterial": 528}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderPostProcessing": {"fields": {"m_flPostProcessStrength": 528, "m_hPostTexture": 880, "m_nPriority": 888}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderProjected": {"fields": {"m_MaterialVars": 928, "m_bEnableProjectedDepthControls": 532, "m_bFlipHorizontal": 531, "m_bOrientToNormal": 924, "m_bProjectCharacter": 528, "m_bProjectWater": 530, "m_bProjectWorld": 529, "m_flAlphaScale": 1304, "m_flAnimationTimeScale": 920, "m_flMaterialSelection": 568, "m_flMaxProjectionDepth": 540, "m_flMinProjectionDepth": 536, "m_flRadiusScale": 952, "m_flRollScale": 1656, "m_nAlpha2Field": 2008, "m_nColorBlendType": 3672, "m_vecColorScale": 2016, "m_vecProjectedMaterials": 544}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderRopes": {"fields": {"m_bClampV": 12028, "m_bClosedLoop": 12065, "m_bDrawAsOpaque": 12076, "m_bEnableFadingAndClamping": 10920, "m_bGenerateNormals": 12077, "m_bReverseOrder": 12064, "m_bUseScalarForTextureCoordinate": 12053, "m_flEndFadeDot": 10944, "m_flEndFadeSize": 10936, "m_flMaxSize": 10928, "m_flMinSize": 10924, "m_flRadiusTaper": 10948, "m_flScalarAttributeTextureCoordScale": 12060, "m_flScaleVOffsetByControlPointDistance": 12048, "m_flScaleVScrollByControlPointDistance": 12044, "m_flScaleVSizeByControlPointDistance": 12040, "m_flStartFadeDot": 10940, "m_flStartFadeSize": 10932, "m_flTessScale": 10960, "m_flTextureVOffset": 11672, "m_flTextureVScrollRate": 11320, "m_flTextureVWorldSize": 10968, "m_nMaxTesselation": 10956, "m_nMinTesselation": 10952, "m_nOrientationType": 12068, "m_nScalarFieldForTextureCoordinate": 12056, "m_nScaleCP1": 12032, "m_nScaleCP2": 12036, "m_nTextureVParamsCP": 12024, "m_nVectorFieldForOrientation": 12072}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBaseRendererSource2"}, "C_OP_RenderScreenShake": {"fields": {"m_flAmplitudeScale": 540, "m_flDurationScale": 528, "m_flFrequencyScale": 536, "m_flRadiusScale": 532, "m_nAmplitudeField": 556, "m_nDurationField": 548, "m_nFilterCP": 560, "m_nFrequencyField": 552, "m_nRadiusField": 544}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderScreenVelocityRotate": {"fields": {"m_flForwardDegrees": 532, "m_flRotateRateDegrees": 528}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderSimpleModelCollection": {"fields": {"m_bAcceptsDecals": 994, "m_bCenterOffset": 528, "m_bDisableMotionBlur": 993, "m_bDisableShadows": 992, "m_fSizeCullScale": 640, "m_hModel": 536, "m_modelInput": 544, "m_nAngularVelocityField": 996}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderSound": {"fields": {"m_bSuppressStopSoundEvent": 824, "m_flDurationScale": 528, "m_flPitchScale": 536, "m_flSndLvlScale": 532, "m_flVolumeScale": 540, "m_nCPReference": 564, "m_nChannel": 560, "m_nDurationField": 548, "m_nPitchField": 552, "m_nSndLvlField": 544, "m_nVolumeField": 556, "m_pszSoundName": 568}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderSprites": {"fields": {"m_OutlineColor": 13069, "m_bDistanceAlpha": 13056, "m_bOutline": 13068, "m_bParticleShadows": 13808, "m_bSoftEdges": 13057, "m_bUseYawWithNormalAligned": 11280, "m_flAlphaAdjustWithSizeAdjust": 11992, "m_flEdgeSoftnessEnd": 13064, "m_flEdgeSoftnessStart": 13060, "m_flEndFadeDot": 13052, "m_flEndFadeSize": 12696, "m_flLightingDirectionality": 13456, "m_flLightingTessellation": 13104, "m_flMaxSize": 11640, "m_flMinSize": 11288, "m_flOutlineEnd0": 13088, "m_flOutlineEnd1": 13092, "m_flOutlineStart0": 13080, "m_flOutlineStart1": 13084, "m_flShadowDensity": 13812, "m_flStartFadeDot": 13048, "m_flStartFadeSize": 12344, "m_nLightingMode": 13096, "m_nOrientationControlPoint": 11276, "m_nOrientationType": 11272, "m_nOutlineAlpha": 13076, "m_nSequenceOverride": 10920, "m_replicationParameters": 13816}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBaseRendererSource2"}, "C_OP_RenderStandardLight": {"fields": {"m_bCastShadows": 2552, "m_bClosedLoop": 5065, "m_bIgnoreDT": 5080, "m_bRenderDiffuse": 4680, "m_bRenderSpecular": 4681, "m_bReverseOrder": 5064, "m_flCapsuleLength": 5060, "m_flConstrainRadiusToLengthRatio": 5084, "m_flFalloffLinearity": 3624, "m_flFiftyPercentFalloff": 3976, "m_flFogContribution": 4704, "m_flIntensity": 2200, "m_flLengthFadeInTime": 5092, "m_flLengthScale": 5088, "m_flMaxLength": 5072, "m_flMinLength": 5076, "m_flPhi": 2912, "m_flRadiusMultiplier": 3264, "m_flTheta": 2560, "m_flZeroPercentFalloff": 4328, "m_lightCookie": 4688, "m_nAttenuationStyle": 3616, "m_nCapsuleLightBehavior": 5056, "m_nColorBlendType": 2192, "m_nFogLightingMode": 4700, "m_nLightType": 528, "m_nPrevPntSource": 5068, "m_nPriority": 4696, "m_vecColorScale": 536}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderStatusEffect": {"fields": {"m_pTextureColorWarp": 528, "m_pTextureDetail2": 536, "m_pTextureDiffuseWarp": 544, "m_pTextureEnvMap": 576, "m_pTextureFresnelColorWarp": 552, "m_pTextureFresnelWarp": 560, "m_pTextureSpecularWarp": 568}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderStatusEffectCitadel": {"fields": {"m_pTextureColorWarp": 528, "m_pTextureDetail": 568, "m_pTextureMetalness": 544, "m_pTextureNormal": 536, "m_pTextureRoughness": 552, "m_pTextureSelfIllum": 560}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderText": {"fields": {"m_DefaultText": 536, "m_OutlineColor": 528}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderTonemapController": {"fields": {"m_flTonemapLevel": 528, "m_flTonemapWeight": 532, "m_nTonemapLevelField": 536, "m_nTonemapWeightField": 540}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderTrails": {"fields": {"m_bEnableFadingAndClamping": 11648, "m_bFlipUVBasedOnPitchYaw": 16420, "m_bIgnoreDT": 11672, "m_flConstrainRadiusToLengthRatio": 11676, "m_flEndFadeDot": 11656, "m_flForwardShift": 16416, "m_flHeadAlphaScale": 13696, "m_flLengthFadeInTime": 11684, "m_flLengthScale": 11680, "m_flMaxLength": 11664, "m_flMinLength": 11668, "m_flRadiusHeadTaper": 11688, "m_flRadiusTaper": 14048, "m_flStartFadeDot": 11652, "m_flTailAlphaScale": 16056, "m_nHorizCropField": 16408, "m_nPrevPntSource": 11660, "m_nVertCropField": 16412, "m_vecHeadColorScale": 12040, "m_vecTailColorScale": 14400}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CBaseTrailRenderer"}, "C_OP_RenderTreeShake": {"fields": {"m_flControlPointOrientationAmount": 560, "m_flPeakStrength": 528, "m_flRadialAmount": 556, "m_flRadius": 536, "m_flShakeDuration": 544, "m_flTransitionTime": 548, "m_flTwistAmount": 552, "m_nControlPointForLinearDirection": 564, "m_nPeakStrengthFieldOverride": 532, "m_nRadiusFieldOverride": 540}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RenderVRHapticEvent": {"fields": {"m_flAmplitude": 544, "m_nHand": 528, "m_nOutputField": 536, "m_nOutputHandCP": 532}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunction<PERSON><PERSON><PERSON>"}, "C_OP_RepeatedTriggerChildGroup": {"fields": {"m_bLimitChildCount": 1520, "m_flClusterCooldown": 1168, "m_flClusterRefireTime": 464, "m_flClusterSize": 816, "m_nChildGroupID": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_RestartAfterDuration": {"fields": {"m_bOnlyChildren": 468, "m_flDurationMax": 452, "m_flDurationMin": 448, "m_nCP": 456, "m_nCPField": 460, "m_nChildGroupID": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RopeSpringConstraint": {"fields": {"m_flAdjustmentScale": 1504, "m_flInitialRestingLength": 1512, "m_flMaxDistance": 1152, "m_flMinDistance": 800, "m_flRestLength": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_RotateVector": {"fields": {"m_bNormalize": 484, "m_flRotRateMax": 480, "m_flRotRateMin": 476, "m_flScale": 488, "m_nFieldOutput": 448, "m_vecRotAxisMax": 464, "m_vecRotAxisMin": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_RtEnvCull": {"fields": {"m_RtEnvName": 474, "m_bCullOnMiss": 472, "m_bStickInsteadOfCull": 473, "m_nComponent": 608, "m_nRTEnvCP": 604, "m_vecTestDir": 448, "m_vecTestNormal": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SDFConstraint": {"fields": {"m_flMaxDist": 800, "m_flMinDist": 448, "m_nMaxIterations": 1152}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_SDFForce": {"fields": {"m_flForceScale": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_SDFLighting": {"fields": {"m_vLightingDir": 448, "m_vTint_0": 460, "m_vTint_1": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SelectivelyEnableChildren": {"fields": {"m_bDestroyImmediately": 1513, "m_bPlayEndcapOnStop": 1512, "m_nChildGroupID": 456, "m_nFirstChild": 808, "m_nNumChildrenToEnable": 1160}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SequenceFromModel": {"fields": {"m_flInputMax": 464, "m_flInputMin": 460, "m_flOutputMax": 472, "m_flOutputMin": 468, "m_nControlPointNumber": 448, "m_nFieldOutput": 452, "m_nFieldOutputAnim": 456, "m_nSetMethod": 476}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetAttributeToScalarExpression": {"fields": {"m_flInput1": 456, "m_flInput2": 808, "m_flOutputRemap": 1160, "m_nExpression": 448, "m_nOutputField": 1512, "m_nSetMethod": 1516}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetCPOrientationToDirection": {"fields": {"m_nInputControlPoint": 448, "m_nOutputControlPoint": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetCPOrientationToGroundNormal": {"fields": {"m_CollisionGroupName": 464, "m_bIncludeWater": 616, "m_flInterpRate": 448, "m_flMaxTraceLength": 452, "m_flTolerance": 456, "m_flTraceOffset": 460, "m_nInputCP": 596, "m_nOutputCP": 600, "m_nTraceSet": 592}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetCPOrientationToPointAtCP": {"fields": {"m_b2DOrientation": 816, "m_bAvoidSingularity": 817, "m_bPointAway": 818, "m_flInterpolation": 464, "m_nInputCP": 456, "m_nOutputCP": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetCPtoVector": {"fields": {"m_nCPInput": 448, "m_nFieldOutput": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetChildControlPoints": {"fields": {"m_bReverse": 816, "m_bSetOrientation": 817, "m_nChildGroupID": 448, "m_nFirstControlPoint": 452, "m_nFirstSourcePoint": 464, "m_nNumControlPoints": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetControlPointFieldFromVectorExpression": {"fields": {"m_flOutputRemap": 3776, "m_nExpression": 456, "m_nOutVectorField": 4132, "m_nOutputCP": 4128, "m_vecInput1": 464, "m_vecInput2": 2120}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointFieldToScalarExpression": {"fields": {"m_flInput1": 464, "m_flInput2": 816, "m_flOutputRemap": 1168, "m_nExpression": 456, "m_nOutVectorField": 1524, "m_nOutputCP": 1520}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointFieldToWater": {"fields": {"m_nCPField": 464, "m_nDestCP": 460, "m_nSourceCP": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointFromObjectScale": {"fields": {"m_nCPInput": 456, "m_nCPOutput": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointOrientation": {"fields": {"m_bRandomize": 458, "m_bSetOnce": 459, "m_bUseWorldLocation": 456, "m_flInterpolation": 496, "m_nCP": 460, "m_nHeadLocation": 464, "m_vecRotation": 468, "m_vecRotationB": 480}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointOrientationToCPVelocity": {"fields": {"m_nCPInput": 456, "m_nCPOutput": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointPositionToRandomActiveCP": {"fields": {"m_flResetRate": 472, "m_nCP1": 456, "m_nHeadLocationMax": 464, "m_nHeadLocationMin": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointPositionToTimeOfDayValue": {"fields": {"m_nControlPointNumber": 456, "m_pszTimeOfDayParameter": 460, "m_vecDefaultValue": 588}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointPositions": {"fields": {"m_bOrient": 457, "m_bSetOnce": 458, "m_bUseWorldLocation": 456, "m_nCP1": 460, "m_nCP2": 464, "m_nCP3": 468, "m_nCP4": 472, "m_nHeadLocation": 524, "m_vecCP1Pos": 476, "m_vecCP2Pos": 488, "m_vecCP3Pos": 500, "m_vecCP4Pos": 512}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointRotation": {"fields": {"m_flRotRate": 2112, "m_nCP": 2464, "m_nLocalCP": 2468, "m_vecRotAxis": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointToCPVelocity": {"fields": {"m_bNormalize": 464, "m_nCPField": 472, "m_nCPInput": 456, "m_nCPOutputMag": 468, "m_nCPOutputVel": 460, "m_vecComparisonVelocity": 480}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointToCenter": {"fields": {"m_bUseAvgParticlePos": 472, "m_nCP1": 456, "m_nSetParent": 476, "m_vecCP1Pos": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointToHMD": {"fields": {"m_bOrientToHMD": 472, "m_nCP1": 456, "m_vecCP1Pos": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointToHand": {"fields": {"m_bOrientToHand": 476, "m_nCP1": 456, "m_nHand": 460, "m_vecCP1Pos": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointToImpactPoint": {"fields": {"m_CollisionGroupName": 844, "m_bIncludeWater": 978, "m_bSetToEndpoint": 976, "m_bTraceToClosestSurface": 977, "m_flOffset": 828, "m_flStartOffset": 824, "m_flTraceLength": 472, "m_flUpdateRate": 464, "m_nCPIn": 460, "m_nCPOut": 456, "m_nTraceSet": 972, "m_vecTraceDir": 832}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointToPlayer": {"fields": {"m_bOrientToEyes": 472, "m_nCP1": 456, "m_vecCP1Pos": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointToVectorExpression": {"fields": {"m_bNormalizedOutput": 3776, "m_nExpression": 456, "m_nOutputCP": 460, "m_vInput1": 464, "m_vInput2": 2120}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointToWaterSurface": {"fields": {"m_bAdaptiveThreshold": 832, "m_flRetestRate": 480, "m_nActiveCP": 468, "m_nActiveCPField": 472, "m_nDestCP": 460, "m_nFlowCP": 464, "m_nSourceCP": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetControlPointsToModelParticles": {"fields": {"m_AttachmentName": 576, "m_HitboxSetName": 448, "m_bAttachment": 717, "m_bSkin": 716, "m_nFirstControlPoint": 704, "m_nFirstSourcePoint": 712, "m_nNumControlPoints": 708}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetControlPointsToParticle": {"fields": {"m_bSetOrientation": 464, "m_nChildGroupID": 448, "m_nFirstControlPoint": 452, "m_nFirstSourcePoint": 460, "m_nNumControlPoints": 456, "m_nOrientationMode": 468, "m_nSetParent": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetFloat": {"fields": {"m_InputValue": 448, "m_Lerp": 808, "m_nOutputField": 800, "m_nSetMethod": 804}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetFloatAttributeToVectorExpression": {"fields": {"m_flOutputRemap": 3768, "m_nExpression": 448, "m_nOutputField": 4120, "m_nSetMethod": 4124, "m_vInput1": 456, "m_vInput2": 2112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetFloatCollection": {"fields": {"m_InputValue": 448, "m_Lerp": 808, "m_nOutputField": 800, "m_nSetMethod": 804}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetFromCPSnapshot": {"fields": {"m_bPrev": 1529, "m_bRandom": 464, "m_bReverse": 465, "m_bSubSample": 1528, "m_flInterpolation": 1176, "m_nAttributeToRead": 452, "m_nAttributeToWrite": 456, "m_nControlPointNumber": 448, "m_nLocalSpaceCP": 460, "m_nRandomSeed": 468, "m_nSnapShotIncrement": 824, "m_nSnapShotStartPoint": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetGravityToCP": {"fields": {"m_bSetOrientation": 816, "m_bSetZDown": 817, "m_flScale": 464, "m_nCPInput": 456, "m_nCPOutput": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetParentControlPointsToChildCP": {"fields": {"m_bSetOrientation": 472, "m_nChildControlPoint": 460, "m_nChildGroupID": 456, "m_nFirstSourcePoint": 468, "m_nNumControlPoints": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetPerChildControlPoint": {"fields": {"m_bNumBasedOnParticleCount": 1176, "m_bSetOrientation": 1168, "m_nChildGroupID": 448, "m_nFirstControlPoint": 452, "m_nFirstSourcePoint": 816, "m_nNumControlPoints": 456, "m_nOrientationField": 1172, "m_nParticleIncrement": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetPerChildControlPointFromAttribute": {"fields": {"m_bNumBasedOnParticleCount": 468, "m_nAttributeToRead": 472, "m_nCPField": 476, "m_nChildGroupID": 448, "m_nFirstControlPoint": 452, "m_nFirstSourcePoint": 464, "m_nNumControlPoints": 456, "m_nParticleIncrement": 460}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetRandomControlPointPosition": {"fields": {"m_bOrient": 457, "m_bUseWorldLocation": 456, "m_flInterpolation": 848, "m_flReRandomRate": 472, "m_nCP1": 460, "m_nHeadLocation": 464, "m_vecCPMaxPos": 836, "m_vecCPMinPos": 824}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetSimulationRate": {"fields": {"m_flSimulationScale": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetSingleControlPointPosition": {"fields": {"m_bSetOnce": 456, "m_nCP1": 460, "m_transformInput": 2120, "m_vecCP1Pos": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetToCP": {"fields": {"m_bOffsetLocal": 464, "m_nControlPointNumber": 448, "m_vecOffset": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetUserEvent": {"fields": {"m_flFallingEdge": 1160, "m_flInput": 448, "m_flRisingEdge": 800, "m_nFallingEventType": 1512, "m_nRisingEventType": 1152}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetVariable": {"fields": {"m_floatInput": 2304, "m_positionOffset": 624, "m_rotationOffset": 636, "m_transformInput": 520, "m_variableReference": 456, "m_vecInput": 648}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_SetVec": {"fields": {"m_InputValue": 448, "m_Lerp": 2112, "m_bNormalizedOutput": 2464, "m_nOutputField": 2104, "m_nSetMethod": 2108}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SetVectorAttributeToVectorExpression": {"fields": {"m_bNormalizedOutput": 3776, "m_nExpression": 448, "m_nOutputField": 3768, "m_nSetMethod": 3772, "m_vInput1": 456, "m_vInput2": 2112}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_ShapeMatchingConstraint": {"fields": {"m_flShapeRestorationTime": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_SnapshotRigidSkinToBones": {"fields": {"m_bTransformNormals": 448, "m_bTransformRadii": 449, "m_nControlPointNumber": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_SnapshotSkinToBones": {"fields": {"m_bTransformNormals": 448, "m_bTransformRadii": 449, "m_flJumpThreshold": 464, "m_flLifeTimeFadeEnd": 460, "m_flLifeTimeFadeStart": 456, "m_flPrevPosScale": 468, "m_nControlPointNumber": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_Spin": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CGeneralSpin"}, "C_OP_SpinUpdate": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CSpinUpdateBase"}, "C_OP_SpinYaw": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CGeneralSpin"}, "C_OP_SpringToVectorConstraint": {"fields": {"m_flMaxDistance": 1152, "m_flMinDistance": 800, "m_flRestLength": 448, "m_flRestingLength": 1504, "m_vecAnchorVector": 1856}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_StopAfterCPDuration": {"fields": {"m_bDestroyImmediately": 808, "m_bPlayEndCap": 809, "m_flDuration": 456}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionPreEmission"}, "C_OP_TeleportBeam": {"fields": {"m_flAlpha": 496, "m_flArcMaxDuration": 484, "m_flArcSpeed": 492, "m_flSegmentBreak": 488, "m_nCPColor": 460, "m_nCPExtraArcData": 468, "m_nCPInvalidColor": 464, "m_nCPMisc": 456, "m_nCPPosition": 448, "m_nCPVelocity": 452, "m_vGravity": 472}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_TimeVaryingForce": {"fields": {"m_EndingForce": 484, "m_StartingForce": 468, "m_flEndLerpTime": 480, "m_flStartLerpTime": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_TurbulenceForce": {"fields": {"m_flNoiseCoordScale0": 464, "m_flNoiseCoordScale1": 468, "m_flNoiseCoordScale2": 472, "m_flNoiseCoordScale3": 476, "m_vecNoiseAmount0": 480, "m_vecNoiseAmount1": 492, "m_vecNoiseAmount2": 504, "m_vecNoiseAmount3": 516}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_TwistAroundAxis": {"fields": {"m_TwistAxis": 468, "m_bLocalSpace": 480, "m_fForceAmount": 464, "m_nControlPointNumber": 484}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_UpdateLightSource": {"fields": {"m_flBrightnessScale": 452, "m_flMaximumLightingRadius": 464, "m_flMinimumLightingRadius": 460, "m_flPositionDampingConstant": 468, "m_flRadiusScale": 456, "m_vColorTint": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_VectorFieldSnapshot": {"fields": {"m_bLockToSurface": 2477, "m_bSetVelocity": 2476, "m_flBoundaryDampening": 2472, "m_flGridSpacing": 2480, "m_flInterpolation": 464, "m_nAttributeToWrite": 452, "m_nControlPointNumber": 448, "m_nLocalSpaceCP": 456, "m_vecScale": 816}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_VectorNoise": {"fields": {"m_bAdditive": 480, "m_bOffset": 481, "m_fl4NoiseScale": 476, "m_flNoiseAnimationTimeScale": 484, "m_nFieldOutput": 448, "m_vecOutputMax": 464, "m_vecOutputMin": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_VelocityDecay": {"fields": {"m_flMinVelocity": 448}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_VelocityMatchingForce": {"fields": {"m_bUseAABB": 464, "m_flDirScale": 448, "m_flFacingStrength": 460, "m_flNeighborDistance": 456, "m_flSpdScale": 452, "m_nCPBroadcast": 468}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionOperator"}, "C_OP_WindForce": {"fields": {"m_vForce": 464}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionForce"}, "C_OP_WorldCollideConstraint": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "C_OP_WorldTraceConstraint": {"fields": {"m_CollisionGroupName": 476, "m_bBrushOnly": 605, "m_bDecayBounce": 2040, "m_bIncludeWater": 606, "m_bKillonContact": 2041, "m_bSetNormal": 2048, "m_bWorldOnly": 604, "m_flBounceAmount": 984, "m_flCollisionConfirmationSpeed": 624, "m_flCpMovementTolerance": 612, "m_flMinSpeed": 2044, "m_flRadiusScale": 632, "m_flRandomDirScale": 1688, "m_flRetestRate": 616, "m_flSlideAmount": 1336, "m_flStopSpeed": 2056, "m_flTraceTolerance": 620, "m_nCP": 448, "m_nCollisionMode": 464, "m_nCollisionModeMin": 468, "m_nEntityStickDataField": 2408, "m_nEntityStickNormalField": 2412, "m_nIgnoreCP": 608, "m_nMaxTracesPerFrame": 628, "m_nStickOnCollisionField": 2052, "m_nTraceSet": 472, "m_vecCpOffset": 452}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CParticleFunctionConstraint"}, "CollisionGroupContext_t": {"fields": {"m_nCollisionGroupNumber": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ControlPointReference_t": {"fields": {"m_bOffsetInLocalSpace": 16, "m_controlPointNameString": 0, "m_vOffsetFromControlPoint": 4}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "FloatInputMaterialVariable_t": {"fields": {"m_flInput": 8, "m_strVariable": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "IParticleCollection": {"fields": {}, "metadata": [], "parent": null}, "IParticleEffect": {"fields": {}, "metadata": [], "parent": null}, "IParticleSystemDefinition": {"fields": {}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "MaterialVariable_t": {"fields": {"m_flScale": 12, "m_nVariableField": 8, "m_strVariable": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ModelReference_t": {"fields": {"m_flRelativeProbabilityOfSpawn": 8, "m_model": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "PARTICLE_EHANDLE__": {"fields": {"unused": 0}, "metadata": [], "parent": null}, "PARTICLE_WORLD_HANDLE__": {"fields": {"unused": 0}, "metadata": [], "parent": null}, "ParticleAttributeIndex_t": {"fields": {"m_Value": 0}, "metadata": [], "parent": null}, "ParticleChildrenInfo_t": {"fields": {"m_ChildRef": 0, "m_bDisableChild": 13, "m_bEndCap": 12, "m_flDelay": 8, "m_nDetailLevel": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ParticleControlPointConfiguration_t": {"fields": {"m_drivers": 8, "m_name": 0, "m_previewState": 32}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ParticleControlPointDriver_t": {"fields": {"m_angOffset": 28, "m_attachmentName": 8, "m_entityName": 40, "m_iAttachType": 4, "m_iControlPoint": 0, "m_vecOffset": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ParticleNamedValueConfiguration_t": {"fields": {"m_BoundEntityPath": 32, "m_ConfigName": 0, "m_ConfigValue": 8, "m_iAttachType": 24, "m_strAttachmentName": 48, "m_strEntityScope": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ParticleNamedValueSource_t": {"fields": {"m_DefaultConfig": 16, "m_IsPublic": 8, "m_Name": 0, "m_NamedConfigs": 72, "m_ValueType": 12}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ParticlePreviewBodyGroup_t": {"fields": {"m_bodyGroupName": 0, "m_nValue": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ParticlePreviewState_t": {"fields": {"m_bAnimationNonLooping": 84, "m_bShouldDrawAttachmentNames": 82, "m_bShouldDrawAttachments": 81, "m_bShouldDrawControlPointAxes": 83, "m_bShouldDrawHitboxes": 80, "m_flParticleSimulationRate": 76, "m_flPlaybackSpeed": 72, "m_groundType": 12, "m_hitboxSetName": 32, "m_materialGroupName": 40, "m_nFireParticleOnSequenceFrame": 24, "m_nModSpecificData": 8, "m_previewModel": 0, "m_sequenceName": 16, "m_vecBodyGroups": 48, "m_vecPreviewGravity": 88}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "PointDefinitionWithTimeValues_t": {"fields": {"m_flTimeDuration": 20}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "PointDefinition_t"}, "PointDefinition_t": {"fields": {"m_bLocalCoords": 4, "m_nControlPoint": 0, "m_vOffset": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "RenderProjectedMaterial_t": {"fields": {"m_hMaterial": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "SequenceWeightedList_t": {"fields": {"m_flRelativeWeight": 4, "m_nSequence": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "TextureControls_t": {"fields": {"m_bClampUVs": 2465, "m_bRandomizeOffsets": 2464, "m_flDistortion": 2112, "m_flFinalTextureOffsetU": 704, "m_flFinalTextureOffsetV": 1056, "m_flFinalTextureScaleU": 0, "m_flFinalTextureScaleV": 352, "m_flFinalTextureUVRotation": 1408, "m_flZoomScale": 1760, "m_nPerParticleBlend": 2468, "m_nPerParticleDistortion": 2492, "m_nPerParticleOffsetU": 2476, "m_nPerParticleOffsetV": 2480, "m_nPerParticleRotation": 2484, "m_nPerParticleScale": 2472, "m_nPerParticleZoom": 2488}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "TextureGroup_t": {"fields": {"m_Gradient": 16, "m_TextureControls": 408, "m_bEnabled": 0, "m_bReplaceTextureWithGradient": 1, "m_flTextureBlend": 56, "m_hTexture": 8, "m_nTextureBlendMode": 48, "m_nTextureChannels": 44, "m_nTextureType": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "VecInputMaterialVariable_t": {"fields": {"m_strVariable": 0, "m_vecInput": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}}, "enums": {"AnimationType_t": {"alignment": 4, "members": {"ANIMATION_TYPE_FIT_LIFETIME": 1, "ANIMATION_TYPE_FIXED_RATE": 0, "ANIMATION_TYPE_MANUAL_FRAMES": 2}, "type": "uint32"}, "BBoxVolumeType_t": {"alignment": 4, "members": {"BBOX_DIMENSIONS": 1, "BBOX_MINS_MAXS": 2, "BBOX_VOLUME": 0}, "type": "uint32"}, "BlurFilterType_t": {"alignment": 4, "members": {"BLURFILTER_BOX": 1, "BLURFILTER_GAUSSIAN": 0}, "type": "uint32"}, "ClosestPointTestType_t": {"alignment": 4, "members": {"PARTICLE_CLOSEST_TYPE_BOX": 0, "PARTICLE_CLOSEST_TYPE_CAPSULE": 1, "PARTICLE_CLOSEST_TYPE_HYBRID": 2}, "type": "uint32"}, "Detail2Combo_t": {"alignment": 4, "members": {"DETAIL_2_COMBO_ADD": 1, "DETAIL_2_COMBO_ADD_SELF_ILLUM": 2, "DETAIL_2_COMBO_CROSSFADE": 5, "DETAIL_2_COMBO_MOD2X": 3, "DETAIL_2_COMBO_MUL": 4, "DETAIL_2_COMBO_OFF": 0, "DETAIL_2_COMBO_UNINITIALIZED": -1}, "type": "uint32"}, "DetailCombo_t": {"alignment": 4, "members": {"DETAIL_COMBO_ADD": 1, "DETAIL_COMBO_ADD_SELF_ILLUM": 2, "DETAIL_COMBO_MOD2X": 3, "DETAIL_COMBO_OFF": 0}, "type": "uint32"}, "EventTypeSelection_t": {"alignment": 4, "members": {"PARTICLE_EVENT_TYPE_MASK_COLLISION": 4, "PARTICLE_EVENT_TYPE_MASK_COLLISION_STOPPED": 16, "PARTICLE_EVENT_TYPE_MASK_FIRST_COLLISION": 8, "PARTICLE_EVENT_TYPE_MASK_KILLED": 2, "PARTICLE_EVENT_TYPE_MASK_KILLED_ON_COLLISION": 32, "PARTICLE_EVENT_TYPE_MASK_NONE": 0, "PARTICLE_EVENT_TYPE_MASK_SPAWNED": 1, "PARTICLE_EVENT_TYPE_MASK_USER_1": 64, "PARTICLE_EVENT_TYPE_MASK_USER_2": 128, "PARTICLE_EVENT_TYPE_MASK_USER_3": 256, "PARTICLE_EVENT_TYPE_MASK_USER_4": 512}, "type": "uint32"}, "HitboxLerpType_t": {"alignment": 4, "members": {"HITBOX_LERP_CONSTANT": 1, "HITBOX_LERP_LIFETIME": 0}, "type": "uint32"}, "InheritableBoolType_t": {"alignment": 4, "members": {"INHERITABLE_BOOL_FALSE": 1, "INHERITABLE_BOOL_INHERIT": 0, "INHERITABLE_BOOL_TRUE": 2}, "type": "uint32"}, "MaterialProxyType_t": {"alignment": 4, "members": {"MATERIAL_PROXY_STATUS_EFFECT": 0, "MATERIAL_PROXY_TINT": 1}, "type": "uint32"}, "MissingParentInheritBehavior_t": {"alignment": 4, "members": {"MISSING_PARENT_DO_NOTHING": -1, "MISSING_PARENT_FIND_NEW": 1, "MISSING_PARENT_KILL": 0, "MISSING_PARENT_SAME_INDEX": 2}, "type": "uint32"}, "ModelHitboxType_t": {"alignment": 4, "members": {"MODEL_HITBOX_TYPE_RAW_BONES": 1, "MODEL_HITBOX_TYPE_RENDERBOUNDS": 2, "MODEL_HITBOX_TYPE_SNAPSHOT": 3, "MODEL_HITBOX_TYPE_STANDARD": 0}, "type": "uint32"}, "PFNoiseModifier_t": {"alignment": 4, "members": {"PF_NOISE_MODIFIER_CLUMPS": 2, "PF_NOISE_MODIFIER_LINES": 1, "PF_NOISE_MODIFIER_NONE": 0, "PF_NOISE_MODIFIER_RINGS": 3}, "type": "uint32"}, "PFNoiseTurbulence_t": {"alignment": 4, "members": {"PF_NOISE_TURB_ALTERNATE": 5, "PF_NOISE_TURB_CONTRAST": 4, "PF_NOISE_TURB_FEEDBACK": 2, "PF_NOISE_TURB_HIGHLIGHT": 1, "PF_NOISE_TURB_LOOPY": 3, "PF_NOISE_TURB_NONE": 0}, "type": "uint32"}, "PFNoiseType_t": {"alignment": 4, "members": {"PF_NOISE_TYPE_CURL": 3, "PF_NOISE_TYPE_PERLIN": 0, "PF_NOISE_TYPE_SIMPLEX": 1, "PF_NOISE_TYPE_WORLEY": 2}, "type": "uint32"}, "PFuncVisualizationType_t": {"alignment": 4, "members": {"PFUNC_VISUALIZATION_BOX": 2, "PFUNC_VISUALIZATION_CYLINDER": 6, "PFUNC_VISUALIZATION_LINE": 5, "PFUNC_VISUALIZATION_PLANE": 4, "PFUNC_VISUALIZATION_RING": 3, "PFUNC_VISUALIZATION_SPHERE_SOLID": 1, "PFUNC_VISUALIZATION_SPHERE_WIREFRAME": 0}, "type": "uint32"}, "ParticleAlphaReferenceType_t": {"alignment": 4, "members": {"PARTICLE_ALPHA_REFERENCE_ALPHA_ALPHA": 0, "PARTICLE_ALPHA_REFERENCE_ALPHA_OPAQUE": 2, "PARTICLE_ALPHA_REFERENCE_OPAQUE_ALPHA": 1, "PARTICLE_ALPHA_REFERENCE_OPAQUE_OPAQUE": 3}, "type": "uint32"}, "ParticleAttrBoxFlags_t": {"alignment": 4, "members": {"PARTICLE_ATTR_BOX_FLAGS_NONE": 0, "PARTICLE_ATTR_BOX_FLAGS_WATER": 1}, "type": "uint32"}, "ParticleCollisionMode_t": {"alignment": 4, "members": {"COLLISION_MODE_DISABLED": -1, "COLLISION_MODE_INITIAL_TRACE_DOWN": 0, "COLLISION_MODE_PER_FRAME_PLANESET": 1, "COLLISION_MODE_PER_PARTICLE_TRACE": 3, "COLLISION_MODE_USE_NEAREST_TRACE": 2}, "type": "uint32"}, "ParticleColorBlendMode_t": {"alignment": 4, "members": {"PARTICLEBLEND_DARKEN": 2, "PARTICLEBLEND_DEFAULT": 0, "PARTICLEBLEND_LIGHTEN": 3, "PARTICLEBLEND_MULTIPLY": 4, "PARTICLEBLEND_OVERLAY": 1}, "type": "uint32"}, "ParticleColorBlendType_t": {"alignment": 4, "members": {"PARTICLE_COLOR_BLEND_ADD": 3, "PARTICLE_COLOR_BLEND_AVERAGE": 10, "PARTICLE_COLOR_BLEND_DIVIDE": 2, "PARTICLE_COLOR_BLEND_LUMINANCE": 12, "PARTICLE_COLOR_BLEND_MAX": 7, "PARTICLE_COLOR_BLEND_MIN": 8, "PARTICLE_COLOR_BLEND_MOD2X": 5, "PARTICLE_COLOR_BLEND_MULTIPLY": 0, "PARTICLE_COLOR_BLEND_MULTIPLY2X": 1, "PARTICLE_COLOR_BLEND_NEGATE": 11, "PARTICLE_COLOR_BLEND_REPLACE": 9, "PARTICLE_COLOR_BLEND_SCREEN": 6, "PARTICLE_COLOR_BLEND_SUBTRACT": 4}, "type": "uint32"}, "ParticleControlPointAxis_t": {"alignment": 4, "members": {"PARTICLE_CP_AXIS_NEGATIVE_X": 3, "PARTICLE_CP_AXIS_NEGATIVE_Y": 4, "PARTICLE_CP_AXIS_NEGATIVE_Z": 5, "PARTICLE_CP_AXIS_X": 0, "PARTICLE_CP_AXIS_Y": 1, "PARTICLE_CP_AXIS_Z": 2}, "type": "uint32"}, "ParticleDepthFeatheringMode_t": {"alignment": 4, "members": {"PARTICLE_DEPTH_FEATHERING_OFF": 0, "PARTICLE_DEPTH_FEATHERING_ON_OPTIONAL": 1, "PARTICLE_DEPTH_FEATHERING_ON_REQUIRED": 2}, "type": "uint32"}, "ParticleDetailLevel_t": {"alignment": 4, "members": {"PARTICLEDETAIL_HIGH": 2, "PARTICLEDETAIL_LOW": 0, "PARTICLEDETAIL_MEDIUM": 1, "PARTICLEDETAIL_ULTRA": 3}, "type": "uint32"}, "ParticleDirectionNoiseType_t": {"alignment": 4, "members": {"PARTICLE_DIR_NOISE_CURL": 1, "PARTICLE_DIR_NOISE_PERLIN": 0, "PARTICLE_DIR_NOISE_WORLEY_BASIC": 2}, "type": "uint32"}, "ParticleEndcapMode_t": {"alignment": 4, "members": {"PARTICLE_ENDCAP_ALWAYS_ON": -1, "PARTICLE_ENDCAP_ENDCAP_OFF": 0, "PARTICLE_ENDCAP_ENDCAP_ON": 1}, "type": "uint32"}, "ParticleFalloffFunction_t": {"alignment": 4, "members": {"PARTICLE_FALLOFF_CONSTANT": 0, "PARTICLE_FALLOFF_EXPONENTIAL": 2, "PARTICLE_FALLOFF_LINEAR": 1}, "type": "uint32"}, "ParticleFloatBiasType_t": {"alignment": 4, "members": {"PF_BIAS_TYPE_COUNT": 3, "PF_BIAS_TYPE_EXPONENTIAL": 2, "PF_BIAS_TYPE_GAIN": 1, "PF_BIAS_TYPE_INVALID": -1, "PF_BIAS_TYPE_STANDARD": 0}, "type": "uint32"}, "ParticleFloatInputMode_t": {"alignment": 4, "members": {"PF_INPUT_MODE_CLAMPED": 0, "PF_INPUT_MODE_COUNT": 2, "PF_INPUT_MODE_INVALID": -1, "PF_INPUT_MODE_LOOPED": 1}, "type": "uint32"}, "ParticleFloatMapType_t": {"alignment": 4, "members": {"PF_MAP_TYPE_COUNT": 7, "PF_MAP_TYPE_CURVE": 4, "PF_MAP_TYPE_DIRECT": 0, "PF_MAP_TYPE_INVALID": -1, "PF_MAP_TYPE_MULT": 1, "PF_MAP_TYPE_NOTCHED": 5, "PF_MAP_TYPE_REMAP": 2, "PF_MAP_TYPE_REMAP_BIASED": 3, "PF_MAP_TYPE_ROUND": 6}, "type": "uint32"}, "ParticleFloatRandomMode_t": {"alignment": 4, "members": {"PF_RANDOM_MODE_CONSTANT": 0, "PF_RANDOM_MODE_COUNT": 2, "PF_RANDOM_MODE_INVALID": -1, "PF_RANDOM_MODE_VARYING": 1}, "type": "uint32"}, "ParticleFloatRoundType_t": {"alignment": 4, "members": {"PF_ROUND_TYPE_CEIL": 2, "PF_ROUND_TYPE_COUNT": 3, "PF_ROUND_TYPE_FLOOR": 1, "PF_ROUND_TYPE_INVALID": -1, "PF_ROUND_TYPE_NEAREST": 0}, "type": "uint32"}, "ParticleFloatType_t": {"alignment": 4, "members": {"PF_TYPE_CLOSEST_CAMERA_DISTANCE": 11, "PF_TYPE_COLLECTION_AGE": 4, "PF_TYPE_CONCURRENT_DEF_COUNT": 10, "PF_TYPE_CONTROL_POINT_CHANGE_AGE": 7, "PF_TYPE_CONTROL_POINT_COMPONENT": 6, "PF_TYPE_CONTROL_POINT_SPEED": 8, "PF_TYPE_COUNT": 22, "PF_TYPE_ENDCAP_AGE": 5, "PF_TYPE_INVALID": -1, "PF_TYPE_LITERAL": 0, "PF_TYPE_NAMED_VALUE": 1, "PF_TYPE_PARTICLE_AGE": 15, "PF_TYPE_PARTICLE_AGE_NORMALIZED": 16, "PF_TYPE_PARTICLE_DETAIL_LEVEL": 9, "PF_TYPE_PARTICLE_FLOAT": 17, "PF_TYPE_PARTICLE_NOISE": 14, "PF_TYPE_PARTICLE_NUMBER": 20, "PF_TYPE_PARTICLE_NUMBER_NORMALIZED": 21, "PF_TYPE_PARTICLE_SPEED": 19, "PF_TYPE_PARTICLE_VECTOR_COMPONENT": 18, "PF_TYPE_RANDOM_BIASED": 3, "PF_TYPE_RANDOM_UNIFORM": 2, "PF_TYPE_RENDERER_CAMERA_DISTANCE": 12, "PF_TYPE_RENDERER_CAMERA_DOT_PRODUCT": 13}, "type": "uint32"}, "ParticleFogType_t": {"alignment": 4, "members": {"PARTICLE_FOG_DISABLED": 2, "PARTICLE_FOG_ENABLED": 1, "PARTICLE_FOG_GAME_DEFAULT": 0}, "type": "uint32"}, "ParticleHitboxBiasType_t": {"alignment": 4, "members": {"PARTICLE_HITBOX_BIAS_ENTITY": 0, "PARTICLE_HITBOX_BIAS_HITBOX": 1}, "type": "uint32"}, "ParticleHitboxDataSelection_t": {"alignment": 4, "members": {"PARTICLE_HITBOX_AVERAGE_SPEED": 0, "PARTICLE_HITBOX_COUNT": 1}, "type": "uint32"}, "ParticleImpulseType_t": {"alignment": 4, "members": {"IMPULSE_TYPE_EXPLOSION": 4, "IMPULSE_TYPE_EXPLOSION_UNDERWATER": 8, "IMPULSE_TYPE_GENERIC": 1, "IMPULSE_TYPE_NONE": 0, "IMPULSE_TYPE_PARTICLE_SYSTEM": 16, "IMPULSE_TYPE_ROPE": 2}, "type": "uint32"}, "ParticleLightBehaviorChoiceList_t": {"alignment": 4, "members": {"PARTICLE_LIGHT_BEHAVIOR_FOLLOW_DIRECTION": 0, "PARTICLE_LIGHT_BEHAVIOR_ROPE": 1, "PARTICLE_LIGHT_BEHAVIOR_TRAILS": 2}, "type": "uint32"}, "ParticleLightFogLightingMode_t": {"alignment": 4, "members": {"PARTICLE_LIGHT_FOG_LIGHTING_MODE_DYNAMIC": 2, "PARTICLE_LIGHT_FOG_LIGHTING_MODE_DYNAMIC_NOSHADOWS": 4, "PARTICLE_LIGHT_FOG_LIGHTING_MODE_NONE": 0}, "type": "uint32"}, "ParticleLightTypeChoiceList_t": {"alignment": 4, "members": {"PARTICLE_LIGHT_TYPE_CAPSULE": 3, "PARTICLE_LIGHT_TYPE_FX": 2, "PARTICLE_LIGHT_TYPE_POINT": 0, "PARTICLE_LIGHT_TYPE_SPOT": 1}, "type": "uint32"}, "ParticleLightUnitChoiceList_t": {"alignment": 4, "members": {"PARTICLE_LIGHT_UNIT_CANDELAS": 0, "PARTICLE_LIGHT_UNIT_LUMENS": 1}, "type": "uint32"}, "ParticleLightingQuality_t": {"alignment": 4, "members": {"PARTICLE_LIGHTING_PER_PARTICLE": 0, "PARTICLE_LIGHTING_PER_PIXEL": -1, "PARTICLE_LIGHTING_PER_VERTEX": 1}, "type": "uint32"}, "ParticleLightnintBranchBehavior_t": {"alignment": 4, "members": {"PARTICLE_LIGHTNING_BRANCH_CURRENT_DIR": 0, "PARTICLE_LIGHTNING_BRANCH_ENDPOINT_DIR": 1}, "type": "uint32"}, "ParticleMassMode_t": {"alignment": 4, "members": {"PARTICLE_MASSMODE_RADIUS_CUBED": 0, "PARTICLE_MASSMODE_RADIUS_SQUARED": 2}, "type": "uint32"}, "ParticleModelType_t": {"alignment": 4, "members": {"PM_TYPE_CONTROL_POINT": 3, "PM_TYPE_COUNT": 4, "PM_TYPE_INVALID": 0, "PM_TYPE_NAMED_VALUE_EHANDLE": 2, "PM_TYPE_NAMED_VALUE_MODEL": 1}, "type": "uint32"}, "ParticleOmni2LightTypeChoiceList_t": {"alignment": 4, "members": {"PARTICLE_OMNI2_LIGHT_TYPE_POINT": 0, "PARTICLE_OMNI2_LIGHT_TYPE_SPHERE": 1}, "type": "uint32"}, "ParticleOrientationChoiceList_t": {"alignment": 4, "members": {"PARTICLE_ORIENTATION_ALIGN_TO_PARTICLE_NORMAL": 3, "PARTICLE_ORIENTATION_FULL_3AXIS_ROTATION": 5, "PARTICLE_ORIENTATION_SCREENALIGN_TO_PARTICLE_NORMAL": 4, "PARTICLE_ORIENTATION_SCREEN_ALIGNED": 0, "PARTICLE_ORIENTATION_SCREEN_Z_ALIGNED": 1, "PARTICLE_ORIENTATION_WORLD_Z_ALIGNED": 2}, "type": "uint32"}, "ParticleOrientationSetMode_t": {"alignment": 4, "members": {"PARTICLE_ORIENTATION_SET_FROM_ROTATIONS": 1, "PARTICLE_ORIENTATION_SET_FROM_VELOCITY": 0}, "type": "uint32"}, "ParticleOutputBlendMode_t": {"alignment": 4, "members": {"PARTICLE_OUTPUT_BLEND_MODE_ADD": 1, "PARTICLE_OUTPUT_BLEND_MODE_ALPHA": 0, "PARTICLE_OUTPUT_BLEND_MODE_BLEND_ADD": 2, "PARTICLE_OUTPUT_BLEND_MODE_HALF_BLEND_ADD": 3, "PARTICLE_OUTPUT_BLEND_MODE_LIGHTEN": 6, "PARTICLE_OUTPUT_BLEND_MODE_MOD2X": 5, "PARTICLE_OUTPUT_BLEND_MODE_NEG_HALF_BLEND_ADD": 4}, "type": "uint32"}, "ParticleParentSetMode_t": {"alignment": 4, "members": {"PARTICLE_SET_PARENT_IMMEDIATE": 1, "PARTICLE_SET_PARENT_NO": 0, "PARTICLE_SET_PARENT_ROOT": 1}, "type": "uint32"}, "ParticlePinDistance_t": {"alignment": 4, "members": {"PARTICLE_PIN_COLLECTION_AGE": 10, "PARTICLE_PIN_DISTANCE_CENTER": 5, "PARTICLE_PIN_DISTANCE_CP": 6, "PARTICLE_PIN_DISTANCE_CP_PAIR_BOTH": 8, "PARTICLE_PIN_DISTANCE_CP_PAIR_EITHER": 7, "PARTICLE_PIN_DISTANCE_FARTHEST": 1, "PARTICLE_PIN_DISTANCE_FIRST": 2, "PARTICLE_PIN_DISTANCE_LAST": 3, "PARTICLE_PIN_DISTANCE_NEIGHBOR": 0, "PARTICLE_PIN_DISTANCE_NONE": -1, "PARTICLE_PIN_FLOAT_VALUE": 11, "PARTICLE_PIN_SPEED": 9}, "type": "uint32"}, "ParticlePostProcessPriorityGroup_t": {"alignment": 4, "members": {"PARTICLE_POST_PROCESS_PRIORITY_GAMEPLAY_EFFECT": 2, "PARTICLE_POST_PROCESS_PRIORITY_GAMEPLAY_STATE_HIGH": 4, "PARTICLE_POST_PROCESS_PRIORITY_GAMEPLAY_STATE_LOW": 3, "PARTICLE_POST_PROCESS_PRIORITY_GLOBAL_UI": 5, "PARTICLE_POST_PROCESS_PRIORITY_LEVEL_OVERRIDE": 1, "PARTICLE_POST_PROCESS_PRIORITY_LEVEL_VOLUME": 0}, "type": "uint32"}, "ParticleReplicationMode_t": {"alignment": 4, "members": {"PARTICLE_REPLICATIONMODE_NONE": 0, "PARTICLE_REPLICATIONMODE_REPLICATE_FOR_EACH_PARENT_PARTICLE": 1}, "type": "uint32"}, "ParticleRotationLockType_t": {"alignment": 4, "members": {"PARTICLE_ROTATION_LOCK_NONE": 0, "PARTICLE_ROTATION_LOCK_NORMAL": 2, "PARTICLE_ROTATION_LOCK_ROTATIONS": 1}, "type": "uint32"}, "ParticleSelection_t": {"alignment": 4, "members": {"PARTICLE_SELECTION_FIRST": 0, "PARTICLE_SELECTION_LAST": 1, "PARTICLE_SELECTION_NUMBER": 2}, "type": "uint32"}, "ParticleSequenceCropOverride_t": {"alignment": 4, "members": {"PARTICLE_SEQUENCE_CROP_OVERRIDE_DEFAULT": -1, "PARTICLE_SEQUENCE_CROP_OVERRIDE_FORCE_OFF": 0, "PARTICLE_SEQUENCE_CROP_OVERRIDE_FORCE_ON": 1}, "type": "uint32"}, "ParticleSetMethod_t": {"alignment": 4, "members": {"PARTICLE_SET_ADD_TO_CURRENT_VALUE": 5, "PARTICLE_SET_ADD_TO_INITIAL_VALUE": 2, "PARTICLE_SET_RAMP_CURRENT_VALUE": 3, "PARTICLE_SET_REPLACE_VALUE": 0, "PARTICLE_SET_SCALE_CURRENT_VALUE": 4, "PARTICLE_SET_SCALE_INITIAL_VALUE": 1}, "type": "uint32"}, "ParticleSortingChoiceList_t": {"alignment": 4, "members": {"PARTICLE_SORTING_CREATION_TIME": 1, "PARTICLE_SORTING_NEAREST": 0}, "type": "uint32"}, "ParticleTextureLayerBlendType_t": {"alignment": 4, "members": {"SPRITECARD_TEXTURE_BLEND_ADD": 3, "SPRITECARD_TEXTURE_BLEND_AVERAGE": 5, "SPRITECARD_TEXTURE_BLEND_LUMINANCE": 6, "SPRITECARD_TEXTURE_BLEND_MOD2X": 1, "SPRITECARD_TEXTURE_BLEND_MULTIPLY": 0, "SPRITECARD_TEXTURE_BLEND_REPLACE": 2, "SPRITECARD_TEXTURE_BLEND_SUBTRACT": 4}, "type": "uint32"}, "ParticleTopology_t": {"alignment": 4, "members": {"PARTICLE_TOPOLOGY_CUBES": 4, "PARTICLE_TOPOLOGY_LINES": 1, "PARTICLE_TOPOLOGY_POINTS": 0, "PARTICLE_TOPOLOGY_QUADS": 3, "PARTICLE_TOPOLOGY_TRIS": 2}, "type": "uint32"}, "ParticleTraceMissBehavior_t": {"alignment": 4, "members": {"PARTICLE_TRACE_MISS_BEHAVIOR_KILL": 1, "PARTICLE_TRACE_MISS_BEHAVIOR_NONE": 0, "PARTICLE_TRACE_MISS_BEHAVIOR_TRACE_END": 2}, "type": "uint32"}, "ParticleTraceSet_t": {"alignment": 4, "members": {"PARTICLE_TRACE_SET_ALL": 0, "PARTICLE_TRACE_SET_DYNAMIC": 3, "PARTICLE_TRACE_SET_STATIC": 1, "PARTICLE_TRACE_SET_STATIC_AND_KEYFRAMED": 2}, "type": "uint32"}, "ParticleTransformType_t": {"alignment": 4, "members": {"PT_TYPE_CONTROL_POINT": 2, "PT_TYPE_CONTROL_POINT_RANGE": 3, "PT_TYPE_COUNT": 4, "PT_TYPE_INVALID": 0, "PT_TYPE_NAMED_VALUE": 1}, "type": "uint32"}, "ParticleVRHandChoiceList_t": {"alignment": 4, "members": {"PARTICLE_VRHAND_CP": 2, "PARTICLE_VRHAND_CP_OBJECT": 3, "PARTICLE_VRHAND_LEFT": 0, "PARTICLE_VRHAND_RIGHT": 1}, "type": "uint32"}, "ParticleVecType_t": {"alignment": 4, "members": {"PVEC_TYPE_CLOSEST_CAMERA_POSITION": 16, "PVEC_TYPE_COUNT": 17, "PVEC_TYPE_CP_DELTA": 15, "PVEC_TYPE_CP_RELATIVE_DIR": 7, "PVEC_TYPE_CP_RELATIVE_POSITION": 6, "PVEC_TYPE_CP_RELATIVE_RANDOM_DIR": 8, "PVEC_TYPE_CP_VALUE": 5, "PVEC_TYPE_FLOAT_COMPONENTS": 9, "PVEC_TYPE_FLOAT_INTERP_CLAMPED": 10, "PVEC_TYPE_FLOAT_INTERP_GRADIENT": 12, "PVEC_TYPE_FLOAT_INTERP_OPEN": 11, "PVEC_TYPE_INVALID": -1, "PVEC_TYPE_LITERAL": 0, "PVEC_TYPE_LITERAL_COLOR": 1, "PVEC_TYPE_NAMED_VALUE": 2, "PVEC_TYPE_PARTICLE_VECTOR": 3, "PVEC_TYPE_PARTICLE_VELOCITY": 4, "PVEC_TYPE_RANDOM_UNIFORM": 13, "PVEC_TYPE_RANDOM_UNIFORM_OFFSET": 14}, "type": "uint32"}, "PetGroundType_t": {"alignment": 4, "members": {"PET_GROUND_GRID": 1, "PET_GROUND_NONE": 0, "PET_GROUND_PLANE": 2}, "type": "uint32"}, "RenderModelSubModelFieldType_t": {"alignment": 4, "members": {"SUBMODEL_AS_BODYGROUP_SUBMODEL": 0, "SUBMODEL_AS_MESHGROUP_INDEX": 1, "SUBMODEL_AS_MESHGROUP_MASK": 2, "SUBMODEL_IGNORED_USE_MODEL_DEFAULT_MESHGROUP_MASK": 3}, "type": "uint32"}, "ScalarExpressionType_t": {"alignment": 4, "members": {"SCALAR_EXPRESSION_ADD": 0, "SCALAR_EXPRESSION_DIVIDE": 3, "SCALAR_EXPRESSION_INPUT_1": 4, "SCALAR_EXPRESSION_MAX": 6, "SCALAR_EXPRESSION_MIN": 5, "SCALAR_EXPRESSION_MOD": 7, "SCALAR_EXPRESSION_MUL": 2, "SCALAR_EXPRESSION_SUBTRACT": 1, "SCALAR_EXPRESSION_UNINITIALIZED": -1}, "type": "uint32"}, "SnapshotIndexType_t": {"alignment": 4, "members": {"SNAPSHOT_INDEX_DIRECT": 1, "SNAPSHOT_INDEX_INCREMENT": 0}, "type": "uint32"}, "SpriteCardPerParticleScale_t": {"alignment": 4, "members": {"SPRITECARD_TEXTURE_PP_SCALE_ANIMATION_FRAME": 2, "SPRITECARD_TEXTURE_PP_SCALE_NEG_RANDOM": 11, "SPRITECARD_TEXTURE_PP_SCALE_NEG_RANDOM_TIME": 13, "SPRITECARD_TEXTURE_PP_SCALE_NONE": 0, "SPRITECARD_TEXTURE_PP_SCALE_PARTICLE_AGE": 1, "SPRITECARD_TEXTURE_PP_SCALE_PARTICLE_ALPHA": 5, "SPRITECARD_TEXTURE_PP_SCALE_PITCH": 9, "SPRITECARD_TEXTURE_PP_SCALE_RANDOM": 10, "SPRITECARD_TEXTURE_PP_SCALE_RANDOM_TIME": 12, "SPRITECARD_TEXTURE_PP_SCALE_ROLL": 7, "SPRITECARD_TEXTURE_PP_SCALE_SHADER_EXTRA_DATA1": 3, "SPRITECARD_TEXTURE_PP_SCALE_SHADER_EXTRA_DATA2": 4, "SPRITECARD_TEXTURE_PP_SCALE_SHADER_RADIUS": 6, "SPRITECARD_TEXTURE_PP_SCALE_YAW": 8}, "type": "uint32"}, "SpriteCardShaderType_t": {"alignment": 4, "members": {"SPRITECARD_SHADER_BASE": 0, "SPRITECARD_SHADER_CUSTOM": 1}, "type": "uint32"}, "SpriteCardTextureChannel_t": {"alignment": 4, "members": {"SPRITECARD_TEXTURE_CHANNEL_MIX_A": 2, "SPRITECARD_TEXTURE_CHANNEL_MIX_A_RGBALPHA": 7, "SPRITECARD_TEXTURE_CHANNEL_MIX_B": 11, "SPRITECARD_TEXTURE_CHANNEL_MIX_BALPHA": 14, "SPRITECARD_TEXTURE_CHANNEL_MIX_G": 10, "SPRITECARD_TEXTURE_CHANNEL_MIX_GALPHA": 13, "SPRITECARD_TEXTURE_CHANNEL_MIX_R": 9, "SPRITECARD_TEXTURE_CHANNEL_MIX_RALPHA": 12, "SPRITECARD_TEXTURE_CHANNEL_MIX_RGB": 0, "SPRITECARD_TEXTURE_CHANNEL_MIX_RGBA": 1, "SPRITECARD_TEXTURE_CHANNEL_MIX_RGBA_RGBALPHA": 6, "SPRITECARD_TEXTURE_CHANNEL_MIX_RGB_A": 3, "SPRITECARD_TEXTURE_CHANNEL_MIX_RGB_ALPHAMASK": 4, "SPRITECARD_TEXTURE_CHANNEL_MIX_RGB_A_RGBALPHA": 8, "SPRITECARD_TEXTURE_CHANNEL_MIX_RGB_RGBMASK": 5}, "type": "uint32"}, "SpriteCardTextureType_t": {"alignment": 4, "members": {"SPRITECARD_TEXTURE_1D_COLOR_LOOKUP": 2, "SPRITECARD_TEXTURE_ANIMMOTIONVEC": 6, "SPRITECARD_TEXTURE_DIFFUSE": 0, "SPRITECARD_TEXTURE_NORMALMAP": 5, "SPRITECARD_TEXTURE_SPHERICAL_HARMONICS_A": 7, "SPRITECARD_TEXTURE_SPHERICAL_HARMONICS_B": 8, "SPRITECARD_TEXTURE_SPHERICAL_HARMONICS_C": 9, "SPRITECARD_TEXTURE_UVDISTORTION": 3, "SPRITECARD_TEXTURE_UVDISTORTION_ZOOM": 4, "SPRITECARD_TEXTURE_ZOOM": 1}, "type": "uint32"}, "StandardLightingAttenuationStyle_t": {"alignment": 4, "members": {"LIGHT_STYLE_NEW": 1, "LIGHT_STYLE_OLD": 0}, "type": "uint32"}, "TextureRepetitionMode_t": {"alignment": 4, "members": {"TEXTURE_REPETITION_PARTICLE": 0, "TEXTURE_REPETITION_PATH": 1}, "type": "uint32"}, "VectorExpressionType_t": {"alignment": 4, "members": {"VECTOR_EXPRESSION_ADD": 0, "VECTOR_EXPRESSION_CROSSPRODUCT": 7, "VECTOR_EXPRESSION_DIVIDE": 3, "VECTOR_EXPRESSION_INPUT_1": 4, "VECTOR_EXPRESSION_MAX": 6, "VECTOR_EXPRESSION_MIN": 5, "VECTOR_EXPRESSION_MUL": 2, "VECTOR_EXPRESSION_SUBTRACT": 1, "VECTOR_EXPRESSION_UNINITIALIZED": -1}, "type": "uint32"}, "VectorFloatExpressionType_t": {"alignment": 4, "members": {"VECTOR_FLOAT_EXPRESSION_DISTANCE": 1, "VECTOR_FLOAT_EXPRESSION_DISTANCESQR": 2, "VECTOR_FLOAT_EXPRESSION_DOTPRODUCT": 0, "VECTOR_FLOAT_EXPRESSION_INPUT1_LENGTH": 3, "VECTOR_FLOAT_EXPRESSION_INPUT1_LENGTHSQR": 4, "VECTOR_FLOAT_EXPRESSION_INPUT1_NOISE": 5, "VECTOR_FLOAT_EXPRESSION_UNINITIALIZED": -1}, "type": "uint32"}}}}