{"resourcesystem.dll": {"classes": {"AABB_t": {"fields": {"m_vMaxBounds": 12, "m_vMinBounds": 0}, "metadata": [], "parent": null}, "CFuseProgram": {"fields": {"m_nMaxTempVarsUsed": 72, "m_programBuffer": 0, "m_variablesRead": 24, "m_variablesWritten": 48}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "CFuseSymbolTable": {"fields": {"m_constantMap": 72, "m_constants": 0, "m_functionMap": 136, "m_functions": 48, "m_variableMap": 104, "m_variables": 24}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "ConstantInfo_t": {"fields": {"m_flValue": 12, "m_name": 0, "m_nameToken": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "FourQuaternions": {"fields": {"w": 48, "x": 0, "y": 16, "z": 32}, "metadata": [], "parent": null}, "FunctionInfo_t": {"fields": {"m_bIsPure": 26, "m_nIndex": 24, "m_nParamCount": 20, "m_name": 8, "m_nameToken": 16}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "FuseFunctionIndex_t": {"fields": {"m_Value": 0}, "metadata": [], "parent": null}, "FuseVariableIndex_t": {"fields": {"m_Value": 0}, "metadata": [], "parent": null}, "InfoForResourceTypeCAnimData": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCAnimationGroup": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCCSGOEconItem": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCChoreoSceneFileData": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCCompositeMaterialKit": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCDOTANovelsList": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCDOTAPatchNotesList": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCDotaItemDefinitionResource": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCEntityLump": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCGcExportableExternalData": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCJavaScriptResource": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCModel": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCMorphSetData": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCNmClip": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCNmGraphDefinition": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCNmGraphVariation": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCNmIKRig": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCNmSkeleton": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCPanoramaDynamicImages": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCPanoramaLayout": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCPanoramaStyle": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCPhysAggregateData": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCPostProcessingResource": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCRenderMesh": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCResponseRulesList": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCSequenceGroupData": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCSmartProp": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCTextureBase": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCTypeScriptResource": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCVDataResource": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCVMixListResource": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCVPhysXSurfacePropertiesList": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCVSoundEventScriptList": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCVSoundStackScriptList": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCVoiceContainerBase": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCVoxelVisibility": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeCWorldNode": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeIAnimGraphModelBinding": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeIMaterial2": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeIParticleSnapshot": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeIParticleSystemDefinition": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeIPulseGraphDef": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeIVectorGraphic": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeManifestTestResource_t": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeProceduralTestResource_t": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeTestResource_t": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "InfoForResourceTypeWorld_t": {"fields": {}, "metadata": [{"name": "MResourceTypeForInfoType", "type": "Unknown"}], "parent": null}, "ManifestTestResource_t": {"fields": {"m_child": 8, "m_name": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "PackedAABB_t": {"fields": {"m_nPackedMax": 4, "m_nPackedMin": 0}, "metadata": [], "parent": null}, "TestResource_t": {"fields": {"m_name": 0}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}, "VariableInfo_t": {"fields": {"m_eAccess": 16, "m_eVarType": 15, "m_nIndex": 12, "m_nNumComponents": 14, "m_name": 0, "m_nameToken": 8}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": null}}, "enums": {"FuseVariableAccess_t": {"alignment": 1, "members": {"READ_ONLY": 1, "WRITABLE": 0}, "type": "uint8"}, "FuseVariableType_t": {"alignment": 1, "members": {"BOOL": 1, "FLOAT32": 8, "INT16": 3, "INT32": 4, "INT8": 2, "INVALID": 0, "UINT16": 6, "UINT32": 7, "UINT8": 5}, "type": "uint8"}}}}