#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
敌方阵营实时坐标追踪器
"""

import json
import time
import pymem
from datetime import datetime
import threading
import copy
from offset_manager import get_base_offset, get_entity_offset


class EnemyTrackerV3:
    def __init__(self):
        """初始化敌方追踪器"""
        self.pm = None
        self.client_dll = None
        self.running = False
        self.finish = False
        
        # 加载配置和偏移量
        self.load_config()
        self.load_offsets()
        
        # 游戏状态数据
        self.in_game = False
        self.local_player = None
        self.local_team = None
        self.local_origin = None
        self.entity_list = None
        self.players = []
        self.enemy_players = {}
        
        # 线程同步
        self.data_lock = threading.Lock()
        self.read_interval = 0.002  # 2ms读取间隔
        self.display_interval = 0.2  # 200ms显示间隔（更频繁的坐标更新）
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open("config.json", "r", encoding="utf-8") as f:
                self.config = json.load(f)
        except FileNotFoundError:
            print(" 未找到config.json文件")
            exit(1)
            
    def load_offsets(self):
        """加载内存偏移量"""
        pass
            
    def connect_to_game(self):
        """连接到CS2游戏进程"""
        try:
            self.pm = pymem.Pymem("cs2.exe")
            client_module = pymem.process.module_from_name(self.pm.process_handle, "client.dll")
            self.client_dll = client_module.lpBaseOfDll
            print(f" 成功连接到CS2进程 (client.dll: 0x{self.client_dll:X})")
            return True
        except Exception as e:
            print(f" 连接CS2失败: {e}")
            return False
            
    def game_loop(self):
        """游戏数据读取循环"""
        with self.data_lock:
            try:
                # 获取本地玩家控制器
                self.local_player = self.pm.read_longlong(self.client_dll + get_base_offset("dwLocalPlayerController"))
                if not self.local_player:
                    self.in_game = False
                    return

                # 获取本地玩家Pawn句柄
                local_player_pawn = self.pm.read_int(self.local_player + get_entity_offset("m_hPlayerPawn"))
                if not local_player_pawn:
                    self.in_game = False
                    return

                # 获取实体列表
                self.entity_list = self.pm.read_longlong(self.client_dll + get_base_offset("dwEntityList"))

                # 获取本地玩家Pawn
                local_list_entry2 = self.pm.read_longlong(self.entity_list + 0x8 * ((local_player_pawn & 0x7FFF) >> 9) + 16)
                local_pcs_player_pawn = self.pm.read_longlong(local_list_entry2 + 120 * (local_player_pawn & 0x1FF))
                if not local_pcs_player_pawn:
                    self.in_game = False
                    return

                # 获取本地玩家信息
                self.local_team = self.pm.read_int(self.local_player + get_entity_offset("m_iTeamNum"))
                self.local_origin = (
                    self.pm.read_float(local_pcs_player_pawn + get_entity_offset("m_vOldOrigin")),      # m_vOldOrigin X
                    self.pm.read_float(local_pcs_player_pawn + get_entity_offset("m_vOldOrigin") + 4),  # m_vOldOrigin Y
                    self.pm.read_float(local_pcs_player_pawn + get_entity_offset("m_vOldOrigin") + 8)   # m_vOldOrigin Z
                )
                
                self.in_game = True
                self.scan_all_players()
                
            except Exception:
                self.in_game = False
    
    def scan_all_players(self):
        """扫描所有玩家"""
        players_list = []
        
        try:
            player_index = 0
            while True:
                player_index += 1
                if player_index > 64:
                    break
                    
                try:
                    # cs2-external-esp标准实体获取
                    list_entry = self.pm.read_longlong(self.entity_list + (8 * (player_index & 0x7FFF) >> 9) + 16)
                    if not list_entry:
                        break
                        
                    entity_ptr = self.pm.read_longlong(list_entry + 120 * (player_index & 0x1FF))
                    if not entity_ptr:
                        continue
                    
                    # 获取队伍
                    team = self.pm.read_int(entity_ptr + get_entity_offset("m_iTeamNum"))
                    if team == self.local_team or team not in [2, 3]:
                        continue

                    # 获取玩家Pawn
                    player_pawn_handle = self.pm.read_int(entity_ptr + get_entity_offset("m_hPlayerPawn"))
                    list_entry2 = self.pm.read_longlong(self.entity_list + 0x8 * ((player_pawn_handle & 0x7FFF) >> 9) + 16)
                    if not list_entry2:
                        continue
                        
                    pawn_ptr = self.pm.read_longlong(list_entry2 + 120 * (player_pawn_handle & 0x1FF))
                    if not pawn_ptr:
                        continue
                    
                    # 获取玩家数据
                    health = self.pm.read_int(pawn_ptr + get_entity_offset("m_iHealth"))
                    if health <= 0 or health > 100:
                        continue

                    # 获取坐标
                    origin_x = self.pm.read_float(pawn_ptr + get_entity_offset("m_vOldOrigin"))
                    origin_y = self.pm.read_float(pawn_ptr + get_entity_offset("m_vOldOrigin") + 4)
                    origin_z = self.pm.read_float(pawn_ptr + get_entity_offset("m_vOldOrigin") + 8)

                    if origin_x == 0 and origin_y == 0:
                        continue

                    # 获取玩家名称
                    try:
                        name_buffer = self.pm.read_bytes(entity_ptr + get_entity_offset("m_iszPlayerName"), 64)
                        player_name = name_buffer.decode('utf-8', errors='ignore').rstrip('\x00')
                        if not player_name:
                            player_name = f"Player_{player_index}"
                    except:
                        player_name = f"Player_{player_index}"
                    
                    # 计算距离
                    distance = self.calculate_distance((origin_x, origin_y, origin_z), self.local_origin)
                    
                    # 创建玩家对象
                    player = {
                        'team': team,
                        'health': health,
                        'name': player_name,
                        'origin': (origin_x, origin_y, origin_z),
                        'distance': distance
                    }
                    
                    players_list.append(player)
                    
                except Exception:
                    continue
            
            self.players = players_list
            
        except Exception:
            self.players = []
    
    def calculate_distance(self, pos1, pos2):
        """计算距离"""
        try:
            dx = pos1[0] - pos2[0]
            dy = pos1[1] - pos2[1]
            dz = pos1[2] - pos2[2]
            return round((dx**2 + dy**2 + dz**2) ** 0.5, 1)
        except:
            return 0
    
    def read_thread_loop(self):
        """数据读取线程"""
        while not self.finish:
            try:
                self.game_loop()
                time.sleep(self.read_interval)
            except Exception:
                time.sleep(0.1)
    
    def display_thread_loop(self):
        """显示线程 - 敌方坐标实时显示"""
        print("敌方阵营实时坐标追踪器双线程")
        print(f" 读取频率: {1000/self.read_interval:.0f}Hz | 显示频率: {1/self.display_interval:.1f}Hz")
        print(" 提示: 按 Ctrl+C 退出程序")
        print("-" * 120)

        while not self.finish:
            try:
                current_time = datetime.now().strftime('%H:%M:%S')

                if self.in_game:
                    # 获取敌方玩家数据
                    with self.data_lock:
                        current_players = copy.deepcopy(self.players)

                    enemy_count = len(current_players)
                    team_name = 'T' if self.local_team == 2 else 'CT' if self.local_team == 3 else '未知'

                    if enemy_count > 0:
                        # 构建每个敌人的坐标信息
                        enemies_info = []
                        # 按距离排序，显示最近的敌人
                        sorted_enemies = sorted(current_players, key=lambda x: x['distance'])

                        for enemy in sorted_enemies[:5]:  # 最多显示5个敌人
                            x, y, z = enemy['origin']
                            enemy_team = 'T' if enemy['team'] == 2 else 'CT'
                            enemy_info = f"{enemy['name'][:6]}[{enemy_team}]({x:.0f},{y:.0f},{z:.0f})HP{enemy['health']}"
                            enemies_info.append(enemy_info)

                        enemies_str = " | ".join(enemies_info)

                        # 构建完整的信息行
                        info_line = f"\r {current_time} |  {team_name}队 |  敌方:{enemy_count}人 | {enemies_str}"
                    else:
                        info_line = f"\r {current_time} |  {team_name}队 |  未发现敌方玩家"
                else:
                    info_line = f"\r {current_time} |  等待进入游戏..."

                # 清除当前行并显示新信息
                print(f"\r{' ' * 150}", end='')
                print(info_line, end='', flush=True)

                time.sleep(self.display_interval)
            except Exception:
                time.sleep(0.1)
    
    def start_tracking(self):
        """开始追踪"""
        if not self.connect_to_game():
            return False
            
        self.running = True
        self.finish = False
        
        try:
            # 启动读取线程
            read_thread = threading.Thread(target=self.read_thread_loop, daemon=True)
            read_thread.start()
            
            # 启动显示线程
            display_thread = threading.Thread(target=self.display_thread_loop, daemon=True)
            display_thread.start()
            
            # 主线程等待
            while not self.finish:
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n\n  用户中断，停止追踪")
        finally:
            self.finish = True
            self.running = False


def main():
    """主函数"""
    tracker = EnemyTrackerV3()
    try:
        tracker.start_tracking()
    except Exception as e:
        print(f" 程序运行错误: {e}")
    finally:
        print(" 程序已退出")


if __name__ == "__main__":
    main()
