#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2 前方人物检测程序`
"""

import time
from cs2_common_lib import (
    Vector3, MathUtils,
    CS2GameConnector, CS2MemoryReader, CS2PlayerDataReader
)
from offset_manager import get_base_offset, get_entity_offset


class FrontDetectorImproved:
    """改进的前方人物检测器"""

    def __init__(self):
        # 使用公共游戏连接器和数据读取器
        self.game_connector = CS2GameConnector()
        self.memory_reader = None
        self.player_data_reader = None

        # 兼容性属性（保持原有接口）
        self.pm = None
        self.client_dll = None
        self.entity_list = None
        self.local_player_controller = None
        self.local_player_pawn = None
        self.local_team = None
        self.local_origin = Vector3()

        # 检测参数
        self.detection_distance = 500.0   # 检测距离
        self.angle_tolerance = 45.0       # 角度容差
        self.blocking_distance = 80.0     # 遮挡距离

        # 视角偏移量
        self.view_angles_offset = get_base_offset("dwViewAngles")
    
    def connect_to_game(self):
        """连接到游戏"""
        try:
            # 使用公共游戏连接器
            if not self.game_connector.connect_to_game():
                return False

            # 设置兼容性属性
            self.pm = self.game_connector.pm
            self.client_dll = self.game_connector.client_dll

            # 初始化读取器
            self.memory_reader = CS2MemoryReader(self.game_connector)
            self.player_data_reader = CS2PlayerDataReader(self.game_connector, self.memory_reader)

            return True

        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def update_local_player(self):
        try:
            # 使用公共玩家数据读取器
            success, controller, pawn, team, origin = self.player_data_reader.update_local_player()

            if success:
                # 设置兼容性属性
                self.local_player_controller = controller
                self.local_player_pawn = pawn
                self.local_team = team
                self.local_origin = origin

                # 获取实体列表（兼容性）
                self.entity_list = self.pm.read_longlong(
                    self.client_dll + get_base_offset("dwEntityList")
                )

                return True
            else:
                return False

        except Exception:
            return False
    
    def get_current_yaw(self):
        """获取当前朝向"""
        if self.memory_reader:
            return self.memory_reader.get_current_yaw()
        try:
            return self.pm.read_float(self.client_dll + self.view_angles_offset + 0x4)
        except:
            return 0.0
    
    def get_all_players(self):
        """获取所有玩家（简化版本用于调试）"""
        players = []

        try:
            # 使用teammate_tracker.py的方法
            player_index = 0

            while player_index < 32:  # 限制循环次数
                player_index += 1

                try:
                    # 获取玩家实体
                    list_entry = self.pm.read_longlong(
                        self.entity_list + (8 * (player_index & 0x7FFF) >> 9) + 16
                    )
                    if not list_entry:
                        continue

                    player_entity = self.pm.read_longlong(
                        list_entry + 120 * (player_index & 0x1FF)
                    )
                    if not player_entity:
                        continue

                    # 获取队伍
                    player_team = self.pm.read_int(
                        player_entity + get_entity_offset("m_iTeamNum")
                    )

                    # 只检测队友，跳过敌人和无效队伍
                    if player_team != self.local_team or player_team not in [2, 3]:
                        continue

                    # 获取玩家Pawn
                    player_pawn_handle = self.pm.read_int(
                        player_entity + get_entity_offset("m_hPlayerPawn")
                    )
                    if not player_pawn_handle:
                        continue

                    list_entry2 = self.pm.read_longlong(
                        self.entity_list + 0x8 * ((player_pawn_handle & 0x7FFF) >> 9) + 16
                    )
                    if not list_entry2:
                        continue

                    player_pawn = self.pm.read_longlong(
                        list_entry2 + 120 * (player_pawn_handle & 0x1FF)
                    )
                    if not player_pawn:
                        continue

                    # 跳过自己
                    if player_pawn == self.local_player_pawn:
                        continue

                    # 获取血量
                    health = self.pm.read_int(player_pawn + get_entity_offset("m_iHealth"))
                    if health <= 0 or health > 100:
                        continue

                    # 获取位置
                    origin_x = self.pm.read_float(player_pawn + get_entity_offset("m_vOldOrigin"))
                    origin_y = self.pm.read_float(player_pawn + get_entity_offset("m_vOldOrigin") + 4)
                    origin_z = self.pm.read_float(player_pawn + get_entity_offset("m_vOldOrigin") + 8)

                    # 跳过无效位置
                    if origin_x == 0 and origin_y == 0:
                        continue

                    player_origin = Vector3(origin_x, origin_y, origin_z)

                    # 跳过相同位置（自己）
                    distance_to_self = (self.local_origin - player_origin).length2d()
                    if distance_to_self < 1.0:  # 距离自己太近，可能是自己
                        continue

                    # 简化名字获取
                    player_name = f"Player{player_index}"

                    # 计算距离
                    distance_2d = distance_to_self

                    players.append({
                        'name': player_name,
                        'origin': player_origin,
                        'distance_2d': distance_2d,
                        'health': health,
                        'team': player_team
                    })

                except Exception as e:
                    continue

        except Exception as e:
            print(f"获取玩家失败: {e}")

        return players
    
    def calculate_angle_to_target(self, target_origin):
        """计算到目标的角度"""
        return MathUtils.calculate_angle_to_target(self.local_origin, target_origin)

    def normalize_angle(self, angle):
        """归一化角度"""
        return MathUtils.normalize_angle(angle)
    
    def detect_front_teammates(self):
        """检测前方队友"""
        if not self.update_local_player():
            return []

        my_yaw = self.get_current_yaw()
        all_players = self.get_all_players()

        front_teammates = []

        for player in all_players:
            # 检查距离
            if player['distance_2d'] > self.detection_distance:
                continue

            # 计算角度差
            target_angle = self.calculate_angle_to_target(player['origin'])
            angle_diff = self.normalize_angle(target_angle - my_yaw)

            # 检查是否在前方
            if abs(angle_diff) <= self.angle_tolerance:
                is_blocking = player['distance_2d'] <= self.blocking_distance

                front_teammates.append({
                    'name': player['name'],
                    'origin': player['origin'],
                    'distance_2d': player['distance_2d'],
                    'angle_diff': angle_diff,
                    'is_blocking': is_blocking,
                    'health': player['health']
                })

        # 按距离排序
        front_teammates.sort(key=lambda x: x['distance_2d'])
        return front_teammates

    def is_aiming_at_teammate(self, crosshair_tolerance=5.0):
        """检测准心是否瞄准队友

        Args:
            crosshair_tolerance: 准心容差角度（度），默认5度

        Returns:
            dict: 如果瞄准队友返回队友信息，否则返回None
        """
        if not self.update_local_player():
            return None

        my_yaw = self.get_current_yaw()
        all_players = self.get_all_players()

        # 寻找准心瞄准范围内最近的队友
        closest_teammate = None
        closest_distance = float('inf')

        for player in all_players:
            # 计算到队友的角度
            target_angle = self.calculate_angle_to_target(player['origin'])
            angle_diff = abs(self.normalize_angle(target_angle - my_yaw))

            # 检查是否在准心瞄准范围内
            if angle_diff <= crosshair_tolerance:
                if player['distance_2d'] < closest_distance:
                    closest_distance = player['distance_2d']
                    closest_teammate = {
                        'name': player['name'],
                        'origin': player['origin'],
                        'distance_2d': player['distance_2d'],
                        'angle_diff': self.normalize_angle(target_angle - my_yaw),
                        'health': player['health']
                    }

        return closest_teammate

    def get_teammate_avoidance_direction(self, teammate_info):
        """根据队友位置计算规避方向

        Args:
            teammate_info: 队友信息字典

        Returns:
            str: 推荐的移动方向 ('left', 'right', 'back', 'forward')
        """
        if not teammate_info:
            return None

        angle_diff = teammate_info['angle_diff']

        # 根据队友相对位置选择移动方向
        if angle_diff > 0:
            # 队友在右前方，向左移动
            return 'left'
        else:
            # 队友在左前方，向右移动
            return 'right'

    def check_safe_shooting_angle(self, crosshair_tolerance=3.0):
        """检查当前射击角度是否安全（不会误伤队友）

        Args:
            crosshair_tolerance: 准心安全容差角度（度），默认3度

        Returns:
            tuple: (is_safe: bool, teammate_info: dict or None)
        """
        teammate_info = self.is_aiming_at_teammate(crosshair_tolerance)
        is_safe = teammate_info is None

        return is_safe, teammate_info
    
    def run_monitor(self):
        """运行监控"""
        print("CS2 前方队友检测 - 改进版（调试模式）")
        print(f"检测距离: {self.detection_distance}m, 遮挡距离: {self.blocking_distance}m")
        print("按 Ctrl+C 退出")
        print("-" * 80)

        try:
            while True:
                # 调试信息
                all_players = self.get_all_players()
                front_teammates = self.detect_front_teammates()

                if front_teammates:
                    closest = front_teammates[0]
                    status = "遮挡" if closest['is_blocking'] else "安全"

                    info = (f"\r位置:({self.local_origin.x:.0f},{self.local_origin.y:.0f}) "
                           f"朝向:{self.get_current_yaw():.0f}° "
                           f"前方队友:{closest['name']} "
                           f"距离:{closest['distance_2d']:.1f}m "
                           f"角度:{closest['angle_diff']:.1f}° "
                           f"血量:{closest['health']} "
                           f"状态:{status}")

                    if len(front_teammates) > 1:
                        info += f" (+{len(front_teammates)-1}个)"
                else:
                    # 显示调试信息
                    total_players = len(all_players)
                    info = (f"\r位置:({self.local_origin.x:.0f},{self.local_origin.y:.0f}) "
                           f"朝向:{self.get_current_yaw():.0f}° "
                           f"队伍:{self.local_team} "
                           f"检测到{total_players}个队友 "
                           f"前方无队友")

                print(info.ljust(120), end="", flush=True)
                time.sleep(0.2)

        except KeyboardInterrupt:
            print("\n监控结束")


def main():
    """主函数"""
    print("CS2 前方队友检测程序 - 改进版")
    print("=" * 50)
    
    detector = FrontDetectorImproved()
    
    if not detector.connect_to_game():
        return
    
    print("开始监控...")
    detector.run_monitor()
    print("程序结束")


if __name__ == "__main__":
    main()
