﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="memory\memory.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="classes\config.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="classes\auto_updater.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="hacks\reader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="classes\utils.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="memory\memory.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="classes\vector.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="hacks\hack.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="classes\globals.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="classes\render.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="classes\json.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="classes\config.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="classes\auto_updater.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="memory\handle_hijack.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="hacks\reader.hpp">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="classes\utils.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>