#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公共方法
"""

import json
import time
import math
import pymem
import psutil
import win32con
import win32gui
import win32process
from offset_manager import get_base_offset, get_entity_offset


class Vector3:
    """统一的3D向量类"""
    
    def __init__(self, x=0.0, y=0.0, z=0.0):
        self.x = float(x)
        self.y = float(y)
        self.z = float(z)
    
    def __add__(self, other):
        return Vector3(self.x + other.x, self.y + other.y, self.z + other.z)
    
    def __sub__(self, other):
        return Vector3(self.x - other.x, self.y - other.y, self.z - other.z)
    
    def __mul__(self, scalar):
        return Vector3(self.x * scalar, self.y * scalar, self.z * scalar)
    
    def length_2d(self):
        """计算2D长度"""
        return math.sqrt(self.x * self.x + self.y * self.y)
    
    def length2d(self):
        """计算2D长度"""
        return self.length_2d()
    
    def length(self):
        """计算3D长度"""
        return math.sqrt(self.x * self.x + self.y * self.y + self.z * self.z)


class QAngle:
    """统一的角度类"""
    
    def __init__(self, pitch=0.0, yaw=0.0, roll=0.0):
        self.pitch = float(pitch)  # x
        self.yaw = float(yaw)      # y
        self.roll = float(roll)    # z
    
    def __sub__(self, other):
        return QAngle(self.pitch - other.pitch, self.yaw - other.yaw, self.roll - other.roll)
    
    def __mul__(self, scalar):
        return QAngle(self.pitch * scalar, self.yaw * scalar, self.roll * scalar)
    
    def length_2d(self):
        """计算2D角度长度"""
        return math.sqrt(self.pitch * self.pitch + self.yaw * self.yaw)
    
    def normalize(self):
        """归一化角度"""
        # pitch 角度限制在 [-89, 89] 度之间
        self.pitch = max(-89.0, min(89.0, self.pitch))

        # yaw 角度归一化到 [-180, 180]
        self.yaw = self._normalize_angle(self.yaw)

        # roll 通常不需要处理
        self.roll = self._normalize_angle(self.roll)
        return self

    def _normalize_angle(self, angle):
        """归一化单个角度到[-180, 180]"""
        while angle > 180:
            angle -= 360
        while angle < -180:
            angle += 360
        return angle


class MathUtils:
    """数学工具类"""
    
    @staticmethod
    def calculate_distance(pos1, pos2):
        """计算两点间距离"""
        try:
            dx = pos1[0] - pos2[0]
            dy = pos1[1] - pos2[1]
            dz = pos1[2] - pos2[2]
            return round((dx**2 + dy**2 + dz**2) ** 0.5, 1)
        except:
            return 0
    
    @staticmethod
    def calculate_angle_to_target(from_pos, to_pos):
        """计算到目标的角度"""
        dx = to_pos.x - from_pos.x
        dy = to_pos.y - from_pos.y
        return math.degrees(math.atan2(dy, dx))
    
    @staticmethod
    def normalize_angle(angle):
        """归一化角度"""
        while angle > 180:
            angle -= 360
        while angle < -180:
            angle += 360
        return angle
    
    @staticmethod
    def calculate_view_angle(from_pos, to_pos):
        """计算视角"""
        d = to_pos - from_pos

        # 计算水平角度
        yaw = math.atan2(d.y, d.x) * 180 / math.pi

        # 计算垂直角度
        dist_2d = d.length_2d()
        if dist_2d > 0:
            pitch = -math.atan2(d.z, dist_2d) * 180 / math.pi
        else:
            pitch = 0.0

        # 限制角度范围
        pitch = max(-89.0, min(89.0, pitch))

        return QAngle(pitch, yaw, 0.0)


class CS2ProcessManager:
    """CS2进程管理器"""
    
    @staticmethod
    def get_cs2_pid():
        """获取CS2进程ID"""
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'].lower() == 'cs2.exe':
                    return proc.info['pid']
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        raise RuntimeError("未找到 cs2.exe 进程")
    
    @staticmethod
    def get_cs_windows():
        """获取CS2游戏窗口"""
        cs_windows = []
        cs2_pids = []
        
        # 获取所有cs2.exe进程ID
        for proc in psutil.process_iter(['pid', 'name']):
            try:
                if proc.info['name'].lower() == 'cs2.exe':
                    cs2_pids.append(proc.info['pid'])
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        if not cs2_pids:
            print("未找到运行中的cs2.exe进程")
            return None

        def enum_windows_callback(hwnd, _):
            if not win32gui.IsWindowVisible(hwnd):
                return
            _, pid = win32process.GetWindowThreadProcessId(hwnd)
            if pid not in cs2_pids:
                return
            window_title = win32gui.GetWindowText(hwnd)
            if not window_title.strip():
                return
            style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
            if not (style & win32con.WS_OVERLAPPEDWINDOW):
                return
            cs_windows.append((hwnd, window_title))

        win32gui.EnumWindows(enum_windows_callback, None)
        cs_windows.sort(key=lambda x: (win32gui.GetWindowRect(x[0])[1], win32gui.GetWindowRect(x[0])[0]))
        return cs_windows
    
    @staticmethod
    def bring_cs2_to_foreground(pid):
        """将窗口设置为前台"""
        def enum_window_callback(hwnd, result):
            _, found_pid = win32process.GetWindowThreadProcessId(hwnd)
            if found_pid == pid and win32gui.IsWindowVisible(hwnd):
                result.append(hwnd)

        hwnds = []
        win32gui.EnumWindows(enum_window_callback, hwnds)
        if not hwnds:
            raise RuntimeError("未找到 cs2.exe 窗口")

        hwnd = hwnds[0]
        try:
            win32gui.ShowWindow(hwnd, win32con.SW_RESTORE)
            win32gui.SetForegroundWindow(hwnd)
            time.sleep(0.1)
        except:
            pass  # 忽略窗口设置错误


class CS2ConfigManager:
    """CS2配置管理器"""
    
    @staticmethod
    def load_config():
        """加载config.json"""
        try:
            with open("config.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except FileNotFoundError:
            print("未找到config.json文件")
            return {}
    
    @staticmethod
    def load_offsets():
        """加载offsets.json"""
        try:
            with open("offsets.json", "r", encoding="utf-8") as f:
                return json.load(f)["client.dll"]
        except FileNotFoundError:
            print("未找到offsets.json文件")
            return {}
    
    @staticmethod
    def load_client_dll_offsets():
        """加载client_dll.json"""
        try:
            with open("client_dll.json", "r", encoding="utf-8") as f:
                return json.load(f)["client.dll"]["classes"]
        except FileNotFoundError:
            print(" 未找到client_dll.json文件")
            return {}
    
    @staticmethod
    def load_paths_data():
        """加载paths.json"""
        try:
            with open("paths.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except FileNotFoundError:
            print("未找到paths.json文件")
            return {}


class CS2GameConnector:
    """CS2游戏连接器"""
    
    def __init__(self):
        self.pm = None
        self.client_dll = None
        self.process_handle = None
        self.connected = False
    
    def connect_to_game(self):
        """连接到CS2游戏"""
        try:
            # 获取CS2进程
            pid = CS2ProcessManager.get_cs2_pid()
            CS2ProcessManager.bring_cs2_to_foreground(pid)
            
            # 连接进程
            self.pm = pymem.Pymem()
            self.pm.open_process_from_id(pid)
            
            # 获取client.dll基址
            client_module = pymem.process.module_from_name(self.pm.process_handle, "client.dll")
            self.client_dll = client_module.lpBaseOfDll
            self.process_handle = self.pm.process_handle
            
            self.connected = True
            print(f" 成功连接到CS2进程 (PID: {pid})")
            return True
            
        except Exception as e:
            print(f" 连接游戏失败: {e}")
            self.connected = False
            return False
    
    def connect_via_windows(self):
        """通过窗口连接到游戏"""
        try:
            cs_windows = CS2ProcessManager.get_cs_windows()
            if not cs_windows:
                print("未找到CS2窗口")
                return False
                
            first_cs_windows_hwnd = cs_windows[0][0]
            win32gui.ShowWindow(first_cs_windows_hwnd, win32con.SW_RESTORE)
            win32gui.SetForegroundWindow(first_cs_windows_hwnd)
            
            _, pid = win32process.GetWindowThreadProcessId(first_cs_windows_hwnd)
            self.pm = pymem.Pymem()
            self.pm.open_process_from_id(pid)
            
            self.client_dll = pymem.process.module_from_name(
                self.pm.process_handle, "client.dll"
            ).lpBaseOfDll
            
            self.process_handle = self.pm.process_handle
            self.connected = True
            
            print(f"成功连接到CS2进程 (PID: {pid})")
            return True
            
        except Exception as e:
            print(f"连接失败: {e}")
            self.connected = False
            return False


class CS2MemoryReader:
    """CS2内存读取器"""

    def __init__(self, game_connector):
        self.connector = game_connector

    def get_local_player_pawn(self):
        """获取本地玩家Pawn指针"""
        try:
            offsets = CS2ConfigManager.load_offsets()
            return self.connector.pm.read_longlong(self.connector.client_dll + offsets["dwLocalPlayerPawn"])
        except:
            return 0

    def get_view_angle(self):
        """获取当前视角"""
        try:
            offsets = CS2ConfigManager.load_offsets()
            pitch = pymem.memory.read_float(self.connector.process_handle, self.connector.client_dll + offsets["dwViewAngles"])
            yaw = pymem.memory.read_float(self.connector.process_handle, self.connector.client_dll + offsets["dwViewAngles"] + 0x4)
            return QAngle(pitch, yaw, 0.0)
        except:
            return QAngle()

    def write_view_angle(self, angle):
        """写入视角"""
        try:
            offsets = CS2ConfigManager.load_offsets()
            pymem.memory.write_float(self.connector.process_handle, self.connector.client_dll + offsets["dwViewAngles"], angle.pitch)
            pymem.memory.write_float(self.connector.process_handle, self.connector.client_dll + offsets["dwViewAngles"] + 0x4, angle.yaw)
            return True
        except:
            return False

    def get_current_yaw(self):
        """获取当前朝向"""
        try:
            view_angles_offset = get_base_offset("dwViewAngles")
            return self.connector.pm.read_float(self.connector.client_dll + view_angles_offset + 0x4)
        except:
            return 0.0

    def read_player_position(self, player_ptr, config):
        """读取玩家位置"""
        try:
            x_offset = int(config["x_offset"], 16)
            y_offset = int(config["y_offset"], 16)
            z_offset = int(config["z_offset"], 16)

            x = self.connector.pm.read_float(player_ptr + x_offset)
            y = self.connector.pm.read_float(player_ptr + y_offset)
            z = self.connector.pm.read_float(player_ptr + z_offset)

            return Vector3(x, y, z)
        except:
            return Vector3()

    def get_old_origin(self, pawn_ptr):
        """获取玩家位置"""
        try:
            x = self.connector.pm.read_float(pawn_ptr + get_entity_offset("m_vOldOrigin"))
            y = self.connector.pm.read_float(pawn_ptr + get_entity_offset("m_vOldOrigin") + 4)
            z = self.connector.pm.read_float(pawn_ptr + get_entity_offset("m_vOldOrigin") + 8)
            return Vector3(x, y, z)
        except:
            return Vector3()

    def get_view_offset(self, pawn_ptr):
        """获取视角偏移"""
        try:
            x = self.connector.pm.read_float(pawn_ptr + get_entity_offset("m_vecViewOffset"))
            y = self.connector.pm.read_float(pawn_ptr + get_entity_offset("m_vecViewOffset") + 4)
            z = self.connector.pm.read_float(pawn_ptr + get_entity_offset("m_vecViewOffset") + 8)
            return Vector3(x, y, z)
        except:
            return Vector3()

    def get_team_num(self, player_ptr):
        """获取玩家队伍编号"""
        try:
            return self.connector.pm.read_int(player_ptr + get_entity_offset("m_iTeamNum"))
        except:
            return 0

    def get_health(self, player_ptr):
        """获取玩家血量"""
        try:
            return self.connector.pm.read_int(player_ptr + get_entity_offset("m_iHealth"))
        except:
            return 0

    def get_life_state(self, player_ptr):
        """获取玩家生命状态"""
        try:
            return self.connector.pm.read_int(player_ptr + get_entity_offset("m_lifeState"))
        except:
            return 1

    def get_aim_punch_angle(self, pawn_ptr):
        """获取后坐力角度"""
        try:
            x = self.connector.pm.read_float(pawn_ptr + get_entity_offset("m_aimPunchAngle"))
            y = self.connector.pm.read_float(pawn_ptr + get_entity_offset("m_aimPunchAngle") + 4)
            z = self.connector.pm.read_float(pawn_ptr + get_entity_offset("m_aimPunchAngle") + 8)
            return QAngle(x, y, z)
        except:
            return QAngle()


class CS2PlayerDataReader:
    """CS2玩家数据读取器"""

    def __init__(self, game_connector, memory_reader):
        self.connector = game_connector
        self.memory_reader = memory_reader

    def update_local_player(self):
        """更新本地玩家信息"""
        try:
            # 获取本地玩家控制器
            local_player_controller = self.connector.pm.read_longlong(
                self.connector.client_dll + get_base_offset("dwLocalPlayerController")
            )
            if not local_player_controller:
                return False, None, None, None, None

            # 获取本地玩家Pawn句柄
            local_player_pawn_handle = self.connector.pm.read_int(
                local_player_controller + get_entity_offset("m_hPlayerPawn")
            )
            if not local_player_pawn_handle:
                return False, None, None, None, None

            # 获取实体列表
            entity_list = self.connector.pm.read_longlong(
                self.connector.client_dll + get_base_offset("dwEntityList")
            )

            # 获取本地玩家Pawn
            local_list_entry2 = self.connector.pm.read_longlong(
                entity_list + 0x8 * ((local_player_pawn_handle & 0x7FFF) >> 9) + 16
            )
            local_player_pawn = self.connector.pm.read_longlong(
                local_list_entry2 + 120 * (local_player_pawn_handle & 0x1FF)
            )
            if not local_player_pawn:
                return False, None, None, None, None

            # 获取本地玩家队伍和位置
            local_team = self.connector.pm.read_int(
                local_player_controller + get_entity_offset("m_iTeamNum")
            )

            origin_x = self.connector.pm.read_float(local_player_pawn + get_entity_offset("m_vOldOrigin"))
            origin_y = self.connector.pm.read_float(local_player_pawn + get_entity_offset("m_vOldOrigin") + 4)
            origin_z = self.connector.pm.read_float(local_player_pawn + get_entity_offset("m_vOldOrigin") + 8)
            local_origin = Vector3(origin_x, origin_y, origin_z)

            return True, local_player_controller, local_player_pawn, local_team, local_origin

        except Exception:
            return False, None, None, None, None


class CS2Utils:
    """CS2工具类"""

    @staticmethod
    def is_at_position(my_x, my_y, target, tolerance=1):
        """检查是否在指定位置"""
        return (abs(my_x - target['x']) <= tolerance and
                abs(my_y - target['y']) <= tolerance)

    @staticmethod
    def safe_execute(func, default_value=None, debug=False):
        """安全执行函数的装饰器"""
        try:
            return func()
        except Exception as e:
            if debug:
                print(f"执行失败: {e}")
            return default_value

    @staticmethod
    def validate_coordinates(x, y, z, max_value=100000):
        """验证坐标数据合理性"""
        if abs(x) > max_value or abs(y) > max_value or abs(z) > max_value:
            return False, f"坐标数据异常: ({x}, {y}, {z})"
        return True, None

    @staticmethod
    def validate_angle(angle, max_value=360):
        """验证角度数据合理性"""
        if abs(angle) > max_value:
            return False, f"视角数据异常: {angle}°"
        return True, None
