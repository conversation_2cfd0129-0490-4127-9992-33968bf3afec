#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2 自动瞄准测试脚本
"""

import time
import keyboard
from cs2_auto_aim import CS2AutoAim


def main():
    """主函数"""
    print(" CS2 自动瞄准测试工具")
    print("=" * 60)
    print(" 使用说明:")
    print("1. 确保CS2游戏正在运行")
    print("2. 按 F1 键切换自动瞄准开关")
    print("3. 按 ESC 键退出程序")
    print("4. 自动瞄准开启时，准心会自动瞄向最近的敌人")
    print("=" * 60)
    
    # 创建自动瞄准实例
    auto_aim = CS2AutoAim()
    
    # 连接到游戏
    print(" 正在连接到CS2游戏...")
    if not auto_aim.connect():
        print(" 连接失败，请确保CS2正在运行")
        input("按回车键退出...")
        return
    
    print(" 成功连接到CS2游戏")
    print("\n 控制说明:")
    print("  F1 - 切换自动瞄准开关")
    print("  F2 - 切换调试模式")
    print("  ESC - 退出程序")
    print("\n 注意：请在训练模式或离线模式下测试！")

    # 主循环
    print("\n 自动瞄准系统已启动...")
    last_status_time = 0
    last_enemy_count = -1
    
    try:
        while True:
            # 检查按键
            if keyboard.is_pressed('f1'):
                auto_aim.toggle_aim()
                time.sleep(0.3)  # 防止重复触发

            if keyboard.is_pressed('f2'):
                auto_aim.toggle_debug()
                time.sleep(0.3)  # 防止重复触发

            if keyboard.is_pressed('esc'):
                print("\n 用户退出程序")
                break

            # 更新自动瞄准
            auto_aim.update()

            # 显示敌人数量变化
            if auto_aim.alive_enemies_count != last_enemy_count:
                if auto_aim.alive_enemies_count == 0:
                    print(" 所有敌人已被消灭，自动瞄准暂停")
                else:
                    print(f" 检测到 {auto_aim.alive_enemies_count} 个存活敌人")
                last_enemy_count = auto_aim.alive_enemies_count

            # 定期显示状态
            current_time = time.time()
            if current_time - last_status_time > 10.0:
                aim_status = "开启" if auto_aim.enabled else "关闭"
                debug_status = "开启" if auto_aim.debug_mode else "关闭"
                print(f" 状态 - 自瞄: {aim_status}, 调试: {debug_status}, 敌人: {auto_aim.alive_enemies_count}")
                last_status_time = current_time

            # 短暂休眠以减少CPU使用
            time.sleep(auto_aim.update_interval)
            
    except KeyboardInterrupt:
        print("\n 程序被中断")
    except Exception as e:
        print(f"\n 程序错误: {e}")
    
    print(" 程序结束")


if __name__ == "__main__":
    main()
