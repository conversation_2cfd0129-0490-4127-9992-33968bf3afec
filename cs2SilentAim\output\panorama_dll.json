{"panorama.dll": {"classes": {}, "enums": {"ELayoutNodeType": {"alignment": 4, "members": {"INCLUDE": 5, "PANEL": 7, "PANEL_ATTRIBUTE": 8, "PANEL_ATTRIBUTE_VALUE": 9, "REFERENCE_COMPILED": 11, "REFERENCE_CONTENT": 10, "REFERENCE_PASSTHROUGH": 12, "ROOT": 0, "SCRIPTS": 3, "SCRIPT_BODY": 2, "SNIPPET": 6, "SNIPPETS": 4, "STYLES": 1}, "type": "uint32"}, "EStyleNodeType": {"alignment": 4, "members": {"COMPILER_CONDITIONAL": 15, "DEFINE": 3, "EXPRESSION": 1, "EXPRESSION_CONCAT": 11, "EXPRESSION_TEXT": 9, "EXPRESSION_URL": 10, "IMPORT": 4, "KEYFRAMES": 5, "KEYFRAME_SELECTOR": 6, "PROPERTY": 2, "REFERENCE_COMPILED": 13, "REFERENCE_CONTENT": 12, "REFERENCE_PASSTHROUGH": 14, "ROOT": 0, "STYLE_SELECTOR": 7, "WHITESPACE": 8}, "type": "uint32"}}}}