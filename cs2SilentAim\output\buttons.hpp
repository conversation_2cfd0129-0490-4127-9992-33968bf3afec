// Generated using https://github.com/a2x/cs2-dumper
// 2025-05-16 14:03:14.936842900 UTC

#pragma once

#include <cstddef>

namespace cs2_dumper {
    // Module: client.dll
    namespace buttons {
        constexpr std::ptrdiff_t attack = 0x18448F0;
        constexpr std::ptrdiff_t attack2 = 0x1844980;
        constexpr std::ptrdiff_t back = 0x1844BC0;
        constexpr std::ptrdiff_t duck = 0x1844E90;
        constexpr std::ptrdiff_t forward = 0x1844B30;
        constexpr std::ptrdiff_t jump = 0x1844E00;
        constexpr std::ptrdiff_t left = 0x1844C50;
        constexpr std::ptrdiff_t lookatweapon = 0x1A6AE20;
        constexpr std::ptrdiff_t reload = 0x1844860;
        constexpr std::ptrdiff_t right = 0x1844CE0;
        constexpr std::ptrdiff_t showscores = 0x1A6AD00;
        constexpr std::ptrdiff_t sprint = 0x18447D0;
        constexpr std::ptrdiff_t turnleft = 0x1844A10;
        constexpr std::ptrdiff_t turnright = 0x1844AA0;
        constexpr std::ptrdiff_t use = 0x1844D70;
        constexpr std::ptrdiff_t zoom = 0x1A6AD90;
    }
}
